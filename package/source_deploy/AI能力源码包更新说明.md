## 直接使用源码包更新(仅在依赖库未更新前提下有效)；

1. 直接解压`ai-model-serve-xxxx.zip`包；
    ```
    unzip ai-model-serve-xxxx.zip
    ```
2. 停止AI能力服务；
    ```
    systemctl stop ai-model-serve
    ```
2. 将原安装目录压缩为zip包以备份；
    ```
    cd /usr/local/data/src
    zip -r ai-model-serve.bak.zip ai-model-serve
    ```
2. 将解压后的文件全部复制到`usr/local/data/src/ai-model-serve`目录下，覆盖原有文件；
    ```
    cp -r ai-model-serve-xxxx/* usr/local/data/src/ai-model-serve/
    ```
3. 将数据库文件备份；
    ```
    cp usr/local/data/src/ai-model-serve/serverrun.db usr/local/data/src/ai-model-serve/serverrun.db.bak
    ```
4. 重启AI能力服务；
    ```
    systemctl restart ai-model-serve
    ```