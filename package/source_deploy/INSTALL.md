# 安装流程

## 1. 安装<PERSON><PERSON><PERSON>-Server-v10-sp1-aarch64系统
> <PERSON><PERSON><PERSON>-Server-10-SP1-Release-Build20-20210518-aarch64.iso

## 2. 基础依赖

### 2.1 基础依赖(如无网络可选2.1.1离线安装)
```
yum install zlib-devel bzip2-devel openssl-devel ncurses-devel sqlite-devel readline-devel tk-devel gdbm-devel db4-devel libpcap-devel xz-devel libffi-devel
```

#### 2.1.1 离线安装
```
cd yum_offline

rpm -Uvh --force --nodeps *.rpm
```

安装kernel升级与开发工具
```
cd kernel_4.19.90-23.54_yum_offline

rpm -Uvh --force --nodeps *.rpm
```

安装系统默认的python3（CANN安装依赖python3.7）
```
cd python37_yum_offline

rpm -Uvh --force --nodeps *.rpm
```

安装ffmpeg
```
cd ffmpeg_yum_offline

rpm -Uvh --force --nodeps *.rpm
```

### 2.2 安装Python（智能依赖python3.10）
#### 2.2.1 编译安装
```
wget https://www.python.org/ftp/python/3.10.12/Python-3.10.12.tgz

tar -zxvf Python-3.10.12.tgz

cd Python-3.10.12

./configure --enable-shared --prefix=/usr/local/python3

make -j8 && make altinstall
```
#### 2.2.2 使用预编译包
```
unzip python310_kylin_v10_sp1_aarch64.zip
# 将解压后得到的python3文件夹复制到/usr/local/文件夹内
cp -r python3 /usr/local/

# 将所有so文件复制至 /usr/lib64 下一份
cp -r /usr/local/python3/lib/*.so /usr/lib64/
```

### 2.3 建立python310和pip310的软链接
```
ln -s /usr/local/python3/bin/python3.10 /usr/bin/python310
ln -s /usr/local/python3/bin/pip3.10 /usr/bin/pip310
```

## 3. 安装NPU固件与驱动(存在华为昇腾NPU设备时需要)

> 以下安装操作需要以root用户进行

### 3.1 创建HwHiAiUser用户和用户属组
```
groupadd HwHiAiUser
useradd -g HwHiAiUser -d /home/<USER>/bin/bash
```
### 3.2 安装驱动和固件

#### 3.2.1 安装依赖(如选择2.1.1离线安装依赖可跳过此步)
```
# 更新yum源
yum makecache
# 安装依赖
yum install -y make dkms gcc kernel-headers-$(uname -r) kernel-devel-$(uname -r)
```

#### 3.2.2 安装驱动
```
chmod +x Ascend-hdk-<chip_type>-npu-driver_<version>_linux-<arch>.run

./Ascend-hdk-<chip_type>-npu-driver_<version>_linux-<arch>.run --full --install-for-all
```
若系统出现如下关键回显信息，则表示驱动安装成功。
> Driver package installed successfully!

#### 3.2.3 安装固件
```
chmod +x Ascend-hdk-<chip_type>-npu-firmware_<version>.run

./Ascend-hdk-<chip_type>-npu-firmware_<version>.run --full
```
若系统出现如下关键回显信息，表示固件安装成功。
> Firmware package installed successfully! Reboot now or after driver installation for the installation/upgrade to take effect

#### 3.2.4 重启系统
```
reboot
```
#### 3.2.5 验证安装

执行如下命令
```
npu-smi info
```
如果返回类似如下信息，则说明加载成功
```
+--------------------------------------------------------------------------------------------------------+
| npu-smi 24.1.rc2                                 Version: 24.1.rc2                                     |
+-------------------------------+-----------------+------------------------------------------------------+
| NPU     Name                  | Health          | Power(W)     Temp(C)           Hugepages-Usage(page) |
| Chip    Device                | Bus-Id          | AICore(%)    Memory-Usage(MB)                        |
+===============================+=================+======================================================+
| 8       310P3                 | OK              | NA           52                0     / 0             |
| 0       0                     | 0000:01:00.0    | 0            1804 / 21527                            |
+===============================+=================+======================================================+
+-------------------------------+-----------------+------------------------------------------------------+
| NPU     Chip                  | Process id      | Process name             | Process memory(MB)        |
+===============================+=================+======================================================+
| No running processes found in NPU 8                                                                    |
+===============================+=================+======================================================+
```

### 3.3 安装CANN

#### 3.3.1 安装依赖
```
yum makecache

ulimit -u unlimited

source /etc/profile
```

#### 3.3.2 安装Toolkit开发套件包
```
chmod +x Ascend-cann-toolkit_<version>_linux-<arch>.run

./Ascend-cann-toolkit_<version>_linux-<arch>.run --install
```
安装完成后，若显示如下信息，则说明软件安装成功：
> xxx install success

配置环境变量，请根据set_env.sh的实际安装路径进行替换。
```
source /usr/local/Ascend/ascend-toolkit/set_env.sh
```
安装后检查。执行如下命令查询CANN版本信息，查询结果与安装软件包的版本一致时，则验证安装成功。

进入软件包安装信息文件目录，请用户根据实际安装路径替换。<arch>表示CPU架构（aarch64或x86_64）。
```
cd /usr/local/Ascend/ascend-toolkit/latest/<arch>-linux
```
执行以下命令获取版本信息。
```
cat ascend_toolkit_install.info
```
##### 注意：安装后需检查`/etc/profile`文件末尾是否包含如下语句，如果未自动添加，需手动添加
```
ulimit -u unlimited

source /usr/local/Ascend/ascend-toolkit/set_env.sh
```

#### 3.3.3 安装Kernels算子包
```
chmod +x Ascend-cann-kernels-<chip_type>_<version>_linux-<arch>.run

./Ascend-cann-kernels-<chip_type>_<version>_linux-<arch>.run --install
```
安装完成后，若显示如下信息，则说明软件安装成功：
> xxx install success

安装后检查。执行如下命令查询软件版本信息，查询结果与安装软件包的版本一致时，则验证安装成功。
进入软件包安装信息文件目录，请用户根据实际安装路径替换。
```
cd <path>/latest/opp_kernel
```
<path>请替换为Kernels依赖的CANN软件包安装路径。

执行以下命令，查看version_dir字段提供的版本信息。
```
cat version.info
```

## 4. 安装Milvus向量数据库

```
cp -r milivus_install /usr/local/milvus_docker

cd /usr/local/milvus_docker

sudo docker load -i milvus_v2.5.9.tar.gz

./standalone_embed.sh
```

## 5. 安装AI智能能力

```
unzip ai-model-serve-<commit_id>-<timestamp>.zip

cd ai-model-serve-<commit_id>-<timestamp>

bash deploy.sh
```
> 将安装至`/usr/local/data/src/ai-model-serve`目录下，确保父级目录存在

## 6. 验证服务是否正常
```
systemctl status ai-model-serve
```

## 7. 修改配置文件

> /usr/local/data/src/ai-model-serve/config.yaml

### 7.1 `external_ip`需修改为可供外部访问的ip，以提供下载能力
```
api_server:
  external_ip: "127.0.0.1"
```

### 7.2 `device_ids`需修改为实际存在的NPU设备ID，以供模型推理使用
```
onnx_provider:
  device_ids: [0, 1, 2, 3]
```

