#!/bin/bash

set -e

# ----------------------------
# 配置参数
# ----------------------------
PROJECT_DIR="/usr/local/data/src/ai-model-serve"
VENV_ZIP="my_env.zip"
VENV_DIR="$PROJECT_DIR/deploy"
RUN_SCRIPT="$PROJECT_DIR/run_main.sh"
SERVICE_NAME="ai-model-serve"
SYSTEMD_FILE="/etc/systemd/system/${SERVICE_NAME}.service"

# ----------------------------
# 检查 root 权限
# ----------------------------
if [ "$EUID" -ne 0 ]; then
    echo "请以 root 身份运行该脚本"
    exit 1
fi

# ----------------------------
# 查找项目源码 zip（以 ai-model-serve- 开头）
# ----------------------------
PROJECT_ZIP=$(ls ai-model-serve-*.zip 2>/dev/null | grep -vE "\-[0-9]{8}-[0-9]{6}\.zip$" | head -n 1)

if [[ -z "$PROJECT_ZIP" ]]; then
    echo "错误：未找到以 ai-model-serve- 开头的源码 zip 包"
    exit 1
fi

echo "使用源码包：$PROJECT_ZIP"

# ----------------------------
# 检查并停止运行中的服务
# ----------------------------
if systemctl is-active --quiet "$SERVICE_NAME"; then
    echo "检测到服务 $SERVICE_NAME 正在运行，准备停止旧服务..."
    systemctl stop "$SERVICE_NAME"
    echo "旧服务已停止。"
fi

# ----------------------------
# 清理并准备项目目录（带备份）
# ----------------------------
echo "准备项目目录：$PROJECT_DIR"
if [ -d "$PROJECT_DIR" ]; then
    TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
    BACKUP_DIR="${PROJECT_DIR}.bak-${TIMESTAMP}"
    echo "检测到已存在目录，备份为：$BACKUP_DIR"
    mv "$PROJECT_DIR" "$BACKUP_DIR"
fi
mkdir -p "$PROJECT_DIR"

# ----------------------------
# 解压源码 zip，直接展开内容到 $PROJECT_DIR
# ----------------------------
echo "解压源码至 $PROJECT_DIR ..."
unzip -q "$PROJECT_ZIP" -d "$PROJECT_DIR.tmp"

TOPDIR=$(ls "$PROJECT_DIR.tmp")
if [[ $(echo "$TOPDIR" | wc -l) -eq 1 ]] && [[ -d "$PROJECT_DIR.tmp/$TOPDIR" ]]; then
    mv "$PROJECT_DIR.tmp/$TOPDIR"/* "$PROJECT_DIR"
else
    mv "$PROJECT_DIR.tmp"/* "$PROJECT_DIR"
fi
rm -rf "$PROJECT_DIR.tmp"

# ----------------------------
# 解压虚拟环境 zip 到 VENV_DIR
# ----------------------------
echo "解压虚拟环境至 $VENV_DIR ..."
mkdir -p "$VENV_DIR"
unzip -q "$VENV_ZIP" -d "$VENV_DIR"

# ----------------------------
# 检查 run_main.sh 是否存在
# ----------------------------
if [ ! -f "$RUN_SCRIPT" ]; then
    echo "错误：找不到 $RUN_SCRIPT"
    exit 1
fi

chmod +x "$RUN_SCRIPT"

# ----------------------------
# 创建 systemd 服务文件
# ----------------------------
echo "创建 systemd 服务配置：$SYSTEMD_FILE"

cat > "$SYSTEMD_FILE" <<EOF
[Unit]
Description=AI Model Serve Service
After=network.target

[Service]
Type=simple
WorkingDirectory=$PROJECT_DIR
ExecStart=/bin/bash $RUN_SCRIPT
Restart=always
User=root

[Install]
WantedBy=multi-user.target
EOF

# ----------------------------
# 启动并启用服务
# ----------------------------
echo "重载 systemd 配置并启动服务"

systemctl daemon-reexec
systemctl daemon-reload
systemctl enable "$SERVICE_NAME"
systemctl start "$SERVICE_NAME"

echo "✅ 服务 $SERVICE_NAME 已启动成功"
