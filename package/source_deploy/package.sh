#!/bin/bash
set -e

# 设置路径
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
VENV_DIR="$ROOT_DIR/deploy"
DEPLOY_DIR="$SCRIPT_DIR"
TIMESTAMP=$(date +"%Y%m%d-%H%M%S")

echo "[INFO] ROOT_DIR: $ROOT_DIR"
echo "[INFO] DEPLOY_DIR: $DEPLOY_DIR"

# Step 1: 打包虚拟环境为 my_env.zip（如未存在）
if [[ -f "$DEPLOY_DIR/my_env.zip" ]]; then
    echo "[INFO] 已存在 my_env.zip，跳过打包步骤"
else
    echo "[INFO] 打包虚拟环境到 my_env.zip ..."
    cd "$VENV_DIR"
    zip -rq "$DEPLOY_DIR/my_env.zip" ./
    cd "$DEPLOY_DIR"
fi

# Step 2: 查找源码 zip（以 ai-model-serve-<commit_id>.zip 命名）
SOURCE_ZIP=$(ls ai-model-serve-*.zip | grep -vE "\-[0-9]{8}-[0-9]{6}\.zip$" | head -n 1)

if [[ ! -f "$SOURCE_ZIP" ]]; then
    echo "[ERROR] 未找到源码 zip（以 ai-model-serve-<commit_id>.zip 命名）"
    exit 1
fi

echo "[INFO] 找到源码包：$SOURCE_ZIP"

# Step 3: 提取 commit_id 或分支名
COMMIT_ID=$(basename "$SOURCE_ZIP" | sed -E 's/^ai-model-serve-(.+)\.zip$/\1/')
if [[ -z "$COMMIT_ID" ]]; then
    echo "[ERROR] 无法从 $SOURCE_ZIP 提取 commit_id"
    exit 1
fi

# Step 4: 构建部署目录并打包
PACKAGE_DIR="ai-model-serve-${COMMIT_ID}-${TIMESTAMP}"
FINAL_ZIP="${PACKAGE_DIR}.zip"

echo "[INFO] 创建临时目录 $PACKAGE_DIR 并复制部署内容"

mkdir -p "$PACKAGE_DIR"
cp "$SOURCE_ZIP" "$PACKAGE_DIR/"
cp "$DEPLOY_DIR/deploy.sh" "$PACKAGE_DIR/"
cp "$DEPLOY_DIR/my_env.zip" "$PACKAGE_DIR/"

echo "[INFO] 打包部署包：$FINAL_ZIP"
zip -qr "$FINAL_ZIP" "$PACKAGE_DIR"

# Step 5: 清理中间文件
rm -rf "$PACKAGE_DIR"
# 保留 my_env.zip 以便下次复用
echo "[DONE] 部署包已生成：$FINAL_ZIP"
