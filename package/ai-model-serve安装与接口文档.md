# 安装
安装命令

```plain
sudo bash INSTALL.sh
```

> 默认安装至`/usr/local/ai-model-serve`
>

安装后服务将自动运行，也可通过如下命令手动运行

```plain
systemctl start ai-model-serve
```

# 配置
进入安装目录，手动编辑`config.yaml`文件，具体可参考文件内说明；

> 注意：
>
> 如需提供下载，需修改`api_server.external_ip`字段为外部可访问IP；
>
> 如需提供推理任务完成后通知，需修改`web_hooks.on_inerence_done`字段为可接受post请求的webhook api；
>



# 典型使用流程
![](https://cdn.nlark.com/yuque/__puml/65d75c4f5b2c05bc017690f4442f7f91.svg)

# API说明
## API 概览
1. 目前API支持的HTTP请求方法

| 请求方法 | 说明 |
| --- | --- |
| GET | 用于检索资源 |
| POST | 用于创建资源 |
| DELETE | 用于删除资源 |




## API通用错误或异常说明
1. 发送错误或无效的 JSON 将导致 `400 Bad Request` 响应。

```http
Status: 400 Bad Request

{
		"error_message": "bad_request"
}
```

2. 发送无效的字段将导致 `422 Unprocessable Entity` 响应。

```http
Status: 422 Unprocessable Entity

{
		"error_message": "missing_field"
}
```

3. 请求处理的资源不存在将导致 `404 Not Found` 响应。

```http
Status: 404 Not Found

{
		"error_message": "resource_not_found"
}
```

4. 请求处理的资源已存在或属性已为希望设定的值将触发 `304 Not Modified` 响应。

```http
Status: 304 Not Modified

{
		"error_message": "resource_not_modified"
}
```

5. 其他未知错误将导致 `500 Internal Server Error` 响应。

```http
Status: 500 Internal Server Error

{
		"error_message": "server_error"
}
```



## API 参考
### Base URL
```http
http://{host}:8000
```



### 推理任务
#### 创建降噪推理任务
```http
POST /audio/denoiser
```

##### 参数
| 参数 | 类型 | 位置 | 说明 |
| --- | --- | --- | --- |
| id | string | body | 任务id |
| type | string | body | 任务类型：<br/>`denoiser`：音频降噪 |
| input_medias | array<Media> | body | 需模型处理的媒体列表 |


###### Media
| 参数 | 类型 | 说明 |
| --- | --- | --- |
| id | string | 媒体id |
| model | string | 模型类型：<br/>`NSNet2`：<br/>`DeepFilterNet3`：<br/>`FullSubNetPlus`： |
| url | string | 媒体下载地址<br/>支持常见音频格式，mp3，wav，aac等； |


##### 请求
```json
POST /audio/denoiser

{
    "id": "test_denoiser_xxx",
    "type": "denoiser",
    "input_medias": [
        {
            "id": "aabbbcccc",
            "model": "NSNet2",
            "url": "http://***************:8081/noisy1.mp3"
        },
        {
            "id": "aabbbdddd",
            "model": "FullSubNetPlus",
            "url": "http://***************:8081/noisy2.mp3"
        },
        {
            "id": "aabbbeeee",
            "model": "DeepFilterNet3",
            "url": "http://***************:8081/noisy3.mp3"
        },
        {
            "id": "aabbbffff",
            "model": "NSNet2",
            "url": "http://***************:8081/qu_取经归来.mp3"
        }
    ]
}
```

##### 返回
```http
Status：201 Created
```

```json
{
  "id": "test_denoiser_xxx",
  "type": "denoiser",
  "hash_id": "5a9dd0c6c8bf99c18b2ab12266c4f894",
  "output_medias": [
    {
      "id": "aabbbcccc",
      "model": "NSNet2",
      "url": "",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.470879",
          "status": "in_progress",
          "updated_at": "2024-10-18T09:57:14.471511"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.470893",
          "status": "queued",
          "updated_at": "2024-10-18T09:57:14.470893"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.470896",
          "status": "queued",
          "updated_at": "2024-10-18T09:57:14.470896"
        }
      ]
    },
    {
      "id": "aabbbdddd",
      "model": "FullSubNetPlus",
      "url": "",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.471382",
          "status": "in_progress",
          "updated_at": "2024-10-18T09:57:14.472505"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.471391",
          "status": "queued",
          "updated_at": "2024-10-18T09:57:14.471391"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.471394",
          "status": "queued",
          "updated_at": "2024-10-18T09:57:14.471394"
        }
      ]
    },
    {
      "id": "aabbbeeee",
      "model": "DeepFilterNet3",
      "url": "",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.472145",
          "status": "in_progress",
          "updated_at": "2024-10-18T09:57:14.473860"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.472153",
          "status": "queued",
          "updated_at": "2024-10-18T09:57:14.472153"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.472156",
          "status": "queued",
          "updated_at": "2024-10-18T09:57:14.472156"
        }
      ]
    },
    {
      "id": "aabbbffff",
      "model": "NSNet2",
      "url": "",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.473581",
          "status": "queued",
          "updated_at": "2024-10-18T09:57:14.473581"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.473595",
          "status": "queued",
          "updated_at": "2024-10-18T09:57:14.473595"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.473601",
          "status": "queued",
          "updated_at": "2024-10-18T09:57:14.473601"
        }
      ]
    }
  ]
}
```

> 1. hash_Id；
>     1. 模块根据传入的input_medias计算MD5所得；
> 2. step类型：
>     1. transcode：转码
>     2. inference：推理
>     3. notify：webhook通知
> 3. status 可选值：
>     1. queued：排队中；
>     2. in_progress：进行中；
>     3. success：成功；
>     4. failed：失败；
> 4. output_medias 与传入的input_medias对应；
>

#### 查询降噪推理任务
```http
GET /audio/denoiser/{hash_id}
```

##### 参数
| 参数 | 类型 | 位置 | 说明 |
| --- | --- | --- | --- |
| hash_id | string | path | 推理任务的hash_id |




##### 请求
```json
GET /audio/denoiser/5a9dd0c6c8bf99c18b2ab12266c4f894
```

##### 返回
```http
Status：200 OK
```

```json
{
  "id": "test_denoiser_xxx",
  "type": "denoiser",
  "hash_id": "5a9dd0c6c8bf99c18b2ab12266c4f894",
  "output_medias": [
    {
      "id": "aabbbcccc",
      "model": "NSNet2",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/NSNet2_aabbbcccc_noisy1.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.470879",
          "status": "success",
          "updated_at": "2024-10-18T09:57:14.650746"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.470893",
          "status": "success",
          "updated_at": "2024-10-18T09:57:16.473918"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.470896",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.092759"
        }
      ]
    },
    {
      "id": "aabbbdddd",
      "model": "FullSubNetPlus",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/FullSubNetPlus_aabbbdddd_noisy2.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.471382",
          "status": "success",
          "updated_at": "2024-10-18T09:57:14.741265"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.471391",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.088407"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.471394",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.093169"
        }
      ]
    },
    {
      "id": "aabbbeeee",
      "model": "DeepFilterNet3",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/DeepFilterNet3_aabbbeeee_noisy3.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.472145",
          "status": "success",
          "updated_at": "2024-10-18T09:57:14.793264"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.472153",
          "status": "success",
          "updated_at": "2024-10-18T09:57:24.628760"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.472156",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.093574"
        }
      ]
    },
    {
      "id": "aabbbffff",
      "model": "NSNet2",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/NSNet2_aabbbffff_qu_取经归来.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.473581",
          "status": "success",
          "updated_at": "2024-10-18T09:57:16.264197"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.473595",
          "status": "success",
          "updated_at": "2024-10-18T09:57:20.255020"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.473601",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.094019"
        }
      ]
    }
  ]
}
```

> 1. url:
>     1. 对应媒体完成推理后生成的媒体下载地址；
>



#### 删除降噪推理任务
```http
DELETE /audio/denoiser/{hash_id}
```

##### 参数
| 参数 | 类型 | 位置 | 说明 |
| --- | --- | --- | --- |
| hash_id | string | path | 推理任务的hash_id |


##### 请求
```json
DELETE /audio/denoiser/5a9dd0c6c8bf99c18b2ab12266c4f894
```

##### 返回
```http
Status：200 OK
```

```json
{
  "id": "test_denoiser_xxx",
  "type": "denoiser",
  "hash_id": "5a9dd0c6c8bf99c18b2ab12266c4f894",
  "output_medias": [
    {
      "id": "aabbbcccc",
      "model": "NSNet2",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/NSNet2_aabbbcccc_noisy1.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.470879",
          "status": "success",
          "updated_at": "2024-10-18T09:57:14.650746"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.470893",
          "status": "success",
          "updated_at": "2024-10-18T09:57:16.473918"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.470896",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.092759"
        }
      ]
    },
    {
      "id": "aabbbdddd",
      "model": "FullSubNetPlus",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/FullSubNetPlus_aabbbdddd_noisy2.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.471382",
          "status": "success",
          "updated_at": "2024-10-18T09:57:14.741265"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.471391",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.088407"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.471394",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.093169"
        }
      ]
    },
    {
      "id": "aabbbeeee",
      "model": "DeepFilterNet3",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/DeepFilterNet3_aabbbeeee_noisy3.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.472145",
          "status": "success",
          "updated_at": "2024-10-18T09:57:14.793264"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.472153",
          "status": "success",
          "updated_at": "2024-10-18T09:57:24.628760"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.472156",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.093574"
        }
      ]
    },
    {
      "id": "aabbbffff",
      "model": "NSNet2",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/NSNet2_aabbbffff_qu_取经归来.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.473581",
          "status": "success",
          "updated_at": "2024-10-18T09:57:16.264197"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.473595",
          "status": "success",
          "updated_at": "2024-10-18T09:57:20.255020"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.473601",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.094019"
        }
      ]
    }
  ]
}
```

## Webhook 参考
### 推理完成通知
1. 参考配置文件`web_hooks.on_inference_done`字段配置；
2. 待推理任务描述`input_medias`中包含的所有媒体均处理完成后触发；

#### webhook示例
```json
POST http://sample.com/api/webhook

{
  "id": "test_denoiser_xxx",
  "type": "denoiser",
  "hash_id": "5a9dd0c6c8bf99c18b2ab12266c4f894",
  "output_medias": [
    {
      "id": "aabbbcccc",
      "model": "NSNet2",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/NSNet2_aabbbcccc_noisy1.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.470879",
          "status": "success",
          "updated_at": "2024-10-18T09:57:14.650746"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.470893",
          "status": "success",
          "updated_at": "2024-10-18T09:57:16.473918"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.470896",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.092759"
        }
      ]
    },
    {
      "id": "aabbbdddd",
      "model": "FullSubNetPlus",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/FullSubNetPlus_aabbbdddd_noisy2.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.471382",
          "status": "success",
          "updated_at": "2024-10-18T09:57:14.741265"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.471391",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.088407"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.471394",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.093169"
        }
      ]
    },
    {
      "id": "aabbbeeee",
      "model": "DeepFilterNet3",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/DeepFilterNet3_aabbbeeee_noisy3.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.472145",
          "status": "success",
          "updated_at": "2024-10-18T09:57:14.793264"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.472153",
          "status": "success",
          "updated_at": "2024-10-18T09:57:24.628760"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.472156",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.093574"
        }
      ]
    },
    {
      "id": "aabbbffff",
      "model": "NSNet2",
      "url": "http://192.168.124.100:8000/audio/denoiser/output/5a9dd0c6c8bf99c18b2ab12266c4f894/NSNet2_aabbbffff_qu_取经归来.wav",
      "steps": [
        {
          "type": "transcode",
          "created_at": "2024-10-18T09:57:14.473581",
          "status": "success",
          "updated_at": "2024-10-18T09:57:16.264197"
        },
        {
          "type": "inference",
          "created_at": "2024-10-18T09:57:14.473595",
          "status": "success",
          "updated_at": "2024-10-18T09:57:20.255020"
        },
        {
          "type": "notify",
          "created_at": "2024-10-18T09:57:14.473601",
          "status": "success",
          "updated_at": "2024-10-18T09:57:29.094019"
        }
      ]
    }
  ]
}
```



