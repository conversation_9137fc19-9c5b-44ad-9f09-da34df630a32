#!/bin/bash

# 默认安装目录
INSTALL_DIR="/usr/local"

# systemd service 文件的默认路径
SYSTEMD_SERVICE_DIR="/etc/systemd/system"

# 显示帮助信息
show_help() {
    echo "Usage: install.sh [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --prefix DIR     Specify the installation directory (default: /usr/local)"
    echo "  -h, --help       Show this help message and exit"
    echo ""
    exit 0
}

# 解析命令行参数
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --prefix)
            INSTALL_DIR="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            ;;
    esac
done

# 安装的文件夹路径
TARGET_DIR="$INSTALL_DIR/ai-model-serve"

# 检查是否有写权限
if [ ! -w "$INSTALL_DIR" ]; then
    echo "Error: No write permission for $INSTALL_DIR. Try running with sudo."
    exit 1
fi

# 创建安装目录
echo "Creating installation directory at $TARGET_DIR..."
mkdir -p "$TARGET_DIR"

# 复制所需文件
echo "Copying ai-model-serve, config.yaml, version.yaml to $TARGET_DIR..."
cp -r ai-model-serve "$TARGET_DIR"
cp config.yaml "$TARGET_DIR"
cp version.yaml "$TARGET_DIR"

# 更新 service 文件的路径
SERVICE_FILE="ai-model-serve.service"
MODIFIED_SERVICE_FILE="/tmp/$SERVICE_FILE"
sed "s|/usr/local/ai-model-serve|$TARGET_DIR|g" "$SERVICE_FILE" > "$MODIFIED_SERVICE_FILE"

# 停止服务
echo "Stoping ai-model-serve service..."
systemctl stop "$SERVICE_FILE"

# 复制 service 文件到 systemd 目录
echo "Copying $SERVICE_FILE to $SYSTEMD_SERVICE_DIR..."
cp "$MODIFIED_SERVICE_FILE" "$SYSTEMD_SERVICE_DIR/$SERVICE_FILE"

# 刷新 systemd 守护进程
echo "Reloading systemd daemon..."
systemctl daemon-reload

# 设置开机自启
echo "Enabling ai-model-serve to start on boot..."
systemctl enable "$SERVICE_FILE"

# 启动服务
echo "Starting ai-model-serve service..."
systemctl start "$SERVICE_FILE"

# 检查服务状态
SERVICE_STATUS=$(systemctl is-active "$SERVICE_FILE")
if [ "$SERVICE_STATUS" = "active" ]; then
    echo "ai-model-serve is successfully installed and running!"
else
    echo "ai-model-serve installation completed, but failed to start. Check logs for more details."
fi
