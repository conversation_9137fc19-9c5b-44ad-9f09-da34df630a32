import torch
import torchvision.models as models

class ResNet50FeatureExtractor(torch.nn.Module):
    def __init__(self):
        super().__init__()
        resnet = models.resnet50(pretrained=True)
        # 去掉最后的全连接层（fc）
        self.feature_extractor = torch.nn.Sequential(*list(resnet.children())[:-1])

    def forward(self, x):
        x = self.feature_extractor(x)  # shape: (batch, 2048, 1, 1)
        return x.view(x.size(0), -1)   # 展平: (batch, 2048)

# 实例化模型
model = ResNet50FeatureExtractor()
model.eval()

# 生成一个 dummy 输入
dummy_input = torch.randn(1, 3, 224, 224)

# 导出 ONNX 模型
torch.onnx.export(
    model,
    dummy_input,
    "resnet50_features.onnx",           # 👈 输出路径
    input_names=["input"],
    output_names=["features"],
    dynamic_axes={"input": {0: "batch_size"}, "features": {0: "batch_size"}},
    opset_version=11
)

print("✅ 成功导出 resnet50_features.onnx (2048维特征)")
