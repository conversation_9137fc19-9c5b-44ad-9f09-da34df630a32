import os
import shutil
import subprocess
import yaml
from pathlib import Path
import zipfile
from datetime import datetime
import platform

def get_git_commit_id():
    """获取当前 Git 仓库的 commit ID"""
    try:
        commit_id = subprocess.check_output(['git', 'rev-parse', 'HEAD']).strip().decode('utf-8')
        return commit_id[:7]  # 只取前7位
    except subprocess.CalledProcessError as e:
        print(f"无法获取 Git commit ID: {e}")
        return None

def update_config_with_git_version(config_path, git_version):
    """更新 version.yaml 文件中的 git_version 字段"""
    if not config_path.exists():
        print(f"未找到 {config_path}，无法更新 git_version 字段")
        return
    
    try:
        # 读取 YAML 文件
        with open(config_path, 'r', encoding='utf-8') as file:
            config_data = yaml.safe_load(file)
        
        # 更新 git_version 字段
        config_data['git_version'] = git_version
        
        # 写回更新后的 YAML 文件
        with open(config_path, 'w', encoding='utf-8') as file:
            yaml.safe_dump(config_data, file)
        print(f"已将 git_version 更新为 {git_version} 并写入 {config_path}")
    except Exception as e:
        print(f"更新 {config_path} 时出错: {e}")

def build_and_package():
    # Step 1: 获取 Git commit ID
    git_commit_id = get_git_commit_id()
    
    # Step 2: 获取平台和操作系统信息
    platform_type = platform.machine()  # 获取平台类型
    os_type = platform.system()  # 获取操作系统类型
    
    # Step 3: 打包项目
    print("开始打包...")
    try:
        subprocess.run(["pyinstaller", "main.spec"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return
    
    # Step 4: 将 config-deploy.yaml 复制到 dist 文件夹
    dist_folder = Path("dist")
    config_file = Path("config-deploy.yaml")
    target_config_file = dist_folder / "config.yaml"
    
    if config_file.exists():
        shutil.copy(str(config_file), str(target_config_file))
        print(f"已将 {config_file} 复制到 {target_config_file}")
    else:
        print(f"未找到 {config_file}，跳过此文件。")

    version_file = Path("version.yaml")
    target_version_file = dist_folder / "version.yaml"
    
    if version_file.exists():
        shutil.copy(str(version_file), str(target_version_file))
        print(f"已将 {version_file} 复制到 {target_version_file}")
    else:
        print(f"未找到 {version_file}，跳过此文件。")

    install_sh = Path("package/INSTALL.sh")
    target_install_sh = dist_folder / "INSTALL.sh"
    
    if install_sh.exists():
        shutil.copy(str(install_sh), str(target_install_sh))
        print(f"已将 {install_sh} 复制到 {target_install_sh}")
    else:
        print(f"未找到 {install_sh}，跳过此文件。")

    service_file = Path("package/ai-model-serve.service")
    target_service_file = dist_folder / "ai-model-serve.service"
    
    if service_file.exists():
        shutil.copy(str(service_file), str(target_service_file))
        print(f"已将 {service_file} 复制到 {target_service_file}")
    else:
        print(f"未找到 {service_file}，跳过此文件。")
    
    api_file = Path("package/ai-model-serve安装与接口文档.md")
    target_api_file = dist_folder / "ai-model-serve安装与接口文档.md"
    
    if api_file.exists():
        shutil.copy(str(api_file), str(target_api_file))
        print(f"已将 {api_file} 复制到 {target_api_file}")
    else:
        print(f"未找到 {api_file}，跳过此文件。")

    # Step 5: 更新 dist 文件夹中 version.yaml 的 git_version 字段
    if git_commit_id:
        update_config_with_git_version(target_version_file, git_commit_id)
    
    # Step 6: 重命名打包输出的 main 文件为 ai-model-serve
    main_executable = dist_folder / "main"
    target_executable = dist_folder / "ai-model-serve"
    
    if main_executable.exists():
        shutil.move(str(main_executable), str(target_executable))
        print(f"重命名 {main_executable} 为 {target_executable}")
    else:
        print(f"未找到 {main_executable}，请检查打包过程。")
        return
    
    # Step 7: 获取当前时间戳并格式化为 YYYYMMDDHHMMSS
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    
    # 根据平台、操作系统、Git commit ID 和时间戳生成文件夹和文件名
    zip_foldername = f"ai-model-serve_{platform_type}_{os_type}_{timestamp}_{git_commit_id}"
    zip_filename = f"{zip_foldername}.zip"
    
    # Step 8: 打包 dist 文件夹中的内容，排除 serve_medias 文件夹
    print(f"开始打包为 {zip_filename}")
    
    with zipfile.ZipFile(zip_filename, "w", zipfile.ZIP_DEFLATED) as zipf:
        # 遍历 dist 文件夹，排除 serve_medias 目录
        for root, dirs, files in os.walk(dist_folder):
            # 跳过 serve_medias 文件夹
            if 'serve_medias' in dirs:
                dirs.remove('serve_medias')
            
            # 添加文件到 zip，保持相对路径
            for file in files:
                file_path = Path(root) / file
                relative_path = file_path.relative_to(dist_folder)
                zipf.write(file_path, arcname=f"{zip_foldername}/{relative_path}")

    print(f"打包完成: {zip_filename}")

if __name__ == "__main__":
    build_and_package()
