from datetime import datetime
from typing import List, Optional
from enum import Enum
from sqlmodel import Field, SQLModel, create_engine, Relationship

# 枚举定义
class StepTypeEnum(str, Enum):
    transcode = 'transcode'
    inference = 'inference'
    notify = 'notify'

class StepStatusEnum(str, Enum):
    queued = 'queued'
    in_progress = 'in_progress'
    success = 'success'
    failed = 'failed'

class RunTypeEnum(str, Enum):
    denoiser = 'denoiser'

class ModelEnum(str, Enum):
    NSNet2 = 'NSNet2'
    DeepFilterNet3 = 'DeepFilterNet3'
    FullSubNetPlus = 'FullSubNetPlus'

# ServeStep类
class ServeStepBase(SQLModel):
    type: StepTypeEnum
    status: StepStatusEnum
    created_at: str
    updated_at: str
    
    @classmethod
    def create_step(cls, type: StepTypeEnum):
        now = datetime.now().isoformat()
        step = cls()
        step.type = type
        step.status = StepStatusEnum.queued
        step.created_at = now
        step.updated_at = now
        return step

    def update_status(self, status: StepStatusEnum):
        self.status = status
        self.updated_at = datetime.now().isoformat()


class ServeStep(ServeStepBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    pri_id: int | None = Field(default=None, primary_key=True)
    media_pri_id: int | None = Field(default=None, foreign_key='mediaoutput.pri_id')
    media: Optional['MediaOutput'] = Relationship(back_populates='steps')

class ServeStepCreate(ServeStepBase):
    pass

class ServeStepPublic(ServeStepBase):
    pass
    
# Media类
class MediaBase(SQLModel):
    id: str
    model: ModelEnum
    url: str

    # 让 Enum 直接作为字符串存储和序列化，避免erve_run.model_dump_json()出现警告
    class Config:
        use_enum_values = True

class MediaInput(MediaBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    pri_id: int | None = Field(default=None, primary_key=True)
    serve_run_pri_id: int | None = Field(default=None, foreign_key='serverun.pri_id')
    serve_run: Optional['ServeRun'] = Relationship(back_populates='input_medias')

class MediaOutput(MediaBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    pri_id: int | None = Field(default=None, primary_key=True)
    serve_run_pri_id: int | None = Field(default=None, foreign_key='serverun.pri_id')
    serve_run: Optional['ServeRun'] = Relationship(back_populates='output_medias')
    steps: List[ServeStep] = Relationship(back_populates='media')

    def get_setp_by_type(self, step_type: StepTypeEnum) -> ServeStep:
        for step in self.steps:
            if step.type == step_type:
                return step
        return None

class MediaCreate(MediaBase):
    pass

class MediaPublic(MediaBase):
    steps: List[ServeStepPublic] = []
    # pass

# ServeRun类
class ServeRunBase(SQLModel):
    id: str
    type: RunTypeEnum
    # private
    hash_id: str = ''
    run_folder: str = ''

class ServeRun(ServeRunBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    pri_id: int | None = Field(default=None, primary_key=True)
    input_medias: List[MediaInput] = Relationship(back_populates='serve_run')
    output_medias: List[MediaOutput] = Relationship(back_populates='serve_run')

    def get_output_media_by_id(self, media_id: str) -> MediaOutput:
        """根据媒体 ID 获取媒体对象"""
        for media in self.output_medias:
            if media.id == media_id:
                return media
        return None

class ServeRunCreate(ServeRunBase):
    input_medias: List[MediaInput]

class ServeRunPublic(ServeRunBase):
    output_medias: List[MediaPublic] = []