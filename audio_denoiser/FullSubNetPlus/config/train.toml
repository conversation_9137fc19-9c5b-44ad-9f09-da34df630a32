[meta]
save_dir = "logs/Complex_amp_subinput_allattention_DeepTCNFCFullSubNet"
# save_dir = "logs/test"
description = "This is a description of FullSubNet experiment."
seed = 0  # set random seed for random, numpy, pytorch-gpu and pytorch-cpu
keep_reproducibility = false  # see https://pytorch.org/docs/stable/notes/randomness.html
use_amp = false  # use automatic mixed precision, it will benefits Tensor Core-enabled GPU (e.g. Volta, Turing, Ampere). 2-3X speedup。


[acoustics]
n_fft = 512
win_length = 512
sr = 16000
hop_length = 256


[loss_function]
name = "mse_loss"
[loss_function.args]


[optimizer]
lr = 0.001
beta1 = 0.9
beta2 = 0.999


[train_dataset]
path = "fullsubnet.dataset.dataset_train.Dataset"
[train_dataset.args]
# clean_dataset = "train_data_DNS_2021_16k/clean_book.txt"
clean_dataset = "train_data_DNS_2021_16k/clean_book.txt"
clean_dataset_limit = false
clean_dataset_offset = 0
# noise_dataset = "train_data_DNS_2021_16k/noise.txt"
noise_dataset = "train_data_DNS_2021_16k/noise.txt"
noise_dataset_limit = false
noise_dataset_offset = 0
num_workers = 36
pre_load_clean_dataset = false
pre_load_noise = false
pre_load_rir = false
reverb_proportion = 0.75
# rir_dataset = "train_data_DNS_2021_16k/rir.txt"
rir_dataset = "train_data_DNS_2021_16k/rir.txt"
rir_dataset_limit = false
rir_dataset_offset = 0
silence_length = 0.2
snr_range = [-5, 20]
sr = 16000
sub_sample_length = 3.072
target_dB_FS = -25
target_dB_FS_floating_value = 10


[train_dataset.dataloader]
batch_size = 18
num_workers = 24
drop_last = true
pin_memory = true


[validation_dataset]
path = "fullsubnet.dataset.dataset_validation.Dataset"
[validation_dataset.args]
dataset_dir_list = [
    "data/DNS-Challenge/DNS-Challenge-interspeech2020-master/datasets/test_set/synthetic/with_reverb/",
    "data/DNS-Challenge/DNS-Challenge-interspeech2020-master/datasets/test_set/synthetic/no_reverb/"
]
sr = 16000


[model]
path = "fullsubnet_plus.model.fullsubnet_plus.FullSubNet_Plus"
[model.args]
sb_num_neighbors = 15
fb_num_neighbors = 0
num_freqs = 257
look_ahead = 2
sequence_model = "LSTM"
fb_output_activate_function = "ReLU"
sb_output_activate_function = false
channel_attention_model = "TSSE"
fb_model_hidden_size = 512
sb_model_hidden_size = 384
weight_init = false
norm_type = "offline_laplace_norm"
num_groups_in_drop_band = 2
kersize=[3, 5, 10]
subband_num = 1


[trainer]
path = "fullsubnet_plus.trainer.trainer.Trainer_Finetune"
[trainer.train]
clip_grad_norm_value = 10
epochs = 9999
alpha = 1
save_checkpoint_interval = 1
[trainer.validation]
save_max_metric_score = true
validation_interval = 1
[trainer.visualization]
metrics = ["WB_PESQ", "NB_PESQ", "STOI", "SI_SDR"]
n_samples = 10
num_workers = 12
