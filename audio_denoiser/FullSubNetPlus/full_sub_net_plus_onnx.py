import toml
import os
import sys

from .speech_enhance.fullsubnet_plus.inferencer.inferencer import Inferencer

class FullSubNetPlusEnhancer(object):
    def __init__(self):
        current_file_path = os.path.abspath(__file__)
        current_folder = os.path.dirname(current_file_path)
        configuration = toml.load(os.path.join(current_folder, "config/inference.toml"))
        self.fullsubnet_plus_inferencer = Inferencer(configuration, os.path.join(current_folder, "best_model.tar"), "")

    def __call__(self, input_path: str, output_path: str):
        self.fullsubnet_plus_inferencer.denoise(input_path, output_path)