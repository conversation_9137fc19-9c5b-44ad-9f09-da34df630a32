from pathlib import Path

import librosa
import numpy as np

from ...audio_zen.dataset.base_dataset import BaseDataset
from ...audio_zen.utils import basename


class Dataset(BaseDataset):
    def __init__(self,
                 file_path,
                 sr,
                 ):
        """
        Args:
            noisy_dataset_dir_list (str or list): noisy dir or noisy dir list
        """
        super().__init__()
        self.sr = sr

        noisy_file_path_list = [file_path]

        self.noisy_file_path_list = noisy_file_path_list
        self.length = len(self.noisy_file_path_list)

    def __len__(self):
        return self.length

    def __getitem__(self, item):
        noisy_file_path = self.noisy_file_path_list[item]
        noisy_y = librosa.load(noisy_file_path, sr=self.sr)[0]
        noisy_y = noisy_y.astype(np.float32)

        return noisy_y, basename(noisy_file_path)[0]
