import time
from fastapi import Request, FastAPI
from starlette.middleware.base import BaseHTTPMiddleware
from utils.logger import get_logger

logger = get_logger("api")

class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # 记录请求信息
        logger.info(
            f"Request started: {request.method} {request.url.path}",
            extra={
                "method": request.method,
                "path": request.url.path,
                "query_params": str(request.query_params),
                "client": request.client.host if request.client else None,
            }
        )
        
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            logger.info(
                f"Request completed: {request.method} {request.url.path} - {response.status_code} ({process_time:.4f}s)",
                extra={
                    "method": request.method,
                    "path": request.url.path,
                    "status_code": response.status_code,
                    "process_time": process_time,
                }
            )
            
            return response
        except Exception as e:
            # 记录异常信息
            process_time = time.time() - start_time
            logger.error(
                f"Request failed: {request.method} {request.url.path} - {str(e)} ({process_time:.4f}s)",
                exc_info=True,
                extra={
                    "method": request.method,
                    "path": request.url.path,
                    "process_time": process_time,
                }
            )
            raise

def add_logging_middleware(app: FastAPI):
    app.add_middleware(LoggingMiddleware)