import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parents[2]
sys.path.append(str(project_root))

# 设置当前工作目录为项目根目录，以便正确加载配置文件
import os
os.chdir(str(project_root))

from sqlmodel import SQLModel
from alembic import context
from database.orm_database import engine
from config import DATABASE_URL, settings

# 导入所有模型以确保它们被注册到 SQLModel.metadata
# 这里需要导入所有包含 SQLModel 模型的模块
from orm.train_task_rom import TrainTask, TrainTaskStep
from orm.model_orm import Model, ModelStep
from orm.vector_task_orm import VectorMedia, VectorMediaStep, VectorTask
from orm.inference_task_orm import InferTask, InferTaskStep,  \
    InferRun, InferObjDetRun, InferFacDetRun, InferImgEngRun, InferSubRegRun, \
    InferEndpoint, InferVectorEndpoint, InferLivePublishEndpoint, InferCroGatEndpoint, InferRegIntEndpoint

database_url = settings.api_server.database_url or DATABASE_URL
# 这是 Alembic Config 对象，提供访问 alembic.ini 文件中值的方式
config = context.config

# 设置 SQLAlchemy URL
config.set_main_option("sqlalchemy.url", database_url)

# 添加 MetaData 对象，包含所有表定义
target_metadata = SQLModel.metadata

def run_migrations_offline():
    """在"离线"模式下运行迁移。"""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        # 添加以下配置以防止数据丢失
        render_as_batch=True,  # 使用批处理模式，特别适用于 SQLite
        compare_type=True,     # 比较列类型
        include_object=lambda obj, name, type_, reflected, compare_to: 
            # 排除特定对象，如果需要的话
            True
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online():
    """在"在线"模式下运行迁移。"""
    with engine.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata,
            # 添加以下配置以防止数据丢失
            render_as_batch=True,  # 使用批处理模式，特别适用于 SQLite
            compare_type=True,     # 比较列类型
            include_object=lambda obj, name, type_, reflected, compare_to: 
                # 排除特定对象，如果需要的话
                True
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
