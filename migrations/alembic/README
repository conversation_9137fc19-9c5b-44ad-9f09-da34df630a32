# Alembic 数据库迁移指南

Alembic 是一个轻量级的数据库迁移工具，用于 SQLAlchemy。本项目使用 Alembic 管理数据库架构变更。

## 基本命令

### 创建新的迁移脚本

当您修改了数据模型后，需要创建新的迁移脚本：

```bash
cd migrations
alembic revision --autogenerate -m "描述您的更改"
```

这将自动检测模型变化并生成迁移脚本。

### 应用迁移

将数据库更新到最新版本：

```bash
cd migrations
alembic upgrade head
```

### 查看迁移历史

查看所有迁移版本：

```bash
cd migrations
alembic history
```

### 回滚迁移

回滚到特定版本：

```bash
cd migrations
alembic downgrade <版本号>
```

回滚一个版本：

```bash
cd migrations
alembic downgrade -1
```

## 注意事项

1. 自动生成的迁移脚本可能不完美，请在应用前检查
2. 某些复杂的更改（如重命名表或列）可能需要手动编辑迁移脚本
3. 在生产环境应用迁移前，请先在测试环境验证
4. 确保所有模型类都已导入到 env.py 中，否则 Alembic 无法检测到它们
