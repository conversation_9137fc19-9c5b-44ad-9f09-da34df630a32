"""为向量化增加media_extend字段

Revision ID: f4c3cc726e9d
Revises: 73881597f5d2
Create Date: 2025-06-03 20:32:01.331550

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'f4c3cc726e9d'
down_revision: Union[str, None] = '73881597f5d2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('infervectorendpoint', schema=None) as batch_op:
        batch_op.add_column(sa.Column('media_extend', sqlmodel.sql.sqltypes.AutoString(), nullable=False, server_default=''))

    with op.batch_alter_table('vectormedia', schema=None) as batch_op:
        batch_op.add_column(sa.Column('media_extend', sqlmodel.sql.sqltypes.AutoString(), nullable=False, server_default=''))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vectormedia', schema=None) as batch_op:
        batch_op.drop_column('media_extend')

    with op.batch_alter_table('infervectorendpoint', schema=None) as batch_op:
        batch_op.drop_column('media_extend')

    # ### end Alembic commands ###
