"""增加音频路径字段

Revision ID: 5057058d17f5
Revises: 73881597f5d2
Create Date: 2025-06-25 14:54:03.604026

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '5057058d17f5'
down_revision: Union[str, None] = '73881597f5d2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('infertask', schema=None) as batch_op:
        batch_op.add_column(sa.Column('audio_folder_path', sqlmodel.sql.sqltypes.AutoString(), nullable=False, server_default=''))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('infertask', schema=None) as batch_op:
        batch_op.drop_column('audio_folder_path')

    # ### end Alembic commands ###
