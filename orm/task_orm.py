from enum import Enum
from typing import List, Optional

from pydantic import ConfigDict
from sqlalchemy import JSON, Column
from sqlmodel import Field, SQLModel

from orm.media_orm import MediaMetadata, MediaTypeEnum
from orm.model_orm import ModelTypeEnum
from orm.step_rom import StepTypeEnum

# class MediaType(Enum):
#     IMAGE = "image"
#     VIDEO = "video"
#     AUDIO = "audio"
#     STREAM = "stream"
#     FILE = "file"
#     UNKNOWN = "unknown"

class EndpointTypeEnum(str, Enum):
    vector = 'vector'
    live_publish = 'live_publish'
    # 人员聚集检测
    crowd_gathering = 'crowd_gathering'
    # 区域入侵检测
    regional_intrusion = 'regional_intrusion'

    def get_step_type(self):
        if self == EndpointTypeEnum.vector:
            return StepTypeEnum.vector
        elif self == EndpointTypeEnum.live_publish:
            return StepTypeEnum.live_publish
        elif self == EndpointTypeEnum.crowd_gathering:
            return StepTypeEnum.crowd_gathering
        elif self == EndpointTypeEnum.regional_intrusion:
            return StepTypeEnum.regional_intrusion
        else:
            raise Exception(f"Unsupported endpoint type: {self}")

class DirectionEnum(str, Enum):
    ENTER = 'enter'
    LEAVE = 'leave'
    ENTER_AND_LEAVE = 'enter_and_leave'

class VectorEndpoint(MediaMetadata):
    pass

class LivePublishEndpoint(SQLModel):
    target_url: str = ''

class CroGatEndpoint(SQLModel):
    # 乘以画面中平均人宽（像素）值的系数，小于间距的人，被判定为聚集人
    person_space_coefficient: float = 1.5
    # 聚集人数阈值，大于此值判定为聚集组
    person_count_threshold: int = 3
    risk_frame_space: int = 25
    # sqlmodel bug, use sa_type, see@https://github.com/fastapi/sqlmodel/discussions/743
    roi_bbox: List[int] = Field(default=[-1, -1, -1, -1], sa_type=JSON)
    web_hook: str

class RegIntEndpoint(SQLModel):
    direction: DirectionEnum = DirectionEnum.ENTER_AND_LEAVE
    target_class_ids: List[int] = Field(default=[0], sa_type=JSON)
    sensitivity: float = 0.3
    risk_frame_space: int = 25
    roi_bbox: List[int] = Field(default=[-1, -1, -1, -1], sa_type=JSON)
    web_hook: str

class EndpointBase(SQLModel):
    type: EndpointTypeEnum
    # vector: Optional[VectorEndpoint] = Field(
    #     default_factory=VectorEndpoint,
    #     sa_column=Column(JSON)
    # )
    # live_publish: Optional[LivePublishEndpoint] = Field(
    #     default_factory=LivePublishEndpoint,
    #     sa_column=Column(JSON)
    # )
    # vector: VectorEndpoint
    # live_publish: LivePublishEndpoint

class DefaultModelLibraryEnum(str, Enum):
    CUSTOM = 'custom'
    YOLOV5S = 'yolov5s'
    YOLOV11 = 'yolov11'

class ObjDetRun(SQLModel):
    # see@https://github.com/pydantic/pydantic/discussions/7121#discussioncomment-9762266
    model_config  = ConfigDict(protected_namespaces=())
    model_id: int = -1
    model_library: DefaultModelLibraryEnum = DefaultModelLibraryEnum.CUSTOM

class FacDetRun(SQLModel):
    pass

class FacRecRun(SQLModel):
    recognition_target_ids: List[str] = Field(default=['all'], sa_type=JSON)

class ImgEngRun(SQLModel):
    pass

class SubRegRun(SQLModel):
    roi_bbox: List[int] = Field(default=[-1, -1, -1, -1], sa_type=JSON)

class SpeRecRun(SQLModel):
    pass

class InferRunBase(SQLModel):
    type: ModelTypeEnum

class TaskBase(SQLModel):
    # media_data_id: str
    # media_name: str
    media_url: str
    media_type: MediaTypeEnum = MediaTypeEnum.UNKNOWN
    web_hook: str = ''
    # endpoints: List[EndpointBase] = Field(
    #     default_factory=list
    # )

    # def get_endpoint_by_type(self, endpoint_type: EndpointTypeEnum) -> EndpointBase:
    #     for endpoint in self.endpoints:
    #         if endpoint.type == endpoint_type:
    #             return endpoint
    #     return None