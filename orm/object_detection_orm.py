
from collections import defaultdict
from enum import Enum
import os
from pathlib import Path
from typing import List, Optional, Tuple
import numpy as np
from pydantic import ConfigDict
from sqlalchemy import <PERSON>SO<PERSON>, Column
from sqlmodel import Field, Relationship, SQLModel

from orm.model_orm import ModelLibraryEnum, ModelTypeEnum
from orm.step_rom import StepBase, StepTypeEnum
from orm.task_orm import CroGatEndpoint, EndpointBase, EndpointTypeEnum, LivePublishEndpoint, RegIntEndpoint, TaskBase, VectorEndpoint

class ObjDetVectorEndpoint(VectorEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="objdetendpoint.id")
    # 关系字段
    endpoint: Optional['ObjDetEndpoint'] = Relationship(back_populates="vector")

class ObjDetLivePublishEndpoint(LivePublishEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="objdetendpoint.id")
    # 关系字段
    endpoint: Optional['ObjDetEndpoint'] = Relationship(back_populates="live_publish")

class ObjDetCroGatEndpoint(CroGatEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="objdetendpoint.id")
    # 关系字段
    endpoint: Optional['ObjDetEndpoint'] = Relationship(back_populates="crowd_gathering")

class ObjDetRegIntEndpoint(RegIntEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="objdetendpoint.id")
    # 关系字段
    endpoint: Optional['ObjDetEndpoint'] = Relationship(back_populates="regional_intrusion")

class ObjDetEndpoint(EndpointBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    obj_det_task_id: int | None = Field(default=None, foreign_key='objdettask.id')
    obj_det_task: Optional['ObjDetTask'] = Relationship(back_populates='endpoints')
    vector: Optional[ObjDetVectorEndpoint] = Relationship(back_populates="endpoint")
    live_publish: Optional[ObjDetLivePublishEndpoint] = Relationship(back_populates="endpoint")
    crowd_gathering: Optional[ObjDetCroGatEndpoint] = Relationship(back_populates="endpoint")
    regional_intrusion: Optional[ObjDetRegIntEndpoint] = Relationship(back_populates="endpoint")

class ObjDetEndpointCreate(EndpointBase):
    vector: Optional[VectorEndpoint] = None
    live_publish: Optional[LivePublishEndpoint] = None
    crowd_gathering: Optional[CroGatEndpoint] = None
    regional_intrusion: Optional[RegIntEndpoint] = None

class ObjDetEndpointPublic(EndpointBase):
    vector: Optional[VectorEndpoint] = None
    live_publish: Optional[LivePublishEndpoint] = None
    crowd_gathering: Optional[CroGatEndpoint] = None
    regional_intrusion: Optional[RegIntEndpoint] = None

class ObjDetTaskStep(StepBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    obj_det_task_id: int | None = Field(default=None, foreign_key='objdettask.id')
    obj_det_task: Optional['ObjDetTask'] = Relationship(back_populates='steps')

class ObjDetModelLibraryEnum(str, Enum):
    CUSTOM = 'custom'
    YOLOV5 = 'yolov5s'
    YOLOV11 = 'yolov11'

class ObjDetTaskBase(TaskBase):
    # see@https://github.com/pydantic/pydantic/discussions/7121#discussioncomment-9762266
    model_config  = ConfigDict(protected_namespaces=())
    model_id: int
    model_library: ObjDetModelLibraryEnum = ObjDetModelLibraryEnum.CUSTOM

class ObjDetTask(ObjDetTaskBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    endpoints: List[ObjDetEndpoint] = Relationship(back_populates='obj_det_task')
    steps: List[ObjDetTaskStep] = Relationship(back_populates='obj_det_task')
    # private
    local_path: str = ''
    frame_folder_path: str = ''
    result_path: str = ''

    def get_step_id_by_type(self, step_type: StepTypeEnum):
        for step in self.steps:
            if step.type == step_type:
                return step.id
        return None
    
    def get_endpoint_by_type(self, endpoint_type: EndpointTypeEnum) -> ObjDetEndpoint:
        for endpoint in self.endpoints:
            if endpoint.type == endpoint_type:
                return endpoint
        return None
    
    def get_image_path_by_frame_index_and_pts(self, frame_index: int, frame_pts: int) -> Optional[str]:
        """
        遍历 frame_folder_path 中的图片，根据传入的 frame_index 和 frame_pts 匹配图片，返回图片地址。

        :param frame_index: 帧索引
        :param frame_pts: 帧时间戳
        :return: 匹配的图片地址，如果未找到则返回 None
        """
        for filename in os.listdir(self.frame_folder_path):
            base_name = os.path.splitext(filename)[0]
            parts = base_name.split('_')
            if len(parts) < 3:
                continue  # 跳过不符合格式的文件名
            try:
                image_frame_index = int(parts[1])
                image_frame_pts = int(parts[2])
                if image_frame_index == frame_index and image_frame_pts == frame_pts:
                    return os.path.join(self.frame_folder_path, filename)
            except ValueError:
                continue  # 跳过解析失败的文件名
        return None

class ObjDetTaskCreate(ObjDetTaskBase):
    endpoints: List[ObjDetEndpointCreate] = Field(default_factory=list)

class ObjDetTaskPublic(ObjDetTaskBase):
    id: int
    endpoints: List[ObjDetEndpointPublic] = []
    steps: List[StepBase] = []
    inference_result: List['ObjDetTaskFrameResult'] = []
    inference_result_url: str = ''

    def load_obj_det_result(self, result_path: str):
        path = Path(result_path)
        if not path.exists():
            print(f"识别结果文件不存在: {result_path}")
            return
        with open(result_path, 'r') as f:
            lines = f.readlines()
            for line in lines:
                parts = line.strip().split(',')
                if len(parts) < 8:
                    continue  # 跳过不完整行
                image_name, left, top, right, bottom, class_index, class_name, score = parts
                image_name = image_name.split('.')[0]  # 去掉扩展名
                frame_result = ObjDetTaskFrameResult(
                    frame_index=int(image_name.split('_')[1]),
                    frame_pts=int(image_name.split('_')[2]),
                    bbox= [int(left), int(top), int(right), int(bottom)],
                    class_index=int(class_index),
                    class_name=class_name,
                    score=float(score)
                )
                self.inference_result.append(frame_result)

    def get_yolo_result_by_image_name(self, image_name: str) -> np.ndarray:
        """
        根据 image_name 返回 YOLO 风格的识别结果数组。
        每项格式为：[left, top, right, bottom, score, class_index]

        若无结果，返回空数组，shape 为 (0, 6)
        """
        try:
            image_name = image_name.split('.')[0]  # 去掉扩展名
            parts = image_name.split('_')
            if len(parts) != 3:
                print(f"非法 image_name 格式: {image_name}")
                return np.empty((0, 6), dtype=np.float32)

            frame_index = int(parts[1])
            frame_pts = int(parts[2])

            matched = [
                r for r in self.inference_result
                if r.frame_index == frame_index and r.frame_pts == frame_pts
            ]

            if not matched:
                return np.empty((0, 6), dtype=np.float32)

            result_array = np.array([
                [
                    float(r.bbox[0]),
                    float(r.bbox[1]),
                    float(r.bbox[2]),
                    float(r.bbox[3]),
                    float(r.score),
                    float(r.class_index)
                ]
                for r in matched
            ], dtype=np.float32)

            return result_array

        except Exception as e:
            print(f"获取识别结果失败: {e}")
            return np.empty((0, 6), dtype=np.float32)
            
    def get_person_bboxes(self) -> List[Tuple[int, int, List[List[int]]]]:
        """
        获取每一帧中 class_name 为 'person' 的 bbox 列表，按帧聚合
        :return: List of tuples: (frame_index, frame_pts, List of bboxes)
        """
        frame_dict = defaultdict(lambda: {"pts": None, "bboxes": []})

        for result in self.inference_result:
            if result.class_name == "person":
                frame_index = result.frame_index
                frame_dict[frame_index]["pts"] = result.frame_pts
                frame_dict[frame_index]["bboxes"].append(result.bbox)

        # 转换为 list[tuple]
        result_list = [
            (frame_index, data["pts"], data["bboxes"])
            for frame_index, data in sorted(frame_dict.items())
        ]
        return result_list
    
    # def find_result_by_image_name(self, image_name: str) -> Optional['ObjDetTaskFrameResult']:
    #     """
    #     通过 image_name 查找对应的 ObjDetTaskFrameResult。
    #     image_name 格式应为 'frame_<frame_index>_<frame_pts>'
    #     """
    #     try:
    #         base_name = image_name.split('.')[0]
    #         frame_index = int(base_name.split('_')[1])
    #         frame_pts = int(base_name.split('_')[2])
    #     except (IndexError, ValueError):
    #         print(f"无法从 image_name 解析 frame_index 和 frame_pts: {image_name}")
    #         return None

    #     for result in self.inference_result:
    #         if result.frame_index == frame_index and result.frame_pts == frame_pts:
    #             return result

    #     print(f"未找到匹配结果: {image_name}")
    #     return None


class ObjDetTaskFrameResult(SQLModel):
    frame_index: int
    frame_pts: int
    # 左上角坐标(left, top), 右下角坐标(right, bottom)
    bbox: list[int] 
    # 左眼坐标，右眼坐标，鼻子坐标，左嘴角坐标，右嘴角坐标
    # kps: list[list[int]] 
    class_index: int
    class_name: str
    score: float
