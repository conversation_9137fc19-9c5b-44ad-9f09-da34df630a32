
import csv
from pathlib import Path
from typing import List, Optional
import numpy as np
from pydantic import ConfigDict
from sqlmodel import Field, Relationship, SQLModel

from orm.media_orm import MediaTypeEnum
from orm.model_orm import ModelTypeEnum
from orm.step_rom import StepBase, StepTypeEnum
from orm.task_orm import EndpointBase, EndpointTypeEnum, LivePublishEndpoint, TaskBase, VectorEndpoint

class FacDetVectorEndpoint(VectorEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="facdetendpoint.id")
    # 关系字段
    endpoint: Optional['FacDetEndpoint'] = Relationship(back_populates="vector")

class FacDetLivePublishEndpoint(LivePublishEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="facdetendpoint.id")
    # 关系字段
    endpoint: Optional['FacDetEndpoint'] = Relationship(back_populates="live_publish")

class FacDetEndpoint(EndpointBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    fac_det_task_id: int | None = Field(default=None, foreign_key='facdettask.id')
    fac_det_task: Optional['FacDetTask'] = Relationship(back_populates='endpoints')
    vector: Optional[FacDetVectorEndpoint] = Relationship(back_populates="endpoint")
    live_publish: Optional[FacDetLivePublishEndpoint] = Relationship(back_populates="endpoint")

class FacDetEndpointCreate(EndpointBase):
    vector: Optional[VectorEndpoint] = None
    live_publish: Optional[LivePublishEndpoint] = None

class FacDetEndpointPublic(EndpointBase):
    vector: Optional[VectorEndpoint] = None
    live_publish: Optional[LivePublishEndpoint] = None

class FacDetTaskStep(StepBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    fac_det_task_id: int | None = Field(default=None, foreign_key='facdettask.id')
    fac_det_task: Optional['FacDetTask'] = Relationship(back_populates='steps')

class FacDetTaskBase(TaskBase):
    # see@https://github.com/pydantic/pydantic/discussions/7121#discussioncomment-9762266
    # model_config  = ConfigDict(protected_namespaces=())
    # model_id: int
    pass

class FacDetTask(FacDetTaskBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    endpoints: List[FacDetEndpoint] = Relationship(back_populates='fac_det_task')
    steps: List[FacDetTaskStep] = Relationship(back_populates='fac_det_task')
    # private
    local_path: str = ''
    frame_folder_path: str = ''
    result_path: str = ''

    def get_step_id_by_type(self, step_type: StepTypeEnum):
        for step in self.steps:
            if step.type == step_type:
                return step.id
        return None
    def get_endpoint_by_type(self, endpoint_type: EndpointTypeEnum) -> FacDetEndpoint:
        for endpoint in self.endpoints:
            if endpoint.type == endpoint_type:
                return endpoint
        return None


class FacDetTaskCreate(FacDetTaskBase):
    endpoints: List[FacDetEndpointCreate] = Field(default_factory=list)

class FacDetTaskPublic(FacDetTaskBase):
    id: int
    endpoints: List[FacDetEndpointPublic] = []
    steps: List[StepBase] = []
    inference_result: List['FacDetTaskFrameResult'] = []
    inference_result_url: str = ''

    def load_fac_det_result(self, result_path: str):
        """
        从CSV文件加载人脸检测结果
        
        Args:
            result_path (str): 结果文件路径
        """
        path = Path(result_path)
        if not path.exists():
            print(f"识别结果文件不存在: {result_path}")
            return
        
        try:
            with open(result_path, 'r', newline='') as f:
                reader = csv.reader(f)
                
                # 跳过表头
                header = next(reader, None)
                if not header:
                    print(f"结果文件为空: {result_path}")
                    return
                
                # 检查表头格式是否正确
                expected_header = ['image_name', 'left', 'top', 'right', 'bottom', 
                                  'landmark_x1', 'landmark_y1', 'landmark_x2', 'landmark_y2', 
                                  'landmark_x3', 'landmark_y3', 'landmark_x4', 'landmark_y4', 
                                  'landmark_x5', 'landmark_y5', 'score']
                if header != expected_header:
                    print(f"结果文件格式不正确: {result_path}")
                    # 尝试继续处理，假设列的顺序是正确的
                
                # 读取数据行
                for row in reader:
                    try:
                        # 确保行有足够的元素
                        if len(row) < 16:  # 需要至少16个字段
                            print(f"跳过不完整行: {row}")
                            continue
                        
                        # 解析图像名称
                        image_name = row[0]
                        
                        # 提取帧索引和时间戳
                        image_name_parts = image_name.split('.')[0].split('_')
                        if len(image_name_parts) >= 3:
                            frame_index = int(image_name_parts[1])
                            frame_pts = int(image_name_parts[2])
                        else:
                            print(f"无法从图像名称提取帧信息: {image_name}")
                            continue
                        
                        # 解析边界框坐标
                        left = int(float(row[1]))
                        top = int(float(row[2]))
                        right = int(float(row[3]))
                        bottom = int(float(row[4]))
                        bbox = [left, top, right, bottom]
                        
                        # 解析关键点坐标
                        kps = [
                            [int(float(row[5])), int(float(row[6]))],  # 左眼
                            [int(float(row[7])), int(float(row[8]))],  # 右眼
                            [int(float(row[9])), int(float(row[10]))],  # 鼻子
                            [int(float(row[11])), int(float(row[12]))],  # 左嘴角
                            [int(float(row[13])), int(float(row[14]))]   # 右嘴角
                        ]
                        
                        # 解析置信度
                        score = float(row[15])
                        
                        # 创建结果对象
                        frame_result = FacDetTaskFrameResult(
                            frame_index=frame_index,
                            frame_pts=frame_pts,
                            bbox=bbox,
                            kps=kps,
                            score=score
                        )
                        self.inference_result.append(frame_result)
                    except Exception as e:
                        print(f"解析行时出错: {e}, 行: {row}")
                        continue
        except Exception as e:
            print(f"读取结果文件时出错: {e}")
        
        print(f"从 {result_path} 加载了 {len(self.inference_result)} 条人脸检测结果")

    def get_face_result_by_image_name(self, image_name: str) -> np.ndarray:
        """
        根据 image_name 返回人脸检测结果数组。
        每项格式为：
            [left, top, right, bottom, kps_x1, kps_y1, ..., kps_x5, kps_y5, score]

        若无结果，返回空数组，shape 为 (0, 15)
        """
        try:
            image_name = image_name.split('.')[0]  # 去掉扩展名
            parts = image_name.split('_')
            if len(parts) != 3:
                print(f"非法 image_name 格式: {image_name}")
                return np.empty((0, 15), dtype=np.float32)

            frame_index = int(parts[1])
            frame_pts = int(parts[2])

            matched = [
                r for r in self.inference_result
                if r.frame_index == frame_index and r.frame_pts == frame_pts
            ]

            if not matched:
                return np.empty((0, 15), dtype=np.float32)

            result_array = np.array([
                [
                    float(r.bbox[0]),
                    float(r.bbox[1]),
                    float(r.bbox[2]),
                    float(r.bbox[3]),
                    float(r.kps[0][0]),
                    float(r.kps[0][1]),
                    float(r.kps[1][0]),
                    float(r.kps[1][1]),
                    float(r.kps[2][0]),
                    float(r.kps[2][1]),
                    float(r.kps[3][0]),
                    float(r.kps[3][1]),
                    float(r.kps[4][0]),
                    float(r.kps[4][1]),
                    float(r.score),
                ]
                for r in matched
            ], dtype=np.float32)

            return result_array

        except Exception as e:
            print(f"获取人脸识别结果失败: {e}")
            return np.empty((0, 15), dtype=np.float32)

    # def find_result_by_image_name(self, image_name: str) -> Optional['FacDetTaskFrameResult']:
    #     """
    #     通过 image_name 查找对应的 FacDetTaskFrameResult。
    #     image_name 格式应为 'frame_<frame_index>_<frame_pts>'
    #     """
    #     try:
    #         base_name = image_name.split('.')[0]
    #         frame_index = int(base_name.split('_')[1])
    #         frame_pts = int(base_name.split('_')[2])
    #     except (IndexError, ValueError):
    #         print(f"无法从 image_name 解析 frame_index 和 frame_pts: {image_name}")
    #         return None

    #     for result in self.inference_result:
    #         if result.frame_index == frame_index and result.frame_pts == frame_pts:
    #             return result

    #     print(f"未找到匹配结果: {image_name}")
    #     return None

class FacDetTaskFrameResult(SQLModel):
    frame_index: int
    frame_pts: int
    # 左上角坐标(left, top), 右下角坐标(right, bottom)
    bbox: list[int]
    # 左眼坐标，右眼坐标，鼻子坐标，左嘴角坐标，右嘴角坐标
    kps: list[list[int]]
    score: float
