
from enum import Enum
from typing import List, Optional
from pydantic import ConfigDict
from sqlmodel import Field, Relationship, SQLModel

from orm.model_orm import ModelLibraryEnum, ModelTypeEnum
from orm.step_rom import StepBase, StepTypeEnum

class DatasetFormatEnum(str, Enum):
    yolo_with_images = 'yolo_with_images'

class TrainTaskStep(StepBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    train_task_id: int | None = Field(default=None, foreign_key='traintask.id')
    train_task: Optional['TrainTask'] = Relationship(back_populates='steps')

class TrainTaskBase(SQLModel):
    model_config  = ConfigDict(protected_namespaces=())
    dataset_id: str
    dataset_format: DatasetFormatEnum
    dataset_url: str
    model_library: ModelLibraryEnum
    web_hook: str = ''

class TrainTask(TrainTaskBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    steps: List[TrainTaskStep] = Relationship(back_populates='train_task')
    # private
    local_path: str = ''
    dataset_path: str = ''
    config_path: str = ''
    export_model_path: str = ''

    def get_step_id_by_type(self, step_type: StepTypeEnum):
        for step in self.steps:
            if step.type == step_type:
                return step.id
        return None


class TrainTaskCreate(TrainTaskBase):
    pass

class TrainTaskPublic(TrainTaskBase):
    id: int
    model_url: str = ''
    steps: List[StepBase] = []