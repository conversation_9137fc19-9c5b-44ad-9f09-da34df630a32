
from collections import defaultdict
from enum import Enum
import os
from pathlib import Path
from typing import List, Optional, Tuple
import numpy as np
from pydantic import ConfigDict
from sqlalchemy import JSO<PERSON>, Column
from sqlmodel import Field, Relationship, SQLModel

from onnx_sessions import face_detection
from orm.model_orm import ModelLibraryEnum, ModelTypeEnum
from orm.step_rom import StepBase, StepTypeEnum
from orm.task_orm import CroGatEndpoint, EndpointBase, EndpointTypeEnum, FacDetRun, FacRecRun, ImgEngRun, InferRunBase, LivePublishEndpoint, ObjDetRun, RegIntEndpoint, SpeRecRun, SubRegRun, TaskBase, VectorEndpoint

# InferEndpoint
class InferVectorEndpoint(VectorEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="inferendpoint.id")
    # 关系字段
    endpoint: Optional['InferEndpoint'] = Relationship(back_populates="vector")

class InferLivePublishEndpoint(LivePublishEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="inferendpoint.id")
    # 关系字段
    endpoint: Optional['InferEndpoint'] = Relationship(back_populates="live_publish")

class InferCroGatEndpoint(CroGatEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="inferendpoint.id")
    # 关系字段
    endpoint: Optional['InferEndpoint'] = Relationship(back_populates="crowd_gathering")

class InferRegIntEndpoint(RegIntEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="inferendpoint.id")
    # 关系字段
    endpoint: Optional['InferEndpoint'] = Relationship(back_populates="regional_intrusion")

class InferEndpoint(EndpointBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    infer_task_id: int | None = Field(default=None, foreign_key='infertask.id')
    infer_task: Optional['InferTask'] = Relationship(back_populates='endpoints')
    vector: Optional[InferVectorEndpoint] = Relationship(back_populates="endpoint")
    live_publish: Optional[InferLivePublishEndpoint] = Relationship(back_populates="endpoint")
    crowd_gathering: Optional[InferCroGatEndpoint] = Relationship(back_populates="endpoint")
    regional_intrusion: Optional[InferRegIntEndpoint] = Relationship(back_populates="endpoint")

class InferEndpointCreate(EndpointBase):
    vector: Optional[VectorEndpoint] = None
    live_publish: Optional[LivePublishEndpoint] = None
    crowd_gathering: Optional[CroGatEndpoint] = None
    regional_intrusion: Optional[RegIntEndpoint] = None

class InferEndpointPublic(EndpointBase):
    vector: Optional[VectorEndpoint] = None
    live_publish: Optional[LivePublishEndpoint] = None
    crowd_gathering: Optional[CroGatEndpoint] = None
    regional_intrusion: Optional[RegIntEndpoint] = None

# InferRun
class InferObjDetRun(ObjDetRun, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    run_id: Optional[int] = Field(default=None, foreign_key="inferrun.id")
    # 关系字段
    infer_run: Optional['InferRun'] = Relationship(back_populates="object_detection")

class InferFacDetRun(FacDetRun, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    run_id: Optional[int] = Field(default=None, foreign_key="inferrun.id")
    # 关系字段
    infer_run: Optional['InferRun'] = Relationship(back_populates="face_detection")

class InferFacRecRun(FacRecRun, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    run_id: Optional[int] = Field(default=None, foreign_key="inferrun.id")
    # 关系字段
    infer_run: Optional['InferRun'] = Relationship(back_populates="face_recognition")

class InferImgEngRun(ImgEngRun, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    run_id: Optional[int] = Field(default=None, foreign_key="inferrun.id")
    # 关系字段
    infer_run: Optional['InferRun'] = Relationship(back_populates="image_enhance")

class InferSubRegRun(SubRegRun, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    run_id: Optional[int] = Field(default=None, foreign_key="inferrun.id")
    # 关系字段
    infer_run: Optional['InferRun'] = Relationship(back_populates="subtitle_recognition")

class InferSpeRecRun(SpeRecRun, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    run_id: Optional[int] = Field(default=None, foreign_key="inferrun.id")
    # 关系字段
    infer_run: Optional['InferRun'] = Relationship(back_populates="speech_recognition")

class InferRun(InferRunBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    infer_task_id: int | None = Field(default=None, foreign_key='infertask.id')
    infer_task: Optional['InferTask'] = Relationship(back_populates='runs')
    object_detection: Optional[InferObjDetRun] = Relationship(back_populates="infer_run")
    face_detection: Optional[InferFacDetRun] = Relationship(back_populates="infer_run")
    face_recognition: Optional[InferFacRecRun] = Relationship(back_populates="infer_run")
    image_enhance: Optional[InferImgEngRun] = Relationship(back_populates="infer_run")
    subtitle_recognition: Optional[InferSubRegRun] = Relationship(back_populates="infer_run")
    speech_recognition: Optional[InferSpeRecRun] = Relationship(back_populates="infer_run")
    # private
    result_path: str = ''


class InferRunCreate(InferRunBase):
    object_detection: Optional[ObjDetRun] = None
    face_detection: Optional[FacDetRun] = None
    face_recognition: Optional[FacRecRun] = None
    image_enhance: Optional[ImgEngRun] = None
    subtitle_recognition: Optional[SubRegRun] = None
    speech_recognition: Optional[SpeRecRun] = None

class InferRunPublic(InferRunBase):
    object_detection: Optional[ObjDetRun] = None
    face_detection: Optional[FacDetRun] = None
    face_recognition: Optional[FacRecRun] = None
    image_enhance: Optional[ImgEngRun] = None
    subtitle_recognition: Optional[SubRegRun] = None
    speech_recognition: Optional[SpeRecRun] = None
    inference_result: List[dict] = []

# InferTaskStep
class InferTaskStep(StepBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    infer_task_id: int | None = Field(default=None, foreign_key='infertask.id')
    infer_task: Optional['InferTask'] = Relationship(back_populates='steps')

# InferTask
class InferTask(TaskBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    steps: List[InferTaskStep] = Relationship(back_populates='infer_task')
    runs: List[InferRun] = Relationship(back_populates='infer_task')
    endpoints: List[InferEndpoint] = Relationship(back_populates='infer_task')
    # private
    local_path: str = ''
    frame_folder_path: str = ''
    audio_folder_path: str = ''
    download_media_path: str = ''

    def get_step_id_by_type(self, step_type: StepTypeEnum):
        for step in self.steps:
            if step.type == step_type:
                return step.id
        return None
    
    def get_step_ids_by_types(self, step_types: List[StepTypeEnum]) -> List[int]:
        """
        获取指定类型的步骤ID列表
        
        :param step_types: 步骤类型列表
        :return: 步骤ID列表
        """
        step_ids = []
        for step in self.steps:
            if step.type in step_types:
                step_ids.append(step.id)
        return step_ids

    def get_run_by_type(self, run_type: ModelTypeEnum) -> Optional[InferRun]:
        for run in self.runs:
            if run.type == run_type:
                return run
        return None
    
    def get_endpoint_by_type(self, endpoint_type: EndpointTypeEnum) -> InferEndpoint:
        for endpoint in self.endpoints:
            if endpoint.type == endpoint_type:
                return endpoint
        return None
    
    def get_image_path_by_frame_index_and_pts(self, frame_index: int, frame_pts: int) -> Optional[str]:
        """
        遍历 frame_folder_path 中的图片，根据传入的 frame_index 和 frame_pts 匹配图片，返回图片地址。

        :param frame_index: 帧索引
        :param frame_pts: 帧时间戳
        :return: 匹配的图片地址，如果未找到则返回 None
        """
        for filename in os.listdir(self.frame_folder_path):
            base_name = os.path.splitext(filename)[0]
            parts = base_name.split('_')
            if len(parts) < 3:
                continue  # 跳过不符合格式的文件名
            try:
                image_frame_index = int(parts[1])
                image_frame_pts = int(parts[2])
                if image_frame_index == frame_index and image_frame_pts == frame_pts:
                    return os.path.join(self.frame_folder_path, filename)
            except ValueError:
                continue  # 跳过解析失败的文件名
        return None

class InferTaskCreate(TaskBase):
    runs: List[InferRunCreate] = Field(default_factory=list)
    endpoints: List[InferEndpointCreate] = Field(default_factory=list)

class InferTaskPublic(TaskBase):
    id: int
    steps: List[StepBase] = []
    runs: List[InferRunPublic] = []
    endpoints: List[InferEndpointPublic] = []

    def set_run_infer_result(self, run_type: ModelTypeEnum, result: list[SQLModel]):
        for run in self.runs:
            if run.type == run_type:
                run.inference_result = [item.model_dump() for item in result]
                break

class ObjDetFrameResult(SQLModel):
    frame_index: int
    frame_pts: int
    # 左上角坐标(left, top), 右下角坐标(right, bottom)
    bbox: list[int] 
    # 左眼坐标，右眼坐标，鼻子坐标，左嘴角坐标，右嘴角坐标
    # kps: list[list[int]] 
    class_index: int
    class_name: str
    score: float
    download_url: str = ""

class FacDetFrameResult(SQLModel):
    frame_index: int
    frame_pts: int
    # 左上角坐标(left, top), 右下角坐标(right, bottom)
    bbox: list[int]
    # 左眼坐标，右眼坐标，鼻子坐标，左嘴角坐标，右嘴角坐标
    kps: list[list[int]]
    score: float
    download_url: str = ""

class FacRecFrameResult(FacDetFrameResult):
    class_id: str
    class_name: str
    rec_score: float

class ImgEnhFrameResult(SQLModel):
    download_url: str = ""

class TextDetFrameResult(SQLModel):
    frame_index: int
    frame_pts: int
    polygon: list[int]
    # download_url: str = ""

class TextRegFrameResult(TextDetFrameResult):
    class_index: int
    class_name: str
    score: float

class SubtitleRegResult(SQLModel):
    # [start_frame_index, end_frame_index]
    frame_index: list[int]
    # [start_frame_pts, end_frame_pts]
    frame_pts: list[int]
    polygon: list[int]
    text: str
    score: float

class SpeechRecResult(SQLModel):
    text: str
    start_time: float
    end_time: float