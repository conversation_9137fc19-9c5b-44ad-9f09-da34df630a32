

from enum import Enum
from typing import List, Optional
from pydantic import ConfigDict
from sqlmodel import Relationship, SQLModel, Field

from orm.step_rom import StepBase, StepTypeEnum

class ModelTypeEnum(str, Enum):
    denoiser = 'denoiser'
    OBJECT_DETECTION = 'object_detection'
    FACE_DETECTION = 'face_detection'
    FACE_RECOGNITION = 'face_recognition'
    IMAGE_ENHANCE = 'image_enhance'
    SUBTITLE_RECOGNITION = 'subtitle_recognition'
    SPEECH_RECOGNITION = 'speech_recognition'

class ModelLibraryEnum(str, Enum):
    yolov5 = 'yolov5'
    yolov11 = 'yolov11'
    duguang_ocr_det = 'duguang_ocr_det'
    duguang_ocr_reg = 'duguang_ocr_reg'

class ModelStep(StepBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    # see@https://github.com/pydantic/pydantic/discussions/7121#discussioncomment-9762266
    model_config  = ConfigDict(protected_namespaces=())
    id: int | None = Field(default=None, primary_key=True)
    model_id: int | None = Field(default=None, foreign_key='model.id')
    model: Optional['Model'] = Relationship(back_populates='steps')

class ModelBase(SQLModel):
    name: str
    type: ModelTypeEnum
    library: ModelLibraryEnum
    url: str
    extend: str = Field(default='', description='针对目标识别模型，例如yolo，传入将class list作为classes的value的json字符串，'\
                        '例如："{\"classes\":[\"person\",\"bicycle\"]}"')
    web_hook: str = ''

class Model(ModelBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    model_config  = ConfigDict(protected_namespaces=())
    id: int | None = Field(default=None, primary_key=True)
    steps: List[ModelStep] = Relationship(back_populates='model')
    # private
    local_path: str = ''
    model_path: str = ''

    def get_step_id_by_type(self, step_type: StepTypeEnum):
        for step in self.steps:
            if step.type == step_type:
                return step.id
        return None

class ModelCreate(ModelBase):
    model_config  = ConfigDict(protected_namespaces=())
    # private
    local_path: str = ''
    model_path: str = ''

class ModelPublic(ModelBase):
    id: int
    steps: List[StepBase] = []