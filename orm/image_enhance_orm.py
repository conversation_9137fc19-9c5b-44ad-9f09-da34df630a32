
from typing import Any, Dict, List, Optional
from pydantic import ConfigD<PERSON>
from sqlmodel import Field, Relationship, SQLModel

from orm.media_orm import MediaTypeEnum
from orm.model_orm import ModelTypeEnum
from orm.step_rom import StepBase, StepTypeEnum
from orm.task_orm import EndpointBase, EndpointTypeEnum, LivePublishEndpoint, TaskBase, VectorEndpoint

class ImgEnhVectorEndpoint(VectorEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="imgenhendpoint.id")
    # 关系字段
    endpoint: Optional['ImgEnhEndpoint'] = Relationship(back_populates="vector")

class ImgEnhLivePublishEndpoint(LivePublishEndpoint, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # 外键字段
    endpoint_id: Optional[int] = Field(default=None, foreign_key="imgenhendpoint.id")
    # 关系字段
    endpoint: Optional['ImgEnhEndpoint'] = Relationship(back_populates="live_publish")

class ImgEnhEndpoint(EndpointBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    img_enh_task_id: int | None = Field(default=None, foreign_key='imgenhtask.id')
    img_enh_task: Optional['ImgEnhTask'] = Relationship(back_populates='endpoints')
    vector: Optional[ImgEnhVectorEndpoint] = Relationship(back_populates="endpoint")
    live_publish: Optional[ImgEnhLivePublishEndpoint] = Relationship(back_populates="endpoint")

class ImgEnhEndpointCreate(EndpointBase):
    vector: Optional[VectorEndpoint] = None
    live_publish: Optional[LivePublishEndpoint] = None

class ImgEnhEndpointPublic(EndpointBase):
    vector: Optional[VectorEndpoint] = None
    live_publish: Optional[LivePublishEndpoint] = None

class ImgEnhTaskStep(StepBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    img_enh_task_id: int | None = Field(default=None, foreign_key='imgenhtask.id')
    img_enh_task: Optional['ImgEnhTask'] = Relationship(back_populates='steps')

class ImgEnhTaskBase(TaskBase):
    pass

class ImgEnhTask(ImgEnhTaskBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    endpoints: List[ImgEnhEndpoint] = Relationship(back_populates='img_enh_task')
    steps: List[ImgEnhTaskStep] = Relationship(back_populates='img_enh_task')
    # private
    local_path: str = ''
    frame_folder_path: str = ''
    result_path: str = ''

    def get_step_id_by_type(self, step_type: StepTypeEnum):
        for step in self.steps:
            if step.type == step_type:
                return step.id
        return None

    def get_endpoint_by_type(self, endpoint_type: EndpointTypeEnum) -> ImgEnhEndpoint:
        for endpoint in self.endpoints:
            if endpoint.type == endpoint_type:
                return endpoint
        return None


class ImgEnhTaskCreate(ImgEnhTaskBase):
    endpoints: List[ImgEnhEndpointCreate] = Field(default_factory=list)

class ImgEnhTaskPublic(ImgEnhTaskBase):
    id: int
    endpoints: List[ImgEnhEndpointPublic] = []
    steps: List[StepBase] = []
    inference_result_url: str = ''

    # def load_fac_det_result(self, result_path: str):
    #     with open(result_path, 'r') as f:
    #         lines = f.readlines()
    #         for line in lines:
    #             parts = line.strip().split(',')
    #             if len(parts) < 8:
    #                 continue  # 跳过不完整行
    #             image_name, left, top, right, bottom, kps_x1, kps_y1, kps_x2, kps_y2, kps_x3, kps_y3, kps_x4, kps_y4, kps_x5, kps_y5, score = parts
    #             image_name = image_name.split('.')[0]  # 去掉扩展名
    #             frame_result = ImgEnhTaskFrameResult(
    #                 frame_index=int(image_name.split('_')[1]),
    #                 frame_pts=int(image_name.split('_')[2]),
    #                 bbox=[int(left), int(top), int(right), int(bottom)],
    #                 kps=[[int(kps_x1), int(kps_y1)], [int(kps_x2), int(kps_y2)], [int(kps_x3), int(kps_y3)], [int(kps_x4), int(kps_y4)], [int(kps_x5), int(kps_y5)]],
    #                 score=float(score)
    #             )
    #             self.inference_result.append(frame_result)


# class ImgEnhTaskFrameResult(SQLModel):
#     frame_index: int
#     frame_pts: int
#     # 左上角坐标(left, top), 右下角坐标(right, bottom)
#     bbox: list[int]
#     # 左眼坐标，右眼坐标，鼻子坐标，左嘴角坐标，右嘴角坐标
#     kps: list[list[int]]
#     score: float
