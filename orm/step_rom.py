from datetime import datetime
from enum import Enum

from sqlmodel import Field, SQLModel
# 枚举定义
class StepTypeEnum(str, Enum):
    download = 'download'
    decode = 'decode'
    encode = 'encode'
    transcode = 'transcode'
    inference = 'inference'
    train = 'train'
    vector = 'vector'
    notify = 'notify'
    # inference runs
    object_detection = 'inference_object_detection'
    face_detection = 'inference_face_detection'
    face_recognition = 'inference_face_recognition'
    image_enhance = 'inference_image_enhance'
    subtitle_recognition = 'inference_subtitle_recognition'
    speech_recognition = 'inference_speech_recognition'
    # endpoints
    live_publish = 'live_publish'
    crowd_gathering = 'crowd_gathering'
    regional_intrusion = 'regional_intrusion'

class StepStatusEnum(str, Enum):
    queued = 'queued'
    in_progress = 'in_progress'
    success = 'success'
    failed = 'failed'

class RunTypeEnum(str, Enum):
    denoiser = 'denoiser'
    object_detection = 'object_detection'

# Step类
class StepBase(SQLModel):
    type: StepTypeEnum
    status: StepStatusEnum
    created_at: str
    updated_at: str
    progress: float = 0.0  # 执行进度比例，0.0-1.0
    message: str = ""      # 可选的状态消息
    
    @classmethod
    def create_step(cls, type: StepTypeEnum):
        now = datetime.now().isoformat()
        step = cls()
        step.type = type
        step.status = StepStatusEnum.queued
        step.created_at = now
        step.updated_at = now
        step.progress = 0.0
        step.message = ""
        return step

    def update_status(self, status: StepStatusEnum, progress: float = None, message: str = None):
        """
        更新步骤状态、进度和消息
        
        :param status: 新的状态
        :param progress: 可选的进度值 (0.0-1.0)
        :param message: 可选的状态消息
        """
        self.status = status
        self.updated_at = datetime.now().isoformat()
        
        # 根据状态自动设置进度
        if progress is not None:
            self.progress = max(0.0, min(1.0, progress))  # 确保进度在0-1之间
        elif status == StepStatusEnum.queued:
            self.progress = 0.0
        elif status == StepStatusEnum.in_progress and self.progress == 0.0:
            self.progress = 0.01  # 刚开始进行中
        elif status == StepStatusEnum.success:
            self.progress = 1.0
        
        # 更新消息
        if message is not None:
            self.message = message

    def update_progress(self, progress: float, message: str = None):
        """
        仅更新进度和消息，不改变状态
        
        :param progress: 新的进度值 (0.0-1.0)
        :param message: 可选的状态消息
        """
        self.progress = round(max(0.0, min(1.0, progress)), 2)  # 确保进度在0-1之间
        self.updated_at = datetime.now().isoformat()
        
        if message is not None:
            self.message = message
        
        # 如果进度大于0但状态仍为queued，自动更新为in_progress
        if self.progress > 0 and self.status == StepStatusEnum.queued:
            self.status = StepStatusEnum.in_progress

