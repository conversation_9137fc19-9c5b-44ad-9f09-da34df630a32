
from enum import Enum
from typing import List, Optional
from sqlmodel import Field, Relationship, SQLModel


class MediaTypeEnum(str, Enum):
    IMAGE = 'image'
    VIDEO = 'video'
    AUDIO = 'audio'
    STREAM = 'stream'
    FILE = 'file'
    UNKNOWN = 'unknown'

# class MediaStep(StepBase, table=True):
#     __table_args__ = {'sqlite_autoincrement': True}
#     id: int | None = Field(default=None, primary_key=True)
#     media_id: int | None = Field(default=None, foreign_key='media.id')
#     media: Optional['Media'] = Relationship(back_populates='steps')

FUSED_VIRTUAL_MEDIA_DATA_ID = 'fused_recognition_vector'

class MediaExtend(SQLModel):
    recognition_target_id: str = ''
    recognition_target_name: str = ''

class MediaMetadata(SQLModel):
    media_data_id: str
    media_name: str
    media_type: MediaTypeEnum
    media_extend: str = ''

class MediaBase(MediaMetadata):
    media_url: str

# class Media(MediaBase, table=True):
#     __table_args__ = {'sqlite_autoincrement': True}
#     id: int | None = Field(default=None, primary_key=True)
#     steps: List[MediaStep] = Relationship(back_populates='media')
#     # private
#     local_path: str = ''
#     frame_folder_path: str = ''

#     def get_step_id_by_type(self, step_type: StepTypeEnum):
#         for step in self.steps:
#             if step.type == step_type:
#                 return step.id
#         return None

# class MediaCreate(MediaBase):
#     pass

# class MediaPublic(MediaBase):
#     steps: List[StepBase] = []
