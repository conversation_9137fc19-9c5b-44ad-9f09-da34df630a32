
from enum import Enum
from typing import List, Optional
from pydantic import <PERSON>fig<PERSON><PERSON>
from sqlalchemy import JSO<PERSON>, Column
from sqlmodel import Field, Relationship, SQLModel

from orm.media_orm import MediaBase, MediaTypeEnum
from orm.step_rom import StepBase, StepTypeEnum
from orm.task_orm import DefaultModelLibraryEnum

class VectorCollectionEnum(str, Enum):
    image_resnet50 = 'image_resnet50'
    image_vgg = 'image_vgg'
    image_insightface = 'image_insightface'
    IMAGE_INSIGHTFACE_DATASET = 'image_insightface_dataset'

class VectorMediaStep(StepBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    vector_media_id: int | None = Field(default=None, foreign_key='vectormedia.id')
    vector_media: Optional['VectorMedia'] = Relationship(back_populates='steps')

# class VectorMediaResultBase(SQLModel):
#     collection: VectorCollectionEnum
#     ids: List[int] = Field(default=[], sa_type=JSON)

# class VectorMediaResult(VectorMediaResultBase, table=True):
#     __table_args__ = {'sqlite_autoincrement': True}
#     id: int | None = Field(default=None, primary_key=True)
#     vector_media_id: int | None = Field(default=None, foreign_key='vectormedia.id')
#     vector_media: Optional['VectorMedia'] = Relationship(back_populates='vector_result')

class VectorMedia(MediaBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    # vector_result: List[VectorMediaResult] = Relationship(back_populates='vector_media')
    steps: List[VectorMediaStep] = Relationship(back_populates='vector_media')
    vector_task_id: int | None = Field(default=None, foreign_key='vectortask.id')
    vector_task: Optional['VectorTask'] = Relationship(back_populates='medias')
    # private
    local_path: str = ''
    frame_folder_path: str = ''
    # alembic test
    # test_add_sss: int = Field(default=0, sa_column_kwargs={'server_default': '0'})

    def get_step_id_by_type(self, step_type: StepTypeEnum):
        for step in self.steps:
            if step.type == step_type:
                return step.id
        return None

    def get_step_by_type(self, step_type: StepTypeEnum):
        for step in self.steps:
            if step.type == step_type:
                return step
        return None
    
class VectorMediaCreate(MediaBase):
    pass

class VectorMediaPublic(MediaBase):
    # vector_result: List[VectorMediaResultBase] = []
    steps: List[StepBase] = []


class VectorTaskBase(SQLModel):
    web_hook: str = ''
    collections: Optional[List[VectorCollectionEnum]] = Field(
        default_factory=list,
        sa_column=Column(JSON)
    )

class VectorTask(VectorTaskBase, table=True):
    __table_args__ = {'sqlite_autoincrement': True}
    id: int | None = Field(default=None, primary_key=True)
    medias: List[VectorMedia] = Relationship(back_populates='vector_task')
    # private
    local_path: str = ''

class VectorTaskCreate(VectorTaskBase):
    # need use VectorMedia, see@https://github.com/fastapi/sqlmodel/issues/293#issuecomment-2247768424
    medias: List[VectorMedia] = Field(default_factory=list)

class VectorTaskPublic(VectorTaskBase):
    id: int
    medias: List[VectorMediaPublic] = []

# 向量来源
class VectorTypeEnum(str, Enum):
    IMAGE = 'image'
    FACE = 'face'
    FACE_DATASET = 'face_dataset'

class VectorQueryMedia(SQLModel):
    base64: str = ''
    url: str = ''
    recognition_target_id: str = ''
    type: VectorTypeEnum = VectorTypeEnum.IMAGE
    threshold: float = 0.3
    bbox: list[int] = [-1, -1, -1, -1]
    kps: list[list[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]]

class VectorSearchResult(SQLModel):
    media_data_id: str
    media_name: str
    media_type: str
    media_extend: str
    media_url: str
    vector_task_id: int
    frame_index: int
    frame_pts: int
    crop_path: str
    # 左上角坐标(left, top), 右下角坐标(right, bottom)
    bbox: list[int]
    # 左眼坐标，右眼坐标，鼻子坐标，左嘴角坐标，右嘴角坐标
    kps: list[list[int]] 
    class_index: int
    class_id: str
    class_name: str
    score: float
    rec_score: float
    distance: float

class VectorSearchGroupEnum(str, Enum):
    NONE = 'none'
    MEDIA_DATA_ID = 'media_data_id'
    MEDIA_NAME = 'media_name'
    MEDIA_TYPE = 'media_type'
    MEDIA_EXTEND = 'media_extend'
    MEDIA_URL = 'media_url'

class VectorSearchMediaCreate(SQLModel):
    media_types: List[MediaTypeEnum]
    media_data_ids: List[str] = []
    group_by_field: VectorSearchGroupEnum = VectorSearchGroupEnum.NONE
    group_size: int = 1
    collection: VectorCollectionEnum
    query_media: VectorQueryMedia
    query_filter: str = ''

class VectorSearchMediaPublic(VectorSearchMediaCreate):
    message: str = ''
    total_count: int = -1
    search_result: List[VectorSearchResult] = []


class VectorExtendInfo(SQLModel):
    vector_task_id: int = -1
    frame_name: str = ''
    crop_path: str = ''
    # 左上角坐标(left, top), 右下角坐标(right, bottom)
    bbox: list[int] = [-1, -1, -1, -1]
    # 左眼坐标，右眼坐标，鼻子坐标，左嘴角坐标，右嘴角坐标
    kps: list[list[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]]
    model_id: int = -1
    class_index: int = -1
    class_id: str = ''
    class_name: str = ''
    score: float = -1
    rec_score: float = -1


class VectorQueryResult(VectorSearchResult):
    vector: list[float] = []


class VectorDeleteMediasCreate(SQLModel):
    media_data_ids: List[str] = []
    collections: List[VectorCollectionEnum] = []

class VectorDeleteMediasResult(SQLModel):
    collection: VectorCollectionEnum
    delete_count: int = 0

class VectorDeleteMediasPublic(VectorDeleteMediasCreate):
    delete_result: List[VectorDeleteMediasResult] = []
