#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完善后的语音识别逻辑
"""

import os
import sys
import numpy as np
from typing import List, Dict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_audio_chunk_with_info():
    """
    测试带信息的音频分块功能
    """
    print("=== 测试音频分块功能（带信息） ===")
    
    # 模拟60秒音频
    sample_rate = 16000
    duration = 60
    waveform = np.random.randn(duration * sample_rate)
    
    # 模拟audio_chunk_with_info方法
    def mock_audio_chunk_with_info(waveform, chunk_size, step_size):
        chunks_info = []
        total_len = len(waveform)
        sample_rate = 16000
        
        chunk_index = 0
        for i in range(0, total_len, step_size):
            end = min(i + chunk_size, total_len)
            chunk = waveform[i:end]
            
            if len(chunk) < chunk_size and chunk_index > 0:
                padded_chunk = np.zeros(chunk_size)
                padded_chunk[:len(chunk)] = chunk
                chunk = padded_chunk
            
            chunk_info = {
                'audio': chunk,
                'start_time': i / sample_rate,
                'end_time': end / sample_rate,
                'start_sample': i,
                'end_sample': end,
                'index': chunk_index,
                'is_last': end >= total_len
            }
            chunks_info.append(chunk_info)
            chunk_index += 1
            
            if end >= total_len:
                break
                
        return chunks_info
    
    chunk_size = 30 * 16000  # 30秒
    step_size = 25 * 16000   # 25秒
    
    chunks_info = mock_audio_chunk_with_info(waveform, chunk_size, step_size)
    
    print(f"原始音频长度: {len(waveform) / 16000:.2f} 秒")
    print(f"生成 {len(chunks_info)} 个音频块:")
    
    for chunk_info in chunks_info:
        print(f"  块 {chunk_info['index']}: "
              f"{chunk_info['start_time']:.2f}s - {chunk_info['end_time']:.2f}s "
              f"(长度: {len(chunk_info['audio']) / 16000:.2f}s, "
              f"最后块: {chunk_info['is_last']})")


def test_timestamp_adjustment_with_chunk_info():
    """
    测试带块信息的时间戳调整
    """
    print("\n=== 测试时间戳调整（带块信息） ===")

    # 模拟ASR结果 - 修正格式：timestamp只包含[开始时间, 结束时间]
    mock_asr_results = [
        {
            "preds": "这是第一句话。",
            "timestamp": [
                [0.0, 0.1], [0.1, 0.2], [0.2, 0.3],
                [0.3, 0.4], [0.4, 0.5], [0.5, 0.6], [0.6, 0.7]
            ],
            "raw_tokens": ["这", "是", "第", "一", "句", "话", "。"]
        },
        {
            "preds": "这是第二句话！",
            "timestamp": [
                [1.0, 1.1], [1.1, 1.2], [1.2, 1.3],
                [1.3, 1.4], [1.4, 1.5], [1.5, 1.6], [1.6, 1.7]
            ],
            "raw_tokens": ["这", "是", "第", "二", "句", "话", "！"]
        }
    ]
    
    # 模拟adjust_timestamps_with_chunk_info方法
    def mock_adjust_timestamps_with_chunk_info(asr_results, start_time, end_time, chunk_index, overlap_duration):
        adjusted_results = []

        for result in asr_results:
            adjusted_result = result.copy()
            adjusted_result['chunk_index'] = chunk_index
            adjusted_result['chunk_start_time'] = start_time
            adjusted_result['chunk_end_time'] = end_time

            if "timestamp" in result and result["timestamp"]:
                adjusted_timestamps = []
                raw_tokens = result.get("raw_tokens", [])

                for i, (rel_start, rel_end) in enumerate(result["timestamp"]):
                    abs_start = start_time + rel_start
                    abs_end = start_time + rel_end

                    in_overlap = chunk_index > 0 and rel_start < overlap_duration
                    char = raw_tokens[i] if i < len(raw_tokens) else ""

                    adjusted_timestamps.append({
                        'char': char,
                        'start': abs_start,
                        'end': abs_end,
                        'in_overlap': in_overlap,
                        'original_format': [abs_start, abs_end]
                    })

                adjusted_result["timestamp"] = adjusted_timestamps
                adjusted_result["timestamp_original"] = [[start_time + s, start_time + e] for s, e in result["timestamp"]]

            adjusted_results.append(adjusted_result)

        return adjusted_results
    
    # 测试不同的块
    test_cases = [
        {"start_time": 0.0, "end_time": 30.0, "chunk_index": 0, "overlap_duration": 5.0},
        {"start_time": 25.0, "end_time": 55.0, "chunk_index": 1, "overlap_duration": 5.0},
        {"start_time": 50.0, "end_time": 60.0, "chunk_index": 2, "overlap_duration": 5.0}
    ]
    
    for case in test_cases:
        print(f"\n块 {case['chunk_index']} ({case['start_time']:.1f}s - {case['end_time']:.1f}s):")
        adjusted = mock_adjust_timestamps_with_chunk_info(
            mock_asr_results, case['start_time'], case['end_time'], 
            case['chunk_index'], case['overlap_duration']
        )
        
        for i, result in enumerate(adjusted):
            print(f"  结果 {i+1}: {result['preds']}")
            overlap_chars = [ts for ts in result['timestamp'] if ts.get('in_overlap', False)]
            non_overlap_chars = [ts for ts in result['timestamp'] if not ts.get('in_overlap', False)]
            print(f"    重叠区域字符数: {len(overlap_chars)}")
            print(f"    非重叠区域字符数: {len(non_overlap_chars)}")
            if result['timestamp']:
                first_ts = result['timestamp'][0]
                last_ts = result['timestamp'][-1]
                print(f"    时间范围: {first_ts['start']:.2f}s - {last_ts['end']:.2f}s")


def test_overlapping_results_merge():
    """
    测试重叠结果合并
    """
    print("\n=== 测试重叠结果合并 ===")
    
    # 模拟多个块的结果 - 修正格式
    chunk_results = [
        {
            'chunk_index': 0,
            'start_time': 0.0,
            'end_time': 30.0,
            'results': [
                {
                    'preds': '第一块的内容，这里有重叠部分',
                    'raw_tokens': ['第', '一', '块', '的', '内', '容', '，', '这', '里', '有', '重', '叠', '部', '分'],
                    'timestamp': [
                        {'char': '第', 'start': 0.0, 'end': 0.1, 'in_overlap': False, 'original_format': [0.0, 0.1]},
                        {'char': '一', 'start': 0.1, 'end': 0.2, 'in_overlap': False, 'original_format': [0.1, 0.2]},
                        {'char': '块', 'start': 0.2, 'end': 0.3, 'in_overlap': False, 'original_format': [0.2, 0.3]},
                        {'char': '的', 'start': 0.3, 'end': 0.4, 'in_overlap': False, 'original_format': [0.3, 0.4]},
                        {'char': '内', 'start': 0.4, 'end': 0.5, 'in_overlap': False, 'original_format': [0.4, 0.5]},
                        {'char': '容', 'start': 0.5, 'end': 0.6, 'in_overlap': False, 'original_format': [0.5, 0.6]},
                        {'char': '，', 'start': 0.6, 'end': 0.7, 'in_overlap': False, 'original_format': [0.6, 0.7]},
                        {'char': '这', 'start': 0.7, 'end': 0.8, 'in_overlap': False, 'original_format': [0.7, 0.8]},
                        {'char': '里', 'start': 0.8, 'end': 0.9, 'in_overlap': False, 'original_format': [0.8, 0.9]},
                        {'char': '有', 'start': 0.9, 'end': 1.0, 'in_overlap': False, 'original_format': [0.9, 1.0]},
                        {'char': '重', 'start': 1.0, 'end': 1.1, 'in_overlap': False, 'original_format': [1.0, 1.1]},
                        {'char': '叠', 'start': 1.1, 'end': 1.2, 'in_overlap': False, 'original_format': [1.1, 1.2]},
                        {'char': '部', 'start': 1.2, 'end': 1.3, 'in_overlap': False, 'original_format': [1.2, 1.3]},
                        {'char': '分', 'start': 1.3, 'end': 1.4, 'in_overlap': False, 'original_format': [1.3, 1.4]}
                    ],
                    'timestamp_original': [[0.0, 0.1], [0.1, 0.2], [0.2, 0.3], [0.3, 0.4], [0.4, 0.5], [0.5, 0.6], [0.6, 0.7], [0.7, 0.8], [0.8, 0.9], [0.9, 1.0], [1.0, 1.1], [1.1, 1.2], [1.2, 1.3], [1.3, 1.4]]
                }
            ]
        },
        {
            'chunk_index': 1,
            'start_time': 25.0,
            'end_time': 55.0,
            'results': [
                {
                    'preds': '重叠部分，然后是第二块的新内容',
                    'raw_tokens': ['重', '叠', '部', '分', '，', '然', '后', '是', '第', '二', '块', '的', '新', '内', '容'],
                    'timestamp': [
                        {'char': '重', 'start': 25.0, 'end': 25.1, 'in_overlap': True, 'original_format': [25.0, 25.1]},
                        {'char': '叠', 'start': 25.1, 'end': 25.2, 'in_overlap': True, 'original_format': [25.1, 25.2]},
                        {'char': '部', 'start': 25.2, 'end': 25.3, 'in_overlap': True, 'original_format': [25.2, 25.3]},
                        {'char': '分', 'start': 25.3, 'end': 25.4, 'in_overlap': True, 'original_format': [25.3, 25.4]},
                        {'char': '，', 'start': 25.4, 'end': 25.5, 'in_overlap': True, 'original_format': [25.4, 25.5]},
                        {'char': '然', 'start': 25.5, 'end': 25.6, 'in_overlap': False, 'original_format': [25.5, 25.6]},
                        {'char': '后', 'start': 25.6, 'end': 25.7, 'in_overlap': False, 'original_format': [25.6, 25.7]},
                        {'char': '是', 'start': 25.7, 'end': 25.8, 'in_overlap': False, 'original_format': [25.7, 25.8]},
                        {'char': '第', 'start': 25.8, 'end': 25.9, 'in_overlap': False, 'original_format': [25.8, 25.9]},
                        {'char': '二', 'start': 25.9, 'end': 26.0, 'in_overlap': False, 'original_format': [25.9, 26.0]},
                        {'char': '块', 'start': 26.0, 'end': 26.1, 'in_overlap': False, 'original_format': [26.0, 26.1]},
                        {'char': '的', 'start': 26.1, 'end': 26.2, 'in_overlap': False, 'original_format': [26.1, 26.2]},
                        {'char': '新', 'start': 26.2, 'end': 26.3, 'in_overlap': False, 'original_format': [26.2, 26.3]},
                        {'char': '内', 'start': 26.3, 'end': 26.4, 'in_overlap': False, 'original_format': [26.3, 26.4]},
                        {'char': '容', 'start': 26.4, 'end': 26.5, 'in_overlap': False, 'original_format': [26.4, 26.5]}
                    ],
                    'timestamp_original': [[25.0, 25.1], [25.1, 25.2], [25.2, 25.3], [25.3, 25.4], [25.4, 25.5], [25.5, 25.6], [25.6, 25.7], [25.7, 25.8], [25.8, 25.9], [25.9, 26.0], [26.0, 26.1], [26.1, 26.2], [26.2, 26.3], [26.3, 26.4], [26.4, 26.5]]
                }
            ]
        }
    ]
    
    # 模拟merge_overlapping_results方法
    def mock_merge_overlapping_results(chunk_results):
        if not chunk_results:
            return []

        # 如果只有一个块，直接返回并恢复原格式
        if len(chunk_results) == 1:
            results = []
            for result in chunk_results[0]['results']:
                clean_result = result.copy()
                # 恢复原始时间戳格式
                if 'timestamp_original' in result:
                    clean_result['timestamp'] = result['timestamp_original']
                    del clean_result['timestamp_original']
                # 移除块信息
                for key in ['chunk_index', 'chunk_start_time', 'chunk_end_time']:
                    if key in clean_result:
                        del clean_result[key]
                results.append(clean_result)
            return results

        merged_results = []

        # 处理第一个块（完整保留）
        first_chunk = chunk_results[0]
        for result in first_chunk['results']:
            clean_result = result.copy()
            # 恢复原始时间戳格式
            if 'timestamp_original' in result:
                clean_result['timestamp'] = result['timestamp_original']
                del clean_result['timestamp_original']
            # 移除块信息
            for key in ['chunk_index', 'chunk_start_time', 'chunk_end_time']:
                if key in clean_result:
                    del clean_result[key]
            merged_results.append(clean_result)

        # 处理后续块（去除重叠部分）
        for i in range(1, len(chunk_results)):
            current_chunk = chunk_results[i]

            for result in current_chunk['results']:
                if 'timestamp' in result and result['timestamp']:
                    # 过滤掉重叠区域的内容
                    non_overlap_timestamps = []
                    non_overlap_tokens = []

                    for j, ts_info in enumerate(result['timestamp']):
                        # 检查是否在重叠区域
                        if not ts_info.get('in_overlap', False):
                            non_overlap_timestamps.append(ts_info['original_format'])
                            # 从raw_tokens获取对应字符
                            if 'raw_tokens' in result and j < len(result['raw_tokens']):
                                non_overlap_tokens.append(result['raw_tokens'][j])

                    if non_overlap_timestamps and non_overlap_tokens:
                        new_result = result.copy()
                        new_result['timestamp'] = non_overlap_timestamps
                        new_result['raw_tokens'] = non_overlap_tokens
                        # 重新生成preds文本
                        new_result['preds'] = ''.join(non_overlap_tokens)

                        # 移除扩展信息
                        if 'timestamp_original' in new_result:
                            del new_result['timestamp_original']
                        for key in ['chunk_index', 'chunk_start_time', 'chunk_end_time']:
                            if key in new_result:
                                del new_result[key]

                        merged_results.append(new_result)

        return merged_results
    
    merged = mock_merge_overlapping_results(chunk_results)
    
    print("合并前:")
    for i, chunk in enumerate(chunk_results):
        print(f"  块 {i}: {chunk['results'][0]['preds']}")
    
    print("\n合并后:")
    for i, result in enumerate(merged):
        print(f"  结果 {i+1}: {result['preds']}")
        if 'timestamp' in result and result['timestamp']:
            start_time = result['timestamp'][0][0]
            end_time = result['timestamp'][-1][1]
            print(f"    时间: {start_time:.2f}s - {end_time:.2f}s")


def test_sentence_boundary_detection():
    """
    测试句子边界检测
    """
    print("\n=== 测试句子边界检测 ===")
    
    # 模拟包含多个句子的结果 - 修正格式
    mock_result = {
        'preds': '这是第一句话。这是第二句话！还有第三句话？',
        'timestamp': [
            [0.0, 0.1], [0.1, 0.2], [0.2, 0.3], [0.3, 0.4],
            [0.4, 0.5], [0.5, 0.6], [0.6, 0.7],
            [1.0, 1.1], [1.1, 1.2], [1.2, 1.3], [1.3, 1.4],
            [1.4, 1.5], [1.5, 1.6], [1.6, 1.7],
            [2.0, 2.1], [2.1, 2.2], [2.2, 2.3], [2.3, 2.4],
            [2.4, 2.5], [2.5, 2.6], [2.6, 2.7]
        ],
        'raw_tokens': [
            '这', '是', '第', '一', '句', '话', '。',
            '这', '是', '第', '二', '句', '话', '！',
            '还', '有', '第', '三', '句', '话', '？'
        ]
    }
    
    # 模拟detect_sentence_boundaries方法
    def mock_detect_sentence_boundaries(results):
        optimized_results = []

        for result in results:
            if 'timestamp' not in result or not result['timestamp'] or 'raw_tokens' not in result:
                optimized_results.append(result)
                continue

            timestamps = result['timestamp']
            raw_tokens = result['raw_tokens']

            # 确保时间戳和字符数量匹配
            if len(timestamps) != len(raw_tokens):
                optimized_results.append(result)
                continue

            sentences = []
            current_sentence = {
                'tokens': [],
                'timestamps': [],
                'start_time': None,
                'end_time': None
            }

            for i, (char, (start, end)) in enumerate(zip(raw_tokens, timestamps)):
                # 初始化句子开始时间
                if current_sentence['start_time'] is None:
                    current_sentence['start_time'] = start

                current_sentence['tokens'].append(char)
                current_sentence['timestamps'].append([start, end])
                current_sentence['end_time'] = end

                # 检测句子结束条件
                is_sentence_end = False

                # 1. 标点符号
                if char in ['。', '！', '？', '.', '!', '?', '；', ';']:
                    is_sentence_end = True

                # 2. 长时间停顿（如果下一个字符开始时间间隔较大）
                elif i < len(timestamps) - 1:
                    next_start = timestamps[i + 1][0]
                    if next_start - end > 1.0:  # 停顿超过1秒
                        is_sentence_end = True

                # 3. 句子过长（超过100个字符）
                elif len(current_sentence['tokens']) > 100:
                    is_sentence_end = True

                # 如果检测到句子结束，保存当前句子并开始新句子
                if is_sentence_end or i == len(timestamps) - 1:
                    if current_sentence['tokens']:
                        sentences.append({
                            'preds': ''.join(current_sentence['tokens']),
                            'raw_tokens': current_sentence['tokens'].copy(),
                            'timestamp': current_sentence['timestamps'].copy(),
                            'start_time': current_sentence['start_time'],
                            'end_time': current_sentence['end_time']
                        })

                    # 重置当前句子
                    current_sentence = {
                        'tokens': [],
                        'timestamps': [],
                        'start_time': None,
                        'end_time': None
                    }

            # 如果只有一个句子，保持原格式
            if len(sentences) == 1:
                optimized_results.append(result)
            else:
                # 多个句子，分别添加
                for sentence in sentences:
                    new_result = result.copy()
                    new_result.update(sentence)
                    optimized_results.append(new_result)

        return optimized_results
    
    print("原始结果:")
    print(f"  文本: {mock_result['preds']}")
    print(f"  时间戳数量: {len(mock_result['timestamp'])}")
    
    optimized = mock_detect_sentence_boundaries([mock_result])
    
    print(f"\n优化后得到 {len(optimized)} 个句子:")
    for i, sentence in enumerate(optimized):
        print(f"  句子 {i+1}: '{sentence['preds']}'")
        print(f"    时间: {sentence['start_time']:.2f}s - {sentence['end_time']:.2f}s")
        print(f"    字符数: {len(sentence['timestamp'])}")


def test_process_results_to_sentences():
    """
    测试process_results_to_sentences方法
    """
    print("\n=== 测试process_results_to_sentences方法 ===")

    # 模拟ASR结果 - 正确格式
    mock_asr_results = [
        {
            "preds": "这是第一句话。这是第二句话！",
            "timestamp": [
                [0.0, 0.1], [0.1, 0.2], [0.2, 0.3], [0.3, 0.4], [0.4, 0.5], [0.5, 0.6], [0.6, 0.7],  # 第一句
                [1.0, 1.1], [1.1, 1.2], [1.2, 1.3], [1.3, 1.4], [1.4, 1.5], [1.5, 1.6], [1.6, 1.7]   # 第二句
            ],
            "raw_tokens": ["这", "是", "第", "一", "句", "话", "。", "这", "是", "第", "二", "句", "话", "！"]
        },
        {
            "preds": "还有第三句话？",
            "timestamp": [
                [2.0, 2.1], [2.1, 2.2], [2.2, 2.3], [2.3, 2.4], [2.4, 2.5], [2.5, 2.6], [2.6, 2.7]
            ],
            "raw_tokens": ["还", "有", "第", "三", "句", "话", "？"]
        }
    ]

    # 模拟process_results_to_sentences方法
    def mock_process_results_to_sentences(asr_results):
        sentences = []

        for result in asr_results:
            if "timestamp" not in result or not result.get("timestamp"):
                text = result.get("preds", "")
                if text:
                    sentences.append({
                        "text": text,
                        "start_time": 0.0,
                        "end_time": 0.0,
                        "words": []
                    })
                continue

            timestamps = result.get("timestamp", [])
            raw_tokens = result.get("raw_tokens", [])

            if not timestamps or not raw_tokens or len(timestamps) != len(raw_tokens):
                continue

            current_sentence = {
                "text": "",
                "start_time": timestamps[0][0] if timestamps else 0.0,
                "end_time": 0.0,
                "words": []
            }

            for i, ((start, end), char) in enumerate(zip(timestamps, raw_tokens)):
                if char == "<sil>":
                    if current_sentence["text"] and i < len(timestamps) - 1:
                        next_start = timestamps[i+1][0]
                        if next_start - end > 0.5:
                            current_sentence["end_time"] = end
                            sentences.append(current_sentence.copy())
                            current_sentence = {
                                "text": "",
                                "start_time": next_start,
                                "end_time": 0.0,
                                "words": []
                            }
                    continue

                if not current_sentence["text"]:
                    current_sentence["start_time"] = start

                current_sentence["text"] += char
                current_sentence["end_time"] = end
                current_sentence["words"].append({
                    "text": char,
                    "start_time": start,
                    "end_time": end
                })

                if char in ['。', '！', '？', '.', '!', '?'] or len(current_sentence["text"]) > 50:
                    if current_sentence["text"]:
                        sentences.append(current_sentence.copy())
                        # 重置当前句子
                        current_sentence = {
                            "text": "",
                            "start_time": 0.0,
                            "end_time": 0.0,
                            "words": []
                        }

            if current_sentence["text"]:
                sentences.append(current_sentence)

        return sentences

    # 执行测试
    sentences = mock_process_results_to_sentences(mock_asr_results)

    print(f"输入ASR结果数: {len(mock_asr_results)}")
    print(f"输出句子数: {len(sentences)}")

    for i, sentence in enumerate(sentences):
        print(f"\n句子 {i+1}:")
        print(f"  文本: '{sentence['text']}'")
        print(f"  时间: {sentence['start_time']:.2f}s - {sentence['end_time']:.2f}s")
        print(f"  字符数: {len(sentence['words'])}")
        if sentence['words']:
            print(f"  首字符: '{sentence['words'][0]['text']}' ({sentence['words'][0]['start_time']:.2f}s)")
            print(f"  末字符: '{sentence['words'][-1]['text']}' ({sentence['words'][-1]['end_time']:.2f}s)")


def main():
    """
    主测试函数
    """
    print("完善后的语音识别逻辑测试")
    print("=" * 60)
    
    test_audio_chunk_with_info()
    test_timestamp_adjustment_with_chunk_info()
    test_overlapping_results_merge()
    test_sentence_boundary_detection()
    test_process_results_to_sentences()

    print("\n" + "=" * 60)
    print("测试完成！")

    print("\n完善的功能特性:")
    print("✓ 音频分块带详细信息（开始/结束时间、索引、是否最后块）")
    print("✓ 时间戳调整包含重叠区域标记")
    print("✓ 智能合并重叠区域，避免重复内容")
    print("✓ 句子边界检测，支持标点符号和停顿分割")
    print("✓ 保持原始数据结构完整性（preds、timestamp、raw_tokens）")
    print("✓ CSV导出格式转换（process_results_to_sentences）")


if __name__ == "__main__":
    main()
