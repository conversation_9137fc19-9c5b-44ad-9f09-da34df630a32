#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试语音识别修复效果的脚本
"""

import os
import sys
import numpy as np
import librosa
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from onnx_sessions.speech_recognition.paraformer_onnx import Paraformer, run_paraformer_asr_inference


def create_test_audio(duration=60, sample_rate=16000):
    """
    创建测试音频数据（模拟包含多句话的音频）
    """
    # 生成包含多个音调的测试音频
    t = np.linspace(0, duration, int(duration * sample_rate), False)
    
    # 模拟多句话：不同频率的正弦波，中间有静音间隔
    audio = np.zeros_like(t)
    
    # 第一句话 (0-10秒)
    mask1 = (t >= 0) & (t < 10)
    audio[mask1] = 0.3 * np.sin(2 * np.pi * 440 * t[mask1])  # A4音调
    
    # 静音间隔 (10-12秒)
    
    # 第二句话 (12-25秒)
    mask2 = (t >= 12) & (t < 25)
    audio[mask2] = 0.3 * np.sin(2 * np.pi * 523 * t[mask2])  # C5音调
    
    # 静音间隔 (25-27秒)
    
    # 第三句话 (27-45秒)
    mask3 = (t >= 27) & (t < 45)
    audio[mask3] = 0.3 * np.sin(2 * np.pi * 659 * t[mask3])  # E5音调
    
    # 静音间隔 (45-47秒)
    
    # 第四句话 (47-60秒)
    mask4 = (t >= 47) & (t < 60)
    audio[mask4] = 0.3 * np.sin(2 * np.pi * 784 * t[mask4])  # G5音调
    
    return audio


def test_audio_chunking():
    """
    测试音频分块功能
    """
    print("=== 测试音频分块功能 ===")
    
    # 创建测试音频
    test_audio = create_test_audio(duration=60)
    print(f"测试音频长度: {len(test_audio) / 16000:.2f} 秒")
    
    # 模拟Paraformer的分块逻辑
    chunk_size = 30 * 16000  # 30秒
    step_size = 25 * 16000   # 25秒, 5秒重叠
    
    chunks = []
    total_len = len(test_audio)
    
    for i in range(0, total_len, step_size):
        end = i + chunk_size
        chunk = test_audio[i:end]
        chunks.append(chunk)
        
        chunk_start_time = i / 16000.0
        chunk_end_time = min(end, total_len) / 16000.0
        print(f"块 {len(chunks)}: {chunk_start_time:.2f}s - {chunk_end_time:.2f}s (长度: {len(chunk) / 16000:.2f}s)")
        
        if end >= total_len:
            break
    
    print(f"总共生成 {len(chunks)} 个音频块")
    return chunks


def test_timestamp_generation():
    """
    测试时间戳生成功能
    """
    print("\n=== 测试时间戳生成功能 ===")
    
    # 模拟ASR结果
    mock_results = [
        {"preds": "这是第一句话"},
        {"preds": "这是第二句话，比较长一些"},
        {"preds": "第三句"},
    ]
    
    # 模拟adjust_timestamps方法
    def mock_adjust_timestamps(asr_results, offset_time):
        adjusted_results = []
        for result in asr_results:
            adjusted_result = result.copy()
            
            text = result.get("preds", "")
            if text:
                # 简单估算：假设每个字符平均0.1秒
                char_duration = 0.1
                
                timestamps = []
                for i, char in enumerate(text):
                    start_time = offset_time + i * char_duration
                    end_time = offset_time + (i + 1) * char_duration
                    timestamps.append([char, start_time, end_time])
                
                adjusted_result["timestamp"] = timestamps
            
            adjusted_results.append(adjusted_result)
        
        return adjusted_results
    
    # 测试不同偏移时间
    offsets = [0.0, 25.0, 50.0]
    
    for offset in offsets:
        print(f"\n偏移时间: {offset}秒")
        adjusted = mock_adjust_timestamps(mock_results, offset)
        
        for i, result in enumerate(adjusted):
            print(f"  结果 {i+1}: {result['preds']}")
            if "timestamp" in result:
                first_char = result["timestamp"][0]
                last_char = result["timestamp"][-1]
                print(f"    时间戳: {first_char[1]:.2f}s - {last_char[2]:.2f}s")


def test_sentence_processing():
    """
    测试句子处理功能
    """
    print("\n=== 测试句子处理功能 ===")
    
    # 模拟带时间戳的ASR结果
    mock_results_with_timestamps = [
        {
            "preds": "这是第一句话。这是第二句话！",
            "timestamp": [
                ["这", 0.0, 0.1], ["是", 0.1, 0.2], ["第", 0.2, 0.3], ["一", 0.3, 0.4],
                ["句", 0.4, 0.5], ["话", 0.5, 0.6], ["。", 0.6, 0.7],
                ["这", 1.0, 1.1], ["是", 1.1, 1.2], ["第", 1.2, 1.3], ["二", 1.3, 1.4],
                ["句", 1.4, 1.5], ["话", 1.5, 1.6], ["！", 1.6, 1.7]
            ]
        }
    ]
    
    # 模拟process_results_to_sentences方法的核心逻辑
    def mock_process_results_to_sentences(asr_results):
        sentences = []
        
        for result in asr_results:
            timestamps = result.get("timestamp", [])
            if not timestamps:
                continue
            
            current_sentence = {
                "text": "",
                "start_time": timestamps[0][1] if timestamps else 0.0,
                "end_time": 0.0,
                "words": []
            }
            
            for i, (char, start, end) in enumerate(timestamps):
                if not current_sentence["text"]:
                    current_sentence["start_time"] = start
                
                current_sentence["text"] += char
                current_sentence["end_time"] = end
                current_sentence["words"].append({
                    "text": char,
                    "start_time": start,
                    "end_time": end
                })
                
                # 检查是否应该分割句子
                if char in ['。', '！', '？', '.', '!', '?']:
                    if current_sentence["text"]:
                        sentences.append(current_sentence.copy())
                        # 准备下一个句子
                        if i < len(timestamps) - 1:
                            current_sentence = {
                                "text": "",
                                "start_time": timestamps[i+1][1] if i+1 < len(timestamps) else end,
                                "end_time": 0.0,
                                "words": []
                            }
            
            # 添加最后一个句子
            if current_sentence["text"]:
                sentences.append(current_sentence)
        
        return sentences
    
    sentences = mock_process_results_to_sentences(mock_results_with_timestamps)
    
    print(f"处理后得到 {len(sentences)} 个句子:")
    for i, sentence in enumerate(sentences):
        print(f"  句子 {i+1}: '{sentence['text']}'")
        print(f"    时间: {sentence['start_time']:.2f}s - {sentence['end_time']:.2f}s")
        print(f"    字数: {len(sentence['words'])}")


def main():
    """
    主测试函数
    """
    print("语音识别修复效果测试")
    print("=" * 50)
    
    # 测试音频分块
    test_audio_chunking()
    
    # 测试时间戳生成
    test_timestamp_generation()
    
    # 测试句子处理
    test_sentence_processing()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    
    print("\n修复说明:")
    print("1. 音频分块: 使用30秒块，25秒步长，确保5秒重叠")
    print("2. 时间戳生成: 为没有时间戳的结果生成估算时间戳")
    print("3. 句子分割: 根据标点符号和长度智能分割句子")
    print("4. 时间偏移: 为每个音频块的结果添加正确的时间偏移")


if __name__ == "__main__":
    main()
