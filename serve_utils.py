
import asyncio
import base64
import json
import os
import re
import shutil
import subprocess
import threading
import time
from typing import Callable, List, Optional
from urllib.parse import urlparse
import zipfile
import cv2
import numpy as np

from pathlib import Path
import yaml
from config import settings

import aiohttp
import backoff

import base64
import imghdr
from io import BytesIO
from PIL import Image

from orm.media_orm import MediaTypeEnum
from utils.logger import get_logger

logger = get_logger()

def ensure_folder_exists(folder: str):
    if not os.path.exists(folder):
        os.makedirs(folder)


def delete_folder(folder: str):
    if len(folder) > 0:
        folder_path = Path(folder)
        # 检查文件夹是否存在
        if folder_path.exists() and folder_path.is_dir():
            try:
                # 删除文件夹及其所有内容
                shutil.rmtree(folder_path)
                logger.info(f"Successfully deleted folder: {folder_path}")
                return True
            except Exception as e:
                logger.info(f"Failed to delete folder: {folder_path} - {e}")
                return False
        logger.info(f"Folder not found: {folder_path}")
    logger.info(f"Invalid folder path: {folder}")
    return False

async def download_file_async(url: str, folder_path: str, progress_callback=None):
    """
    异步从给定URL下载文件并保存到本地路径，支持进度回调
    
    :param url: 文件URL
    :param folder_path: 保存文件的文件夹路径
    :param progress_callback: 可选的进度回调函数，接收(进度百分比, 消息)作为参数
    :return: 下载的文件路径，失败则返回None
    """
    try:
        # 确保文件夹存在
        ensure_folder_exists(folder_path)
        
        # 从URL中提取文件名
        parsed_url = urlparse(url)
        file_name = os.path.basename(parsed_url.path)
        if not file_name:
            file_name = f"downloaded_file_{int(time.time())}"
        
        # 构建完整的文件路径
        file_path = os.path.join(folder_path, file_name)
        
        # 禁用总超时时间
        timeout = aiohttp.ClientTimeout(total=None)
        
        # 初始化进度变量
        start_time = time.time()
        last_update_time = start_time
        downloaded_size = 0
        total_size = 0
        
        # 更新进度的辅助函数
        def update_progress(current, total, force=False):
            nonlocal last_update_time
            current_time = time.time()
            
            # 限制更新频率，避免过多的回调（每0.5秒更新一次）
            if not force and current_time - last_update_time < 0.5:
                return
                
            last_update_time = current_time
            
            if total > 0:
                percentage = min(current / total, 1.0)
                elapsed = current_time - start_time
                
                # 计算下载速度 (bytes/s)
                speed = current / elapsed if elapsed > 0 else 0
                
                # 估计剩余时间 (秒)
                remaining = (total - current) / speed if speed > 0 else 0
                
                # 格式化消息
                speed_str = f"{format_size(speed)}/s"
                message = (f"已下载: {format_size(current)}/{format_size(total)} "
                          f"({percentage:.1%}) - {speed_str}, "
                          f"剩余时间: {format_time(remaining)}")
            else:
                percentage = 0
                message = f"已下载: {format_size(current)}"
            
            # 调用回调函数
            if progress_callback:
                progress_callback(percentage, message)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # 首先发送HEAD请求获取文件大小
            try:
                async with session.head(url, ssl=False) as response:
                    if response.status == 200:
                        content_length = response.headers.get('Content-Length')
                        if content_length:
                            total_size = int(content_length)
                            # 初始进度更新
                            update_progress(0, total_size, force=True)
            except Exception as e:
                logger.info(f"获取文件大小失败: {e}")
            
            # 下载文件
            async with session.get(url, ssl=False) as response:
                response.raise_for_status()  # 确认请求成功
                
                # 如果HEAD请求未获取到大小，尝试从GET响应获取
                if total_size == 0:
                    content_length = response.headers.get('Content-Length')
                    if content_length:
                        total_size = int(content_length)
                        # 初始进度更新
                        update_progress(0, total_size, force=True)
                
                with open(file_path, 'wb') as f:
                    # 分块下载，每次读取8KB
                    chunk_size = 8192
                    async for chunk in response.content.iter_chunked(chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            update_progress(downloaded_size, total_size)
                
                # 最终进度更新
                update_progress(downloaded_size, total_size, force=True)
                
                return file_path
                
    except aiohttp.ClientError as e:
        logger.info(f"下载失败 {url}: {e}")
        if progress_callback:
            progress_callback(0, f"下载失败: {e}")
        return None
    except Exception as e:
        logger.info(f"发生意外错误: {e}")
        if progress_callback:
            progress_callback(0, f"发生错误: {e}")
        return None

def format_size(size_bytes):
    """将字节大小格式化为人类可读的形式"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes/1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes/(1024*1024):.1f} MB"
    else:
        return f"{size_bytes/(1024*1024*1024):.2f} GB"

def format_time(seconds):
    """将秒数格式化为人类可读的时间形式"""
    if seconds < 60:
        return f"{seconds:.0f}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        seconds %= 60
        return f"{minutes:.0f}分{seconds:.0f}秒"
    else:
        hours = seconds // 3600
        seconds %= 3600
        minutes = seconds // 60
        seconds %= 60
        return f"{hours:.0f}时{minutes:.0f}分{seconds:.0f}秒"

def check_file_is_image(input_path: str):
    return os.path.splitext(input_path)[1].lower() in ['.jpg', '.jpeg', '.png']

def extract_frame_copy(input_path, output_dir):
    """
    将单张图片复制到输出目录，并转换为标准的JPG格式
    
    :param input_path: 输入图片路径
    :param output_dir: 输出目录
    :return: 输出目录路径，失败则返回None
    """
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 读取原始图片
        img = cv2.imread(input_path)
        if img is None:
            logger.info(f"无法读取图片: {input_path}")
            return None
        
        # 构建输出文件的完整路径（统一使用jpg格式）
        output_filename = "frame_00000000_00000000.jpg"
        output_path = os.path.join(output_dir, output_filename)
        
        # 保存为JPG格式
        success = cv2.imwrite(output_path, img, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        if success:
            logger.info(f"✅ 完成：复制并转换图片 {input_path} ，输出至 {output_path}")
            return output_dir
        else:
            logger.info(f"❌ 失败：无法保存图片至 {output_path}")
            return None
    except Exception as e:
        logger.info(f"❌ 处理图片时出错: {e}")
        return None

def extract_frames_ffmpeg(input_path, output_dir, frame_interval: int = 0, max_frames: int = 0, 
                         threads: int = 8, quality: int = 2, resize: str = "", progress_callback=None):
    """
    使用 FFmpeg 高效抽帧，并将帧时间戳（毫秒）添加到文件名中
    
    :param input_path: 输入视频路径
    :param output_dir: 输出帧文件夹
    :param frame_interval: 间隔帧数；为 0 时表示抽取所有帧
    :param max_frames: 最大抽取帧数，0 表示不限制
    :param threads: FFmpeg 使用的线程数，0 表示自动选择
    :param quality: JPEG 质量 (1-31)，值越小质量越高，2-5 为推荐值
    :param resize: 可选的调整大小参数，格式为 "wxh"，例如 "640x480"
    :param progress_callback: 可选的进度回调函数，接收(进度百分比, 消息)作为参数
    :return: 输出目录路径
    """
    os.makedirs(output_dir, exist_ok=True)

    # 首先获取视频信息以计算总帧数
    if progress_callback:
        progress_callback(0.01, "获取视频信息...")
    
    # 获取视频总帧数和时长
    probe_cmd = [
        "ffprobe", "-v", "error",
        "-select_streams", "v:0",
        "-show_entries", "stream=nb_frames,duration,r_frame_rate",
        "-of", "json",
        input_path
    ]
    
    try:
        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True)
        video_info = json.loads(probe_result.stdout)
        
        # 尝试获取总帧数
        total_frames = None
        duration = None
        frame_rate = None
        
        if 'streams' in video_info and len(video_info['streams']) > 0:
            stream = video_info['streams'][0]
            
            # 尝试直接获取帧数
            if 'nb_frames' in stream and stream['nb_frames'] != 'N/A':
                total_frames = int(stream['nb_frames'])
            
            # 获取时长
            if 'duration' in stream:
                duration = float(stream['duration'])
            
            # 获取帧率
            if 'r_frame_rate' in stream:
                rate_parts = stream['r_frame_rate'].split('/')
                if len(rate_parts) == 2:
                    frame_rate = float(rate_parts[0]) / float(rate_parts[1])
                else:
                    frame_rate = float(rate_parts[0])
        
        # 如果没有直接获取到帧数，但有时长和帧率，则计算帧数
        if total_frames is None and duration is not None and frame_rate is not None:
            total_frames = int(duration * frame_rate)
        
        # 如果仍然无法获取帧数，使用默认值
        if total_frames is None:
            total_frames = 1000  # 默认值，用于进度估算
            if progress_callback:
                progress_callback(0.02, "警告: 无法确定视频总帧数，将使用估计值")
    except Exception as e:
        logger.info(f"获取视频信息失败: {e}")
        total_frames = 1000  # 默认值
        if progress_callback:
            progress_callback(0.02, f"获取视频信息失败: {e}，将使用估计值")
    
    # 计算预期输出帧数
    expected_frames = total_frames
    if frame_interval > 0:
        expected_frames = total_frames // frame_interval
    if max_frames > 0:
        expected_frames = min(expected_frames, max_frames)
    
    if progress_callback:
        progress_callback(0.05, f"准备提取约 {expected_frames} 帧...")

    # 构建基本命令
    cmd = ["ffmpeg", "-i", input_path]
    
    # 添加线程参数
    if threads > 0:
        cmd.extend(["-threads", str(threads)])
    
    # 添加视频过滤器
    filter_complex = []
    
    # 帧间隔过滤器
    if frame_interval > 0:
        filter_complex.append(f"select='not(mod(n\\,{frame_interval}))'")
    
    # 最大帧数限制
    if max_frames > 0:
        if filter_complex:
            filter_complex[-1] += f",select='lt(n\\,{max_frames})'"
        else:
            filter_complex.append(f"select='lt(n\\,{max_frames})'")
    
    # 调整大小过滤器
    if resize:
        filter_complex.append(f"scale={resize}")
    
    # 应用过滤器
    if filter_complex:
        cmd.extend(["-vf", ",".join(filter_complex)])
    
    # 设置输出参数，优化速度
    cmd.extend([
        "-vsync", "vfr",                # 可变帧率，避免重复帧
        "-q:v", str(quality),           # JPEG 质量，较低的值 = 较高的质量
        "-preset", "ultrafast",         # 最快的编码速度
        "-f", "image2",                 # 图像输出格式
        "-progress", "pipe:1"           # 将进度信息输出到stdout
    ])
    
    # 输出文件模式：直接输出为带时间戳的文件名
    tmp_pattern = os.path.join(output_dir, "tmp_frame_%08d.jpg")
    cmd.append(tmp_pattern)
    
    # 执行 FFmpeg 命令
    if progress_callback:
        progress_callback(0.1, "开始提取帧...")
    
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True,
        bufsize=1
    )
    
    # 正则表达式用于从FFmpeg输出中提取帧信息
    frame_pattern = re.compile(r'frame=\s*(\d+)')
    time_pattern = re.compile(r'out_time_ms=\s*(\d+)')
    
    # 初始化进度变量
    current_frame = 0
    last_update_time = time.time()
    start_time = last_update_time
    
    # 读取FFmpeg输出并更新进度
    for line in process.stdout:
        # 限制更新频率，避免过多的回调（每0.5秒更新一次）
        current_time = time.time()
        if current_time - last_update_time < 0.5:
            continue
        
        last_update_time = current_time
        
        # 尝试从输出中提取当前帧
        frame_match = frame_pattern.search(line)
        if frame_match:
            current_frame = int(frame_match.group(1))
            
            # 计算进度 (10%-80%范围)
            progress = 0.1 + min(current_frame / expected_frames, 1.0) * 0.7
            
            # 计算已用时间和预估剩余时间
            elapsed = current_time - start_time
            if current_frame > 0:
                frames_per_second = current_frame / elapsed
                remaining_frames = expected_frames - current_frame
                remaining_time = remaining_frames / frames_per_second if frames_per_second > 0 else 0
            else:
                remaining_time = 0
            
            # 格式化消息
            message = (f"已提取: {current_frame}/{expected_frames} 帧 ({progress:.1%}), "
                      f"剩余时间: {format_time(remaining_time)}")
            
            # 计算抽帧倍速并添加到消息中
            if elapsed > 0:
                frames_per_second = current_frame / elapsed
                if frame_rate is not None and frames_per_second > 0:
                    speed_ratio = frames_per_second / frame_rate
                    message += f", 处理速度: {frames_per_second:.1f}帧/秒 ({speed_ratio:.1f}倍速)"
                else:
                    message += f", 处理速度: {frames_per_second:.1f}帧/秒"
            
            # 调用回调函数
            if progress_callback:
                progress_callback(progress, message)

            logger.debug(message)
    
    # 等待进程完成
    process.wait()
    
    # 检查是否成功
    if process.returncode != 0:
        error_output = process.stderr.read()
        logger.info(f"提取帧失败: {error_output}")
        if progress_callback:
            progress_callback(0, f"提取帧失败: {error_output}")
        return False
    
    if progress_callback:
        progress_callback(0.8, f"帧提取完成，正在处理时间戳...")
    
    # 获取每帧时间戳（单位：秒）
    probe_cmd = [
        "ffprobe", "-v", "error",
        "-select_streams", "v",
        "-show_entries", "frame=pkt_pts_time",
        "-of", "csv=p=0",
        input_path
    ]
    
    # 如果设置了帧间隔，修改 ffprobe 命令以匹配相同的帧选择
    if frame_interval > 0:
        probe_cmd.insert(5, "-read_intervals")
        # 估算视频总时长，确保能读取所有帧
        duration_cmd = [
            "ffprobe", "-v", "error",
            "-show_entries", "format=duration",
            "-of", "csv=p=0",
            input_path
        ]
        duration_result = subprocess.run(duration_cmd, stdout=subprocess.PIPE, text=True)
        try:
            duration = float(duration_result.stdout.strip())
            probe_cmd.insert(6, f"%+{duration}")
        except (ValueError, IndexError):
            # 如果无法获取时长，使用一个大数值
            probe_cmd.insert(6, "%+999999")
    
    # 执行 ffprobe 命令获取时间戳
    result = subprocess.run(probe_cmd, stdout=subprocess.PIPE, text=True)
    
    # 过滤掉空行
    pts_list = [line for line in result.stdout.strip().split("\n") if line.strip()]
    
    # 如果设置了帧间隔，需要按间隔选择时间戳
    if frame_interval > 0:
        pts_list = pts_list[::frame_interval]
    
    # 如果设置了最大帧数，限制时间戳列表长度
    if max_frames > 0:
        pts_list = pts_list[:max_frames]
    
    # 重命名临时帧为带时间戳的帧
    temp_files = sorted(f for f in os.listdir(output_dir) if f.startswith("tmp_frame_"))
    
    if progress_callback:
        progress_callback(0.85, f"开始格式化文件名 {len(temp_files)} 个文件...")
    
    # 使用多线程并行重命名文件
    def rename_file(args):
        i, fname = args
        if i >= len(pts_list):
            return
        try:
            timestamp_ms = int(float(pts_list[i]) * 1000)
            src_path = os.path.join(output_dir, fname)
            dst_path = os.path.join(output_dir, f"frame_{i:08d}_{timestamp_ms:08d}.jpg")
            os.rename(src_path, dst_path)
        except (ValueError, IndexError) as e:
            logger.info(f"格式化名文件时出错: {e}")
    
    # 使用线程池并行重命名
    from concurrent.futures import ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=min(32, os.cpu_count() * 2)) as executor:
        list(executor.map(rename_file, enumerate(temp_files)))
    
    if progress_callback:
        progress_callback(1.0, f"完成：共抽取 {len(temp_files)} 帧")
    
    logger.info(f"✅ 完成：共抽取 {len(temp_files)} 帧，输出至 {output_dir}")
    
    return output_dir

def extract_audio_ffmpeg(input_path, output_dir, 
                         sample_rate: int = 16000, channels: int = 1,
                         duration: float = 0,  # 单位：秒，0 表示不限制
                         threads: int = 0,
                         filename_prefix: str = "audio",
                         progress_callback=None):
    """
    使用 FFmpeg 从视频中提取音频为 WAV 文件，输出至指定目录，供 librosa.load 使用

    :param input_path: 输入视频或音频路径
    :param output_dir: 输出 WAV 文件所在的文件夹路径
    :param sample_rate: 输出采样率 (Hz)
    :param channels: 声道数（1=单声道, 2=立体声）
    :param duration: 最长提取时长（单位：秒），0 表示提取完整音频
    :param threads: FFmpeg 线程数，0 表示自动选择
    :param filename_prefix: 输出文件名前缀（默认 audio）
    :param progress_callback: 进度回调函数(progress: float, message: str)
    :return: 输出的 WAV 音频文件路径
    """
    os.makedirs(output_dir, exist_ok=True)

    if progress_callback:
        progress_callback(0.05, "准备提取音频...")

    # 输出文件名可加时间戳避免冲突
    # timestamp = int(time.time() * 1000)
    # output_wav_path = os.path.join(output_dir, f"{filename_prefix}_{timestamp}.wav")
    output_wav_path = os.path.join(output_dir, f"{filename_prefix}.wav")

    # 构建 FFmpeg 命令
    cmd = [
        "ffmpeg", "-y",
        "-i", input_path,
        "-vn",
        "-acodec", "pcm_s16le",
        "-ar", str(sample_rate),
        "-ac", str(channels),
    ]

    if duration > 0:
        cmd.extend(["-t", str(duration)])

    if threads > 0:
        cmd.extend(["-threads", str(threads)])

    cmd.append(output_wav_path)

    try:
        subprocess.run(cmd, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
    except subprocess.CalledProcessError as e:
        error_msg = e.stderr.decode(errors="ignore") if e.stderr else str(e)
        logger.error(f"音频提取失败: {error_msg}")
        if progress_callback:
            progress_callback(0.0, f"音频提取失败: {error_msg}")
        raise RuntimeError(f"音频提取失败: {error_msg}")

    if progress_callback:
        progress_callback(1.0, f"音频提取完成: {output_wav_path}")

    logger.info(f"✅ 成功提取音频到: {output_wav_path}")
    return output_wav_path

def save_base64_image(base64_data: str, save_dir: str, filename_prefix: str = 'query_media') -> str:
    if not base64_data:
        raise ValueError("Empty base64 data")

    # 尝试去掉前缀，如：data:image/jpeg;base64,...
    if ',' in base64_data:
        base64_data = base64_data.split(',')[1]

    # 解码 base64
    img_bytes = base64.b64decode(base64_data)

    # 使用 imghdr 判断格式（快速但有限）
    img_format = imghdr.what(None, h=img_bytes)

    # 更精确地用 Pillow 再判断格式
    if not img_format:
        try:
            with Image.open(BytesIO(img_bytes)) as img:
                img_format = img.format.lower()
        except Exception:
            raise ValueError("Unsupported or corrupted image format in base64")

    # 扩展名映射表（可根据需要扩展）
    ext_map = {
        'jpeg': 'jpg',
        'png': 'png',
        'bmp': 'bmp',
        'gif': 'gif',
        'webp': 'webp',
        'tiff': 'tif'
    }

    ext = ext_map.get(img_format, 'jpg')  # fallback 为 jpg
    file_path = os.path.join(save_dir, f'{filename_prefix}.{ext}')

    # 保存图片
    with open(file_path, 'wb') as f:
        f.write(img_bytes)

    return file_path


def generate_yolo_dataset_config_file(dataset_path: str, config_path: str):
    if dataset_path.endswith('.zip'):
        # 解压 zip 文件
        dst_path = dataset_path.replace('.zip', '')
        with zipfile.ZipFile(dataset_path, 'r') as zip_ref:
            zip_ref.extractall(dst_path)
        # 获取解压后的文件夹路径
        dataset_path = dst_path
    elif dataset_path.endswith('.tar.gz'):
        # 解压 tar.gz 文件
        dst_path = dataset_path.replace('.tar.gz', '')
        shutil.unpack_archive(dataset_path, dst_path)
        dataset_path = dst_path
    # 读取 classes.txt 文件
    classes_path = os.path.join(dataset_path, "classes.txt")
    with open(classes_path, 'r') as f:
        classes = [line.strip() for line in f.readlines()]
    # 创建 config.yaml 文件内容
    config = {
        "path": os.path.abspath(dataset_path),
        "train": "images",
        "val": "images",
        "test": None,
        "names": {i: name for i, name in enumerate(classes)}
    }
    # 写入 config.yaml 文件
    with open(config_path, 'w') as f:
        yaml.dump(config, f, sort_keys=False)
    return config_path

# 发送 webhook 请求的函数，添加指数退避重试机制
@backoff.on_exception(
    # 使用指数退避策略
    backoff.expo,
    # 针对 ClientError 进行重试
    aiohttp.ClientError,
    # base=2,
    # 最大重试次数为 5 次
    max_tries=settings.web_hooks.max_try_count,
    # 最大重试时间为 60 秒
    max_time=settings.web_hooks.max_try_time,
    # 防止重试高峰，可以使用抖动机制
    jitter=backoff.full_jitter,
    # jitter=None,
    # logger=logger
)
async def send_webhook_request(web_hook_url: str, data: dict):
    if web_hook_url and data:
        async with aiohttp.ClientSession() as session:
            async with session.post(web_hook_url, json=data) as response:
                if response.status == 200:
                    logger.info(f"Webhook {web_hook_url} response: {response.status} {await response.text()}")
                else:
                    raise aiohttp.ClientError(f"Webhook {web_hook_url} failed with status: {response.status}")

async def hook_notify_backoff(web_hook_url: str, data: dict):
    try:
        # 使用 backoff 装饰器处理重试机制
        await send_webhook_request(web_hook_url, data)
        return True
    except aiohttp.ClientError as e:
        logger.info(f"Webhook {web_hook_url} failed after retries: {e}")
        return False
        
def run_async_in_background(coro):
    def runner():
        asyncio.run(coro)
    threading.Thread(target=runner, daemon=True).start()

def is_valid_bbox(bbox, image_shape):
    if not isinstance(bbox, (list, tuple, np.ndarray)) or len(bbox) != 4:
        return False
    try:
        x1, y1, x2, y2 = map(float, bbox)
        H, W = image_shape
        # 合法坐标 + 有效矩形（右下大于左上）
        return (0 <= x1 < x2 <= W) and (0 <= y1 < y2 <= H)
    except Exception:
        return False


def is_valid_kps(kps, image_shape):
    try:
        kps = np.array(kps, dtype=np.float32)
        if kps.ndim != 2 or kps.shape[1] != 2:
            return False
        H, W = image_shape
        xs_valid = np.all((0 <= kps[:, 0]) & (kps[:, 0] <= W))
        ys_valid = np.all((0 <= kps[:, 1]) & (kps[:, 1] <= H))
        return xs_valid and ys_valid
    except Exception:
        return False

def crop_image_with_path(image_path, bbox):
    # Read and crop the image to bbox
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Failed to read image: {image_path}")
    return crop_image(image, bbox)

def crop_image(image, bbox):
    # Read and crop the image to bbox
    if image is None:
        raise ValueError(f"Image is None")
    
    x1, y1, x2, y2 = bbox
    x1, y1 = max(0, x1), max(0, y1)
    x2, y2 = min(image.shape[1], x2), min(image.shape[0], y2)
    if x2 <= x1 or y2 <= y1:
        raise ValueError(f"Invalid bbox dimensions: {bbox}")

    cropped_image = image[y1:y2, x1:x2]
    return cropped_image

def crop_and_save(image_path, bbox, output_path):
    """
    根据 bbox 截取图像的一部分并保存。

    :param image_path: 输入图像的路径。
    :param bbox: 边界框坐标，格式为 [x1, y1, x2, y2]。
    :param output_path: 输出图像的路径。
    """
    # 读取图像
    img = cv2.imread(image_path)

    # 确保 bbox 坐标在图像尺寸范围内
    x1, y1, x2, y2 = bbox
    height, width = img.shape[:2]
    x1, y1 = max(0, min(x1, width))
    x2, y2 = max(0, min(x2, width))
    y1, y2 = max(0, min(y2, height))

    # 截取图像
    cropped_img = img[y1:y2, x1:x2]

    # 保存截取后的图像
    cv2.imwrite(output_path, cropped_img)
    return output_path


def is_live_stream(source: str) -> bool:
    """智能判断输入地址是直播流还是文件（包含协议、扩展名、时长检测）"""
    live_protocols = ('rtmp://', 'rtsp://')
    file_protocols = ('http://', 'https://')
    file_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.webm', '.jpg', '.jpeg', '.png')
    # live_extensions = ('.flv', )
    live_extensions = ()

    # 1. 基于协议判断
    if source.startswith(live_protocols):
        return True

    # 2. http/https 需要看扩展名
    if source.startswith(file_protocols):
        _, ext = os.path.splitext(source.lower())
        if ext in live_extensions:
            return True  # HTTP-FLV当直播流
        elif ext in file_extensions:
            return False  # 文件
        else:
            # 其他情况：未知扩展或无扩展 -> 动态探测
            return probe_is_live(source)

    # 3. 本地路径 -> 动态探测
    return probe_is_live(source)


def probe_is_live(source: str) -> bool:
    """调用ffprobe探测视频源，如果没有duration字段，认为是直播流"""
    try:
        # 使用ffprobe获取format信息
        cmd = [
            "ffprobe", "-v", "error",
            "-select_streams", "v:0",
            "-show_entries", "format=duration",
            "-of", "default=noprint_wrappers=1:nokey=1",
            source
        ]
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=10)
        output = result.stdout.decode().strip()
        
        if output == '' or float(output) <= 0:
            return True  # 没有duration 或 duration无效，认为是直播
        else:
            return False  # 有duration，认为是文件
    except Exception as e:
        logger.info(f"ffprobe failed: {e}")
        return True  # 出错时默认按直播处理更安全


import requests
import mimetypes
from urllib.parse import urlparse

def guess_media_type(url: str) -> MediaTypeEnum:
    """根据HTTP头信息和URL后缀自动判断url类型，返回枚举类型"""
    try:
        live_protocols = ('rtmp://', 'rtsp://')
        # 1. 基于协议判断
        if url.startswith(live_protocols):
            return MediaTypeEnum.STREAM
        
        response = requests.head(url, allow_redirects=True, timeout=5, verify=False)
        content_type = response.headers.get('Content-Type', '').lower()
        
        if content_type:
            if content_type.startswith('image/'):
                return MediaTypeEnum.IMAGE
            elif content_type.startswith('video/'):
                if is_live_stream(url):
                    return MediaTypeEnum.STREAM
                return MediaTypeEnum.VIDEO
            elif content_type.startswith('audio/'):
                return MediaTypeEnum.AUDIO
            # elif 'mpegurl' in content_type or 'hls' in content_type:
            #     return MediaTypeEnum.STREAM
            elif content_type in ('application/octet-stream',):
                pass
            else:
                # return MediaTypeEnum.FILE
                pass

        # Content-Type 没有或不可用，靠后缀兜底推测
        path = urlparse(url).path
        extension = path.split('.')[-1].lower() if '.' in path else ''

        if extension:
            mime_type, _ = mimetypes.guess_type(url)
            if mime_type:
                if mime_type.startswith('image/'):
                    return MediaTypeEnum.IMAGE
                elif mime_type.startswith('video/'):
                    if is_live_stream(url):
                        return MediaTypeEnum.STREAM
                    return MediaTypeEnum.VIDEO
                elif mime_type.startswith('audio/'):
                    return MediaTypeEnum.AUDIO

        # 直播流特殊格式
        if is_live_stream(url):
            return MediaTypeEnum.STREAM
        
        return MediaTypeEnum.FILE
    except Exception as e:
        logger.info(f"Error guessing media type for {url}: {e}")
        return MediaTypeEnum.UNKNOWN


def image_to_base64(image_path: str) -> Optional[str]:
    """
    将图片文件转换为 Base64 编码的字符串。

    :param image_path: 图片文件的路径
    :return: Base64 编码的字符串，如果文件不存在或读取失败则返回 None
    """
    try:
        # 打开图片文件并读取内容
        with open(image_path, "rb") as image_file:
            # 将图片内容编码为 Base64
            base64_data = base64.b64encode(image_file.read()).decode("utf-8")
            return base64_data
    except FileNotFoundError:
        logger.info(f"图片文件不存在: {image_path}")
    except Exception as e:
        logger.info(f"读取图片文件时发生错误: {e}")
    return None

def np_image_to_base64(image_np: np.ndarray, format: str = 'jpg') -> str:
    # 把 ndarray 编码成图片字节，format 通常是 'jpg' 或 'png'
    success, encoded_image = cv2.imencode(f'.{format}', image_np)
    if not success:
        raise ValueError("图片编码失败")

    # encoded_image 是 numpy.ndarray，转换成 bytes
    image_bytes = encoded_image.tobytes()

    # 转 base64
    base64_str = base64.b64encode(image_bytes).decode('utf-8')
    return base64_str

def get_sorted_frame_filenames_by_index(folder_path: str) -> List[str]:
    """
    筛选文件夹中符合 frame_XXXXXXXX_XXXXXXXX.jpg 格式的文件，并按 frame_index 从小到大排序返回文件名列表。
    """
    pattern = re.compile(r"^frame_(\d{8})_(\d{8})\.jpg$")
    matched_files = []

    for filename in os.listdir(folder_path):
        match = pattern.match(filename)
        if match:
            frame_index = int(match.group(1))  # 提取 frame_index 作为排序键
            matched_files.append((frame_index, filename))

    # 按 frame_index 排序
    sorted_files = sorted(matched_files, key=lambda x: x[0])
    # 返回排序后的文件名列表
    return [filename for _, filename in sorted_files]

def to_json_serializable(obj):
    """
    将numpy 数组转换为 JSON 可序列化的对象
    """
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.integer, np.floating)):
        return obj.item()
    elif isinstance(obj, dict):
        return {k: to_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [to_json_serializable(x) for x in obj]
    else:
        return obj

def count_frames_in_directory(directory_path: str, pattern: str = None) -> int:
    """
    计算目录中符合特定模式的帧文件数量
    
    :param directory_path: 帧文件所在目录路径
    :param pattern: 可选的文件名匹配模式，默认为 "frame_*.jpg"
    :return: 帧文件数量
    """
    if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
        logger.info(f"警告: 目录不存在或不是有效目录: {directory_path}")
        return 0
    
    # 默认匹配模式为 "frame_*.jpg"
    if pattern is None:
        pattern = r"^frame_\d{8}_\d{8}\.jpg$"
    
    # 使用正则表达式编译模式以提高性能
    import re
    regex = re.compile(pattern)
    
    # 使用os.scandir而不是os.listdir，性能更好
    count = 0
    try:
        with os.scandir(directory_path) as entries:
            for entry in entries:
                if entry.is_file() and regex.match(entry.name):
                    count += 1
    except Exception as e:
        logger.info(f"计算帧数时出错: {e}")
        return 0
    
    return count

def count_frame_files(directory_path: str) -> int:
    """
    计算目录中标准帧文件的数量 (frame_XXXXXXXX_XXXXXXXX.jpg)
    这是一个针对标准帧文件格式优化的简化版本
    
    :param directory_path: 帧文件所在目录路径
    :return: 帧文件数量
    """
    if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
        return 0
    
    # 使用glob模式匹配，比正则表达式更快
    import glob
    pattern = os.path.join(directory_path, "frame_????????_????????.jpg")
    
    try:
        # glob.glob返回匹配的文件列表
        matching_files = glob.glob(pattern)
        return len(matching_files)
    except Exception as e:
        logger.info(f"计算帧文件时出错: {e}")
        return 0

def get_frame_index_range(directory_path: str) -> tuple:
    """
    获取目录中帧文件的索引范围 (最小和最大帧索引)
    
    :param directory_path: 帧文件所在目录路径
    :return: 元组 (min_index, max_index)，如果没有帧文件则返回 (0, 0)
    """
    if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
        return (0, 0)
    
    import re
    pattern = re.compile(r"^frame_(\d{8})_\d{8}\.jpg$")
    
    min_index = float('inf')
    max_index = -1
    
    try:
        with os.scandir(directory_path) as entries:
            for entry in entries:
                if entry.is_file():
                    match = pattern.match(entry.name)
                    if match:
                        frame_index = int(match.group(1))
                        min_index = min(min_index, frame_index)
                        max_index = max(max_index, frame_index)
    except Exception as e:
        logger.info(f"获取帧索引范围时出错: {e}")
        return (0, 0)
    
    # 如果没有找到任何帧
    if min_index == float('inf'):
        return (0, 0)
    
    return (min_index, max_index)

