
from sqlmodel import Session, create_engine, SQLModel
from sqlalchemy import Column, Integer
from sqlalchemy.ext.declarative import declared_attr

from config import DATABASE_URL, settings

database_url = settings.api_server.database_url or DATABASE_URL
connect_args = {"check_same_thread": False, "timeout": 30}
# echo: 是否打印 SQL 语句
engine = create_engine(database_url, echo=False, connect_args=connect_args)

"""
对于需要防止并发更新冲突的关键模型，可以继承 `VersionedModel` 基类：

```python
from database.orm_database import VersionedModel

class MyModel(SQLModel, VersionedModel, table=True):
    id: int = Field(default=None, primary_key=True)
    # 其他字段...
```
"""
# 添加版本控制基类
class VersionedModel:
    """为模型添加版本控制支持"""
    
    @declared_attr
    def version_id(cls):
        return Column(Integer, nullable=False, default=1)
    
    __mapper_args__ = {"version_id_col": "version_id"}

def create_db_and_tables():
    SQLModel.metadata.create_all(engine)

def get_session():
    with Session(engine) as session:
        yield session

# 手动创建 session，在其他线程中调用
def get_session_need_close():
    return Session(engine)

# 运行数据库迁移
def run_migrations():
    """运行数据库迁移（可选）"""
    import subprocess
    import os
    import shutil
    from pathlib import Path
    import datetime
    
    # 备份数据库文件
    db_path = "serverun.db"
    if os.path.exists(db_path):
        backup_dir = "db_backups"
        os.makedirs(backup_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"serverun_{timestamp}.db")
    #     shutil.copy2(db_path, backup_path)
    #     print(f"数据库已备份到: {backup_path}")
    
    migrations_dir = Path(__file__).parent.parent / "migrations"
    if migrations_dir.exists():
        try:
            # 使用 --sql 参数先生成 SQL 而不执行
            sql_output = subprocess.run(
                ["alembic", "upgrade", "head", "--sql"],
                cwd=str(migrations_dir),
                env=os.environ.copy(),
                capture_output=True,
                text=True
            )
            
            # 检查 SQL 是否包含 DROP TABLE 语句
            sql = sql_output.stdout
            if "DROP TABLE" in sql.upper():
                print("警告: 迁移脚本包含 DROP TABLE 语句，可能会导致数据丢失！")
                print("请检查迁移脚本并手动执行迁移。")
                return
            
            # 如果没有危险操作，执行迁移
            subprocess.run(
                ["alembic", "upgrade", "head"],
                cwd=str(migrations_dir),
                env=os.environ.copy()
            )
        except Exception as e:
            print(f"执行迁移时出错: {e}")
            # 如果迁移失败，恢复备份
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, db_path)
                print(f"迁移失败，已恢复数据库备份")
