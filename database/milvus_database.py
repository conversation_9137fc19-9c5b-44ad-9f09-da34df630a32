
from pymilvus import DataType, MilvusClient

from config import MILVUS_URL, settings
from orm.vector_task_orm import VectorCollectionEnum


milvus_url = settings.api_server.milvus_url or MILVUS_URL

client = MilvusClient(milvus_url)

def create_milvus_collections():
    for collection in VectorCollectionEnum:
        collection_value = collection.value
        if not client.has_collection(collection_value):
            schema = MilvusClient.create_schema(
                enable_dynamic_field=True,
            )
            schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=True)
            if collection_value == VectorCollectionEnum.image_resnet50.value or collection_value == VectorCollectionEnum.image_vgg.value:
                schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=2048)
            elif collection_value == VectorCollectionEnum.image_insightface.value or collection_value == VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET.value:
                schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=512)
            schema.add_field(field_name="media_data_id", datatype=DataType.VARCHAR, max_length=512)
            schema.add_field(field_name="media_name", datatype=DataType.VARCHAR, max_length=512)
            schema.add_field(field_name="media_type", datatype=DataType.VARCHAR, max_length=512)
            schema.add_field(field_name="media_extend", datatype=DataType.JSON)
            schema.add_field(field_name="media_url", datatype=DataType.VARCHAR, max_length=4096)
            schema.add_field(field_name="extend_json", datatype=DataType.JSON)

            index_params = client.prepare_index_params()
            index_params.add_index(
                field_name="media_data_id",
                index_type="AUTOINDEX"
            )
            index_params.add_index(
                field_name="vector", 
                index_type="AUTOINDEX",
                metric_type="COSINE"
            )

            client.create_collection(
                collection_name=collection_value,
                schema=schema,
                index_params=index_params
            )

def get_milvus_client():
    return client
