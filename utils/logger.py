import multiprocessing
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any, Union
import json

from loguru import logger
from loguru._logger import Logger as LoguruLogger

class JsonFormatter:
    """格式化日志为JSON格式，便于对接其他系统"""
    def __call__(self, record):
        log_record = {
            "timestamp": record["time"].strftime("%Y-%m-%d %H:%M:%S.%f"),
            "level": record["level"].name,
            "logger": record["name"],
            "message": record["message"],
            "module": record["module"],
            "function": record["function"],
            "line": record["line"]
        }
        
        if "extra" in record and record["extra"]:
            log_record.update(record["extra"])
            
        if record["exception"]:
            log_record["exception"] = record["exception"]
            
        return json.dumps(log_record)

class Logger:
    """
    统一日志处理类
    支持控制台输出、文件输出和JSON格式化
    """
    _instance = None
    _initialized = False
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, 
                 name: str = "app", 
                 level: str = "INFO",
                 console: bool = True,
                 file: bool = False,
                 file_path: str = "logs",
                 max_bytes: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5,
                 json_format: bool = False):
        
        if self._initialized:
            return
        
        # 移除所有默认处理器
        logger.remove()
        
        self.name = name
        self.level = level
        # see@https://loguru.readthedocs.io/en/stable/resources/recipes.html#compatibility-with-multiprocessing-using-enqueue-argument
        context = multiprocessing.get_context("spawn")
        # 控制台输出
        if console:
            if json_format:
                logger.add(
                    sys.stdout, 
                    level=level.upper(),
                    enqueue=True,
                    context=context,
                    format="{message}",
                    # filter=lambda record: record["name"] == name,
                    serialize=JsonFormatter()
                )
            else:
                logger.add(
                    sys.stdout, 
                    level=level.upper(),
                    enqueue=True,
                    context=context,
                    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level>|<cyan>{process.name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
                    # filter=lambda record: record["name"] == name
                )
        
        # 文件输出
        if file:
            log_dir = Path(file_path)
            log_dir.mkdir(parents=True, exist_ok=True)
            
            if json_format:
                logger.add(
                    log_dir / f"{name}.log",
                    level=level.upper(),
                    enqueue=True,
                    context=context,
                    format="{message}",
                    # filter=lambda record: record["name"] == name,
                    rotation=max_bytes,  # 直接使用整数表示字节数
                    retention=backup_count,
                    serialize=JsonFormatter()
                )
            else:
                logger.add(
                    log_dir / f"{name}.log",
                    level=level.upper(),
                    enqueue=True,
                    context=context,
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
                    # filter=lambda record: record["name"] == name,
                    rotation=max_bytes,  # 直接使用整数表示字节数
                    retention=backup_count
                )
        
        self._initialized = True
        self.logger = logger.bind(name=name)
    
    def get_logger(self):
        return self.logger
    
    @classmethod
    def configure(cls, config: Dict[str, Any]):
        """通过配置字典初始化日志系统"""
        return cls(**config)
    
    @classmethod
    def reset(cls):
        """重置单例状态，主要用于测试"""
        cls._instance = None
        cls._initialized = False

default_logger: Optional[LoguruLogger] = None  # 初始化为空

def init_default_logger(config: Dict[str, Any] = None):
    """显式初始化默认 logger"""
    global default_logger
    if config is None:
        config = {}
    default_logger = Logger.configure(config).get_logger()

def set_logger(logger: LoguruLogger):
    """设置默认 logger"""
    global default_logger
    default_logger = logger

# 便捷函数
def get_logger(name: str = None) -> LoguruLogger:
    """获取指定名称的日志器"""
    if name:
        return logger.bind(name=name)
    if default_logger is None:
        init_default_logger()
    return default_logger

def log(message: str, level: str = "info", extra: Dict[str, Any] = None):
    """简便的日志记录函数，替代print"""
    log_method = getattr(default_logger, level.lower(), default_logger.info)
    if extra:
        log_method(message, **extra)
    else:
        log_method(message)

# 别名，方便替换print
info = lambda msg, **kwargs: default_logger.info(msg, **kwargs)
debug = lambda msg, **kwargs: default_logger.debug(msg, **kwargs)
warning = lambda msg, **kwargs: default_logger.warning(msg, **kwargs)
error = lambda msg, **kwargs: default_logger.error(msg, **kwargs)
critical = lambda msg, **kwargs: default_logger.critical(msg, **kwargs)
