# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all
# see@https://github.com/microsoft/onnxruntime/issues/13499#issuecomment-2100177948
from PyInstaller.utils.hooks import collect_dynamic_libs
# Collect dynamic libraries from onnxruntime
datas = [('audio_denoiser/nsnet2_denoiser/nsnet2-20ms-48k-baseline.onnx', 'audio_denoiser/nsnet2_denoiser'),
            ('audio_denoiser/FullSubNetPlus/config/inference.toml', 'audio_denoiser/FullSubNetPlus/config'),
            ('audio_denoiser/FullSubNetPlus/best_model.tar', 'audio_denoiser/FullSubNetPlus'),
            ('audio_denoiser/DeepFilterNet3/denoiser_model.onnx', 'audio_denoiser/DeepFilterNet3')]
binaries = collect_dynamic_libs('onnxruntime', destdir='onnxruntime/capi')
hiddenimports = []
# for torchaudio use FFmpeg backend
tmp_ret = collect_all('torio')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]

tmp_ret = collect_all('onnxruntime')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
# tmp_ret = collect_all('sympy')
# datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
# tmp_ret = collect_all('packaging')
# datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
# tmp_ret = collect_all('flatbuffers')
# datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
# tmp_ret = collect_all('coloredlogs')
# datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
# tmp_ret = collect_all('protobuf')
# datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
# tmp_ret = collect_all('numpy')
# datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
# tmp_ret = collect_all('humanfriendly')
# datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
# tmp_ret = collect_all('mpmath')
# datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]

yolov5_files = [('model_train/yolov5', 'model_train/yolov5')]
datas += yolov5_files

model_zoo_files = [('model_zoo', 'model_zoo')]
datas += model_zoo_files

paths = []
# paths = ['/usr/local/lib', '/usr/lib/x86_64-linux-gnu']

a = Analysis(
    ['main.py'],
    pathex=paths,
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='main',
)
