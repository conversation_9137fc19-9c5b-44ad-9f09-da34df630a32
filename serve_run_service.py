from sqlmodel import Session, desc, select
from serve_run import MediaOutput, ServeRun, ServeStep, StepStatusEnum, StepTypeEnum

class ServeRunService:
    def __init__(self, session: Session):
        self.session = session
    
    # ServeRun
    def add_serve_run(self, serve_run: ServeRun) -> ServeRun:
        self.session.add(serve_run)
        self.session.commit()
        self.session.refresh(serve_run)
        return serve_run

    def get_serve_run(self, pri_id: str) -> ServeRun:
        serve_run = self.session.get(ServeRun, pri_id)
        return serve_run

    def get_serve_run_by_hash_id(self, hash_id: str) -> ServeRun:
        # last one ServeRun
        statement = select(ServeRun).where(ServeRun.hash_id == hash_id).order_by(desc(ServeRun.pri_id)).limit(1)
        serve_run = self.session.exec(statement).first()
        return serve_run

    def update_serve_run(self, serve_run: ServeRun) -> ServeRun:
        serve_run_db = self.session.get(ServeRun, serve_run.pri_id)
        if not serve_run_db:
            serve_run_db = self.add_serve_run(serve_run)
        else:
            serve_run_data = serve_run.model_dump(exclude_unset=True)
            for key, value in serve_run_data.items():
                setattr(serve_run_db, key, value)
            self.session.add(serve_run_db)
            self.session.commit()
            self.session.refresh(serve_run_db)
        return serve_run_db
    
    def delete_serve_run(self, pri_id: str) -> ServeRun:
        serve_run = self.session.get(ServeRun, pri_id)
        if serve_run:
            self.session.delete(serve_run)
            self.session.commit()
            return serve_run
        return None


    # MediaOutput
    def add_media_output(self, media_output: MediaOutput) -> MediaOutput:
        self.session.add(media_output)
        self.session.commit()
        self.session.refresh(media_output)
        return media_output
    
    def get_media_output(self, pri_id: str) -> MediaOutput:
        media_output = self.session.get(MediaOutput, pri_id)
        return media_output
    
    def get_media_output_by_id(self, media_id: str) -> MediaOutput:
        statement = select(MediaOutput).where(MediaOutput.id == media_id).order_by(desc(MediaOutput.pri_id)).limit(1)
        output_media = self.session.exec(statement).first()
        return output_media
    
    def update_media_output(self, media_output: MediaOutput) -> MediaOutput:
        media_output_db = self.session.get(MediaOutput, media_output.pri_id)
        if not media_output_db:
            media_output_db = self.add_media_output(media_output)
        else:
            media_output_data = media_output.model_dump(exclude_unset=True)
            for key, value in media_output_data.items():
                setattr(media_output_db, key, value)
                self.session.add(media_output_db)
                self.session.commit()
                self.session.refresh(media_output_db)
        return media_output_db

    # ServeStep
    def get_steps_by_media_pri_id(self, media_pri_id: str) -> list[ServeStep]:
        media_output = self.get_media_output(media_pri_id)
        if media_output:
            return media_output.steps
        return []
    
    def update_step_status(self, media_pri_id: str, step_type: StepTypeEnum, status: StepStatusEnum) -> ServeRun:
        output_media = self.get_media_output(media_pri_id)
        if output_media:
            step = output_media.get_setp_by_type(step_type)
            if step:
                step.update_status(status)
                self.session.add(step)
                self.session.commit()
                self.session.refresh(step)
            