from typing import List
import yaml
from pydantic import BaseModel
from pydantic_settings import BaseSettings

DATABASE_URL = "sqlite:///serverun.db"
MILVUS_URL = "http://localhost:19530"

class ApiServerConfig(BaseModel):
    port: int
    external_ip: str
    database_url: str = DATABASE_URL  # 添加数据库 URL 配置
    milvus_url: str = MILVUS_URL  # 添加 Milvus URL 配置

class ProcessPoolConfig(BaseModel):
    max_workers: int = 8  # Default to 8 workers
    use_cpu_count: bool = False  # If True, will use CPU count to determine max_workers

class WebHooksConfig(BaseModel):
    max_try_count: int = 20
    max_try_time: int = 300
    on_inference_done: str
    on_model_download_done: str
    on_model_train_done: str
    on_vector_done: str
    on_object_detection_done: str
    on_face_detection_done: str

class LogConfig(BaseSettings):
    level: str = "WARNING"
    console: bool = True
    file: bool = True
    file_path: str = "logs"
    max_bytes: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 100
    json_format: bool = False
    
    class Config:
        env_prefix = "LOG_"

class OnnxProviderConfig(BaseModel):
    enable: bool = True  # 是否启用CANNExecutionProvider
    device_ids: List[int] = [0]  # 默认使用设备0
    arena_extend_strategy: str = "kNextPowerOfTwo"
    npu_mem_limit: int = 2 * 1024 * 1024 * 1024  # 2GB
    op_select_impl_mode: str = "high_performance"
    enable_cann_graph: bool = True

class Settings(BaseSettings):
    debug: bool
    api_server: ApiServerConfig
    onnx_provider: OnnxProviderConfig = OnnxProviderConfig()  # 添加ONNX提供商配置
    process_pool: ProcessPoolConfig = ProcessPoolConfig()  # Add process pool config
    web_hooks: WebHooksConfig
    log: LogConfig = LogConfig()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

class Versions(BaseSettings):
    app_version: str
    git_version: str

    class Config:
        # 指定配置文件路径
        env_file = ".env"  # 如果需要使用环境变量文件
        # 这里不需要指定 yaml 文件，因为我们会手动加载

def load_config(file_path: str) -> Settings:
    with open(file_path, 'r') as file:
        config_data = yaml.safe_load(file)
        return Settings(**config_data)

def load_version(file_path: str) -> Settings:
    with open(file_path, 'r') as file:
        version_data = yaml.safe_load(file)
        return Versions(**version_data)

# 加载配置
settings = load_config("config.yaml")
versions = load_version("version.yaml")

SERVER_MEDIAS_BASE_PATH = 'serve_medias'
