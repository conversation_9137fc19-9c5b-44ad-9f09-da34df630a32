#!/bin/bash

# 生效环境
source deploy/bin/activate
# 获取当前脚本所在目录（确保相对路径正确）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_LIB="$SCRIPT_DIR/deploy/lib/python3.10/site-packages/simsimd./libgomp-98df74fd.so.1.0.0"

# 判断是否为 ARM 架构（如 aarch64、armv7l）
ARCH=$(uname -m)
if [[ "$ARCH" == "aarch64" || "$ARCH" == arm* ]]; then
    export LD_PRELOAD="${LD_PRELOAD}:${DEPLOY_LIB}"
fi

# 执行 Python 脚本
python main.py
