debug: false

# 配置为服务器可供外部访问的IP与端口，供拼接生成文件的下载地址使用
api_server:
  port: 8000
  # external_ip: "127.0.0.1"
  external_ip: "*************"
  # 数据库URL
  database_url: "sqlite:///serverun.db"
  # 向量数据库 Milvus URL
  #milvus_url: "http://localhost:19530"
  milvus_url: "http://***************:19530"

# 华为NPU配置
onnx_provider:
  # 是否启用CANNExecutionProvider
  enable: true
  # 设备ID列表，按顺序分配
  device_ids: [0]
  # 内存扩展策略
  arena_extend_strategy: "kNextPowerOfTwo"
  # NPU内存限制 (2GB)
  npu_mem_limit: **********
  # 操作选择实现模式
  op_select_impl_mode: "high_performance"
  # 启用CANN图
  enable_cann_graph: true

# 进程池配置
process_pool:
  # 最大工作进程数
  max_workers: 8
  # 是否使用CPU核心数作为最大工作进程数
  use_cpu_count: false
  
web_hooks:
  # 回调失败时最大重试次数
  max_try_count: 20
  # 回调失败时最大重试时间，单位秒
  max_try_time: 300
  # 通过接口创建的音频降噪推理任务完成时回调
  on_inference_done: "http://127.0.0.1:8000/api/webhook"
  # on_inference_done: "http://***************:8000/api/webhook"
  # on_inference_done: "http://***************:8888/api/webhook"
  # 创建上传模型任务后，完成模型下载时默认的回调地址
  on_model_download_done: "http://127.0.0.1:8000/api/webhook"
  # 创建模型训练任务后，完成模型训练时默认的回调地址
  on_model_train_done: "http://127.0.0.1:8000/api/webhook"
  # 向量化任务完成后回调
  on_vector_done: "http://127.0.0.1:8000/api/webhook"
  # 创建目标识别推理任务后，完成推理时默认的回调地址
  on_object_detection_done: "http://127.0.0.1:8000/api/webhook"
  # 创建人脸识别推理任务后，完成推理时默认的回调地址
  on_face_detection_done: "http://127.0.0.1:8000/api/webhook"

log:
  # 日志级别，支持 DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "DEBUG"
  console: true
  file: true
  file_path: "logs"
  max_bytes: 10485760  # 10MB
  backup_count: 100
  json_format: false
