

import os
from typing import Any, List, Tuple
import onnxruntime
import numpy as np
import cv2
import time

from cv2.typing import <PERSON><PERSON><PERSON>
from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.object_detection.yolov5_onnx import filter_box
from orm.vector_task_orm import VectorExtendInfo
from serve_utils import crop_image, crop_image_with_path
from utils.logger import get_logger

logger = get_logger()

class ResNet50ONNX(ICannONNX):
    def __init__(self, onnx_path, obj_det_model: ICannONNX = None, device_id=None, provider_options=None):
        super().__init__(onnx_path, device_id, provider_options)
        self.input_name = [input.name for input in self.onnx_session.get_inputs()]
        self.output_name = [output.name for output in self.onnx_session.get_outputs()]
        logger.info("Input:", self.input_name)
        logger.info("Output:", self.output_name)
        self.obj_det_model = obj_det_model

    def preprocess(self, image_path):
        img = cv2.imread(image_path)
        img = cv2.resize(img, (224, 224))  # ResNet50 默认输入尺寸
        img = img[:, :, ::-1]  # BGR to RGB
        img = img.astype(np.float32) / 255.0
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        img = (img - mean) / std
        img = img.transpose(2, 0, 1)  # HWC to CHW
        img = np.expand_dims(img, axis=0)  # Add batch dimension
        return img

    def get_input_feed(self, img_tensor):
        return {name: img_tensor for name in self.input_name}

    # def inference(self, image_path):
    #     img_tensor = self.preprocess(image_path)
    #     input_feed = self.get_input_feed(img_tensor)
    #     start = time.time()
    #     outputs = self.onnx_session.run(None, input_feed)
    #     elapsed = (time.time() - start) * 1000
    #     logger.info(f"Inference time: {elapsed:.2f} ms")
    #     return outputs[0]  # 通常只有一个输出

    def inference(self, img_path):
        img = cv2.imread(img_path)
        return self.inference_image(img)

    def inference_image(self, img):
        img = cv2.resize(img, (224, 224))
        img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR2RGB and HWC2CHW
        img = img.astype(np.float32) / 255.0
        img = np.expand_dims(img, axis=0)
        input_feed = self.get_input_feed(img)
        latency = []
        start = time.time()
        pred = self.onnx_session.run(None, input_feed)[0]
        # logger.info(f"Inference output shape: {pred.shape}")
        latency.append(time.time() - start)
        logger.debug("ResNet50ONNX Runtime CPU/GPU Inference time = {} ms".format(sum(latency) * 1000 / len(latency), '.2f'))
        return pred
    
    def extract_vectors(self, image: np.ndarray, 
                        image_name: str = '',
                        bbox: List[int] = [-1, -1, -1, -1], 
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0) -> List[Tuple[VectorExtendInfo, List[float]]]:
        """
        Extract normalized feature vectors from an image (whole or bbox region).

        Args:
            image_path (str): Path to the input image.
            bbox (List[int]): Bounding box [x1, y1, x2, y2]. If all elements are -1, use the whole image.
            kps (List[List[int]]): Keypoints (unused in this version but kept for future use).

        Returns:
            List of (VectorExtendInfo, vector) tuples.
        """
        vector_tuple_list = []
        if self.obj_det_model:
            logger.info("extract_vectors use obj_det_model")
            det_obj_tuple_list = self.obj_det_model.detect_object(image, bbox)
            logger.info(f"det_obj_tuple_list size: {len(det_obj_tuple_list)}")
            for vector_extend_info, cropped_image in det_obj_tuple_list:
                vectors = self.inference_image(cropped_image)
                vectors = normalize_vector(vectors)
                vector_extend_info.frame_name = image_name
                vector_tuple = (vector_extend_info, vectors[0].tolist())
                vector_tuple_list.append(vector_tuple)
        if len(vector_tuple_list) == 0:
            if bbox != [-1, -1, -1, -1]:
                cropped_image = crop_image(image, bbox)
                vectors = self.inference_image(cropped_image)
            else:
                # Assume image path input for full image
                vectors = self.inference_image(image)
            vectors = normalize_vector(vectors)
            vector_extend_info = VectorExtendInfo(
                frame_name=image_name, 
                bbox=bbox, 
                score=score
            )
            vector_tuple = (vector_extend_info, vectors[0].tolist())
            vector_tuple_list.append(vector_tuple)

        return vector_tuple_list
    
    def reference_frame(self, frame: np.ndarray) -> Tuple[Any, np.ndarray]:
        pass

    def detect_object(self, image: np.ndarray, bbox: List[int] = [-1, -1, -1, -1]) -> List[Tuple[VectorExtendInfo, np.ndarray]]:
        pass

def normalize_vector(v):
    norm = np.linalg.norm(v, axis=1, keepdims=True)
    return v / (norm + 1e-8)  # 防止除以 0


# def run_resnet_vectors(onnx_path, frame_folder, te):
#     model = YOLOV5(onnx_path)
#     image_list = os.listdir(frame_folder)
#     image_list.sort(key=lambda x: int(x.split('_')[1]))
#     for image_name in image_list:
#         image_path = os.path.join(frame_folder, image_name)
#         output, or_img = model.inference(image_path)
#         if output is None:
#             logger.info(f"{image_name} 推理失败")
#             continue
#         outbox = filter_box(output, 0.25, 0.45)
#         logger.info(f"{image_name}：共检测到 {len(outbox)} 个目标")
#         if len(outbox) == 0:
#             logger.info(f"{image_name} 没有有效框")
#             continue
#         # draw image
#         # draw(or_img, outbox, class_list)
#         # cv2.imwrite(os.path.join(frame_folder, 'output_' + image_name), or_img)
#         # write txt
#         write_txt(image_name, outbox, class_list, text_path)
#     return text_path
