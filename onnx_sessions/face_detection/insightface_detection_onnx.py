import ctypes
import os
from pathlib import Path
import platform
import site
import time
from typing import Any, List, <PERSON>ple
import uuid
import cv2
import numpy as np
import onnxruntime
from numpy.linalg import norm as l2norm
import csv

# def get_site_packages_dir():
#     site_package = [p for p in site.getsitepackages()
#                     if "site-packages" in p][0]
#     return site_package

# if platform.system() == "Linux":
#     lib_path = os.path.join(get_site_packages_dir(), "simsimd.", "libgomp-98df74fd.so.1.0.0")
#     os.environ['LD_PRELOAD'] = f"{lib_path}:{os.environ.get('LD_PRELOAD', '')}"
    
from insightface.model_zoo import RetinaFace

from cv2.typing import MatLike
from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.live_stream_publisher import LiveStreamPublisher
from orm.inference_task_orm import FacDetFrameResult
from orm.vector_task_orm import VectorExtendInfo
from routers.download_router import generate_download_url, restore_file_path
from utils.logger import get_logger

logger = get_logger()

class Face(dict):

    def __init__(self, d=None, **kwargs):
        if d is None:
            d = {}
        if kwargs:
            d.update(**kwargs)
        for k, v in d.items():
            setattr(self, k, v)
        # Class attributes
        #for k in self.__class__.__dict__.keys():
        #    if not (k.startswith('__') and k.endswith('__')) and not k in ('update', 'pop'):
        #        setattr(self, k, getattr(self, k))

    def __setattr__(self, name, value):
        if isinstance(value, (list, tuple)):
            value = [self.__class__(x)
                    if isinstance(x, dict) else x for x in value]
        elif isinstance(value, dict) and not isinstance(value, self.__class__):
            value = self.__class__(value)
        super(Face, self).__setattr__(name, value)
        super(Face, self).__setitem__(name, value)

    __setitem__ = __setattr__

    def __getattr__(self, name):
        return None

    @property
    def embedding_norm(self):
        if self.embedding is None:
            return None
        return l2norm(self.embedding)

    @property 
    def normed_embedding(self):
        if self.embedding is None:
            return None
        return self.embedding / self.embedding_norm

    @property 
    def sex(self):
        if self.gender is None:
            return None
        return 'M' if self.gender==1 else 'F'

class InsightFaceDetONNX(ICannONNX):
    def __init__(self, onnx_path, device_id=None, provider_options=None):
        super().__init__(onnx_path, device_id, provider_options)
        # de_onnx_path = "model_zoo/models/buffalo_l/det_10g.onnx"
        # de_onnx_path = "model_zoo/models/buffalo_l/scrfd_person_2.5g.onnx"
        # see@https://github.com/deepinsight/insightface/tree/master/model_zoo#2-face-detection-models

        self.detection = RetinaFace(onnx_path, self.onnx_session)
        self.detection.prepare(ctx_id = 0, input_size=(640, 640), det_thresh=0.5)

    def inference(self, image_path: str) -> list:
        folder_path, file_name = os.path.split(image_path)
        image = cv2.imread(image_path)
        faces = self.inference_image(image)
        # for test
        # rimg = self.draw_on(image, faces)
        # output_file = os.path.join(folder_path, 'insightface_output' + file_name)
        # cv2.imwrite(output_file, rimg)
        return faces

    def inference_image(self, image) -> list:
        latency = []
        start = time.time()
        try: 
            faces = self.get(image)
        except Exception as e:
            logger.info(f"Error processing image: {e}")
            return []
        latency.append(time.time() - start)
        logger.debug("InsightFace detection CPU/GPU Inference time = {} ms".format(sum(latency) * 1000 / len(latency), '.2f'))
        return faces

    def get(self, img, max_num=0):
        bboxes, kpss = self.detection.detect(img,
                                             max_num=max_num,
                                             metric='default')
        if bboxes.shape[0] == 0:
            return []
        ret = []
        for i in range(bboxes.shape[0]):
            bbox = bboxes[i, 0:4]
            det_score = bboxes[i, 4]
            kps = None
            if kpss is not None:
                kps = kpss[i]
            face = Face(bbox=bbox, kps=kps, det_score=det_score)
            # self.recognition.get(img, face)
            ret.append(face)
        return ret
    
    def draw_on(self, img, faces):
        dimg = img.copy()
        for i in range(len(faces)):
            face = faces[i]
            try:
                box = face.bbox.astype(np.int32)
            except Exception as e:
                logger.info(f"Error processing face {i}: {e}")
            # box = face['bbox']  # 假设 bbox 是 [x, y, x+w, y+h] 格式
            # # 将浮点 bbox 转换为整数坐标
            # # 先乘以图像的宽度和高度，再转换为整数
            # box = box * np.array([img.shape[1], img.shape[0], img.shape[1], img.shape[0]])
            # box = box.astype(np.int)
            color = (0, 0, 255)
            cv2.rectangle(dimg, (box[0], box[1]), (box[2], box[3]), color, 2)
            if face.kps is not None:
                kps = face.kps.astype(np.int32)
                #logger.info(landmark.shape)
                for l in range(kps.shape[0]):
                    color = (0, 0, 255)
                    if l == 0 or l == 3:
                        color = (0, 255, 0)
                    cv2.circle(dimg, (kps[l][0], kps[l][1]), 1, color,
                               2)
            if face.gender is not None and face.age is not None:
                cv2.putText(dimg,'%s,%d'%(face.sex,face.age), (box[0]-1, box[1]-4),cv2.FONT_HERSHEY_COMPLEX,0.7,(0,255,0),1)

            #for key, value in face.items():
            #    if key.startswith('landmark_3d'):
            #        logger.info(key, value.shape)
            #        logger.info(value[0:10,:])
            #        lmk = np.round(value).astype(np.int)
            #        for l in range(lmk.shape[0]):
            #            color = (255, 0, 0)
            #            cv2.circle(dimg, (lmk[l][0], lmk[l][1]), 1, color,
            #                       2)
        return dimg
    
    def extract_vectors(self, image: np.ndarray, 
                        image_name: str = '',
                        bbox: List[int] = [-1, -1, -1, -1], 
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0) -> List[Tuple[VectorExtendInfo, List[float]]]:
        pass


    def reference_frame(self, frame: np.ndarray) -> Tuple[Any, np.ndarray]:
        faces = self.inference_image(frame)
        new_frame = self.draw_on(frame, faces)
        return faces, new_frame

    def detect_object(self, image: np.ndarray, bbox: List[int] = [-1, -1, -1, -1]) -> List[Tuple[VectorExtendInfo, np.ndarray]]:
        pass
    

def write_txt(image_name, faces, txt_path, crop_paths=None):
    """
    将人脸检测结果写入CSV文件
    
    Args:
        image_name (str): 图像名称
        faces (List[Face]): 人脸检测结果列表
        txt_path (str): 输出文件路径
        crop_paths (List[str], optional): 裁剪图像路径列表
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(txt_path), exist_ok=True)
    
    # 检查文件是否存在，如果不存在则创建并写入表头
    file_exists = os.path.isfile(txt_path) and os.path.getsize(txt_path) > 0
    
    with open(txt_path, 'a', newline='') as f:
        writer = csv.writer(f)
        
        # 如果文件不存在，写入表头
        if not file_exists:
            writer.writerow(['image_name', 'left', 'top', 'right', 'bottom', 
                            'landmark_x1', 'landmark_y1', 'landmark_x2', 'landmark_y2', 
                            'landmark_x3', 'landmark_y3', 'landmark_x4', 'landmark_y4', 
                            'landmark_x5', 'landmark_y5', 'score', 'crop_path'])
        
        # 写入检测结果
        for i, face in enumerate(faces):
            bbox = face.bbox.astype(np.int32)
            kps = face.kps.astype(np.int32) if face.kps is not None else np.zeros((5, 2), dtype=np.int32)
            
            # 准备写入的行数据
            row = [image_name]
            # 添加边界框坐标
            row.extend([int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])])
            # 添加关键点坐标
            for j in range(5):
                row.extend([int(kps[j][0]), int(kps[j][1])])
            # 添加置信度
            row.append(f"{face.det_score:.4f}")
            
            # 添加裁剪图像路径
            crop_path = ""
            if crop_paths and i < len(crop_paths):
                crop_path = crop_paths[i]
            row.append(crop_path)
            
            writer.writerow(row)


def load_insightface_det_result(result_path: str) -> List[FacDetFrameResult]:
    """
    从CSV文件加载InsightFace人脸检测结果
    
    Args:
        result_path (str): 结果文件路径
        
    Returns:
        List[FacDetFrameResult]: 检测结果对象列表
    """
    path = Path(result_path)
    results = []
    
    if not path.exists():
        logger.info(f"识别结果文件不存在: {result_path}")
        return results
    
    try:
        with open(result_path, 'r', newline='') as f:
            reader = csv.reader(f)
            
            # 跳过表头
            header = next(reader, None)
            if not header:
                logger.info(f"结果文件为空: {result_path}")
                return results
            
            # 检查表头格式是否正确
            expected_header = ['image_name', 'left', 'top', 'right', 'bottom', 
                              'landmark_x1', 'landmark_y1', 'landmark_x2', 'landmark_y2', 
                              'landmark_x3', 'landmark_y3', 'landmark_x4', 'landmark_y4', 
                              'landmark_x5', 'landmark_y5', 'score', 'crop_path']
            if header != expected_header:
                logger.warning(f"结果文件格式不正确: {result_path}, 实际表头: {header}")
                # 尝试继续处理，假设列的顺序是正确的
            
            # 读取数据行
            for row in reader:
                if len(row) < 16:  # 需要至少16个字段
                    logger.warning(f"跳过不完整行: {row}")
                    continue
                
                try:
                    image_name = row[0]
                    left, top, right, bottom = map(int, row[1:5])
                    kps_flat = list(map(int, row[5:15]))
                    kps = [
                        [kps_flat[0], kps_flat[1]],
                        [kps_flat[2], kps_flat[3]],
                        [kps_flat[4], kps_flat[5]],
                        [kps_flat[6], kps_flat[7]],
                        [kps_flat[8], kps_flat[9]]
                    ]
                    score = float(row[15])
                    
                    # 读取裁剪图像路径
                    crop_path = row[16] if len(row) > 16 else ""
                    
                    # 生成下载URL（如果有裁剪图像路径）
                    download_url = ""
                    if crop_path and os.path.exists(crop_path):
                        download_url = generate_download_url(crop_path)
                    
                    # 提取帧索引和时间戳
                    image_name_parts = image_name.split('.')[0].split('_')
                    if len(image_name_parts) >= 3:
                        frame_index = int(image_name_parts[1])
                        frame_pts = int(image_name_parts[2])
                    else:
                        logger.warning(f"无法从图像名称提取帧信息: {image_name}")
                        frame_index = 0
                        frame_pts = 0
                    
                    # 创建结果对象
                    frame_result = FacDetFrameResult(
                        frame_index=frame_index,
                        frame_pts=frame_pts,
                        bbox=[left, top, right, bottom],
                        kps=kps,
                        score=score,
                        download_url=download_url
                    )
                    results.append(frame_result)
                except Exception as e:
                    logger.warning(f"解析行时出错: {e}, 行: {row}")
                    continue
    except Exception as e:
        logger.error(f"读取结果文件时出错: {e}")
    
    logger.info(f"从 {result_path} 加载了 {len(results)} 条人脸检测结果")
    return results

def get_face_result_by_image_name(image_name: str, frame_results: List[FacDetFrameResult]) -> Tuple[np.ndarray, List[str]]:
    """
    根据 image_name 返回人脸检测结果数组。
    每项格式为：
        [left, top, right, bottom, kps_x1, kps_y1, ..., kps_x5, kps_y5, score]

    若无结果，返回空数组，shape 为 (0, 15)
    """
    try:
        image_name = image_name.split('.')[0]  # 去掉扩展名
        parts = image_name.split('_')
        if len(parts) < 3:
            logger.warning(f"非法 image_name 格式: {image_name}")
            return np.empty((0, 15), dtype=np.float32)

        frame_index = int(parts[1])
        frame_pts = int(parts[2])

        matched = [
            r for r in frame_results
            if r.frame_index == frame_index and r.frame_pts == frame_pts
        ]

        if not matched:
            return np.empty((0, 15), dtype=np.float32), []

        result_array = np.array([
            [
                float(r.bbox[0]),
                float(r.bbox[1]),
                float(r.bbox[2]),
                float(r.bbox[3]),
                float(r.kps[0][0]),
                float(r.kps[0][1]),
                float(r.kps[1][0]),
                float(r.kps[1][1]),
                float(r.kps[2][0]),
                float(r.kps[2][1]),
                float(r.kps[3][0]),
                float(r.kps[3][1]),
                float(r.kps[4][0]),
                float(r.kps[4][1]),
                float(r.score),
            ]
            for r in matched
        ], dtype=np.float32)

        crop_paths = [restore_file_path(r.download_url) for r in matched]

        return result_array, crop_paths

    except Exception as e:
        logger.warning(f"获取人脸识别结果失败: {e}")
        return np.empty((0, 15), dtype=np.float32)

def valid_face_result(face_result):
    """
    验证人脸检测结果是否有效
    
    Args:
        face_result: 人脸检测结果数组
        
    Returns:
        bool: 是否有效
    """
    if not isinstance(face_result, np.ndarray):
        return False
    if face_result.ndim == 0:
        return False
    if face_result.shape[-1] < 15:  # 需要至少15个元素 [x1,y1,x2,y2, 5个关键点x,y, score]
        return False
    return True

def run_insightface_det_inference(onnx_path, frame_folder, text_path, task_local_path=None, progress_callback=None):
    """
    运行InsightFace人脸检测推理并将结果保存到CSV文件
    
    :param onnx_path: ONNX模型路径
    :param frame_folder: 帧文件夹路径
    :param text_path: 输出结果文本路径
    :param task_local_path: 任务本地路径，用于保存裁剪图像
    :param progress_callback: 可选的进度回调函数，接收(进度百分比, 消息)作为参数
    :return: 结果文本路径
    """
    # 初始化模型
    if progress_callback:
        progress_callback(0.01, "正在加载人脸检测模型...")
    
    model = InsightFaceDetONNX(onnx_path)
    
    # 获取所有图像文件并排序
    image_list = os.listdir(frame_folder)
    image_list = [img for img in image_list if img.startswith("frame_") and (img.endswith(".jpg") or img.endswith(".png"))]
    image_list.sort(key=lambda x: int(x.split('_')[1]))
    
    total_images = len(image_list)
    if total_images == 0:
        if progress_callback:
            progress_callback(0, "没有找到有效的帧图像")
        logger.info("没有找到有效的帧图像")
        return None
    
    if progress_callback:
        progress_callback(0.05, f"开始处理 {total_images} 个图像帧")
    
    # 确保输出文件目录存在
    os.makedirs(os.path.dirname(text_path), exist_ok=True)
    
    # 清空输出文件（如果存在）- 创建一个新的空文件
    with open(text_path, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['image_name', 'left', 'top', 'right', 'bottom', 
                        'landmark_x1', 'landmark_y1', 'landmark_x2', 'landmark_y2', 
                        'landmark_x3', 'landmark_y3', 'landmark_x4', 'landmark_y4', 
                        'landmark_x5', 'landmark_y5', 'score', 'crop_path'])
    
    # 处理开始时间
    start_time = time.time()
    processed_frames = 0
    detected_faces = 0
    
    # 处理每一帧
    for i, image_name in enumerate(image_list):
        # 计算进度
        progress = 0.05 + (i / total_images) * 0.9
        
        # 计算处理速度和预计剩余时间
        if i > 0:
            elapsed = time.time() - start_time
            frames_per_second = processed_frames / elapsed if elapsed > 0 else 0
            remaining_frames = total_images - processed_frames
            remaining_time = remaining_frames / frames_per_second if frames_per_second > 0 else 0
            
            # 格式化剩余时间
            if remaining_time < 60:
                time_str = f"{remaining_time:.0f}秒"
            elif remaining_time < 3600:
                time_str = f"{remaining_time/60:.1f}分钟"
            else:
                time_str = f"{remaining_time/3600:.1f}小时"
            
            speed_message = f", 处理速度: {frames_per_second:.1f}帧/秒, 预计剩余: {time_str}"
        else:
            speed_message = ""
        
        if progress_callback and i % max(1, total_images // 100) == 0:  # 每1%更新一次进度
            progress_callback(progress, f"处理第 {i+1}/{total_images} 帧 ({progress:.1%}){speed_message}")
        
        image_path = os.path.join(frame_folder, image_name)
        faces = model.inference(image_path)
        processed_frames += 1
        
        if faces is None:
            logger.info(f"{image_name} 推理失败")
            continue
        
        detected_faces += len(faces)
        logger.debug(f"{image_name}：检测到 {len(faces)} 个人脸")
        
        # 裁剪人脸区域
        crop_paths = []
        if task_local_path is not None and len(faces) > 0:
            image = cv2.imread(image_path)
            crop_paths = crop_face_area(image, faces, task_local_path, image_name)
        
        # 写入结果
        write_txt(image_name, faces, text_path, crop_paths)
        
        # 可选：生成可视化结果
        if i % 10 == 0 or i == total_images - 1:  # 每10帧或最后一帧生成可视化
            image = cv2.imread(image_path)
            result_image = model.draw_on(image, faces)
            output_file = os.path.join(frame_folder, f'face_det_{image_name}')
            cv2.imwrite(output_file, result_image)
    
    if progress_callback:
        # 计算最终处理速度
        total_time = time.time() - start_time
        frames_per_second = total_images / total_time if total_time > 0 else 0
        faces_per_frame = detected_faces / total_images if total_images > 0 else 0
        
        progress_callback(1.0, f"人脸检测完成，共处理 {total_images} 帧，" +
                         f"检测到 {detected_faces} 个人脸 (平均 {faces_per_frame:.1f}个/帧)，" +
                         f"平均速度: {frames_per_second:.1f}帧/秒，" +
                         f"结果保存至 {text_path}")
    
    return text_path

def run_live_reference(onnx_path, source_url, target_rtmp_url):
    """处理直播流并实时推送到新的 RTMP 地址"""
    model = InsightFaceDetONNX(onnx_path)

    publisher = LiveStreamPublisher(model, source_url, target_rtmp_url)
    publisher.start()

    return target_rtmp_url, publisher

def valid_face_result(face_result):
    if not isinstance(face_result, np.ndarray):
        return False
    if face_result.ndim == 0:
        return False
    if face_result.shape[-1] < 15:
        return False
    return True

def build_np_face_result(faces: List[Face]) -> np.ndarray:
    """
    将Face对象列表转换为numpy数组格式
    
    Args:
        faces (List[Face]): Face对象列表
        
    Returns:
        np.ndarray: 人脸检测结果数组，shape为(N, 15)
                    每行格式为[left, top, right, bottom, kps_x1, kps_y1, ..., kps_x5, kps_y5, score]
    """
    if not faces:
        return np.empty((0, 15), dtype=np.float32)
    
    result = []
    for face in faces:
        if not hasattr(face, 'bbox') or not hasattr(face, 'kps') or not hasattr(face, 'det_score'):
            continue
            
        bbox = face.bbox.astype(np.float32)
        kps = face.kps.astype(np.float32) if face.kps is not None else np.zeros((5, 2), dtype=np.float32)
        score = face.det_score
        
        # 构建结果行: [left, top, right, bottom, kps_x1, kps_y1, ..., kps_x5, kps_y5, score]
        row = np.concatenate([
            bbox,                  # [left, top, right, bottom]
            kps.flatten(),         # [kps_x1, kps_y1, ..., kps_x5, kps_y5]
            np.array([score])      # [score]
        ])
        
        result.append(row)
    
    return np.array(result, dtype=np.float32) if result else np.empty((0, 15), dtype=np.float32)

def crop_face_area(origin_image: np.ndarray, faces: List[Face], local_path: str, image_name: str) -> List[str]:
    """
    裁切并保存检测到的人脸区域
    
    Args:
        origin_image: 原始图像
        faces: 人脸检测结果列表
        local_path: 本地保存路径
        image_name: 图像名称
        
    Returns:
        List[str]: 裁剪图像路径列表
    """
    crop_paths = []
    if local_path is None or len(faces) == 0:
        return crop_paths
    
    # 创建裁剪图像目录
    crop_dir = os.path.join(local_path, "crops", "fac_det")
    os.makedirs(crop_dir, exist_ok=True)
    
    # 提取帧索引和时间戳
    image_name_parts = image_name.split('.')[0].split('_')
    if len(image_name_parts) >= 3:
        frame_index = int(image_name_parts[1])
        frame_pts = int(image_name_parts[2])
    else:
        logger.warning(f"无法从图像名称提取帧信息: {image_name}")
        frame_index = 0
        frame_pts = 0
    
    # 对每个人脸进行裁剪
    for i, face in enumerate(faces):
        try:
            # 获取边界框
            bbox = face.bbox.astype(np.int32)
            left, top, right, bottom = bbox
            
            # 确保边界框在图像范围内
            h, w = origin_image.shape[:2]
            left = max(0, left)
            top = max(0, top)
            right = min(w, right)
            bottom = min(h, bottom)
            
            # 检查边界框是否有效
            if left >= right or top >= bottom:
                logger.warning(f"无效的边界框: {bbox}")
                crop_paths.append("")
                continue
            
            # 生成唯一文件名
            crop_filename = f"fac_det_frame_{frame_index:08d}_{frame_pts:08d}_det_{i:04d}_{uuid.uuid1()}.jpg"
            crop_path = os.path.join(crop_dir, crop_filename)
            
            # 裁剪并保存
            crop_img = origin_image[top:bottom, left:right]
            cv2.imwrite(crop_path, crop_img, [cv2.IMWRITE_JPEG_QUALITY, 95])
            crop_paths.append(crop_path)
        except Exception as e:
            logger.error(f"保存裁剪图像时出错: {e}")
            crop_paths.append("")
    
    return crop_paths
