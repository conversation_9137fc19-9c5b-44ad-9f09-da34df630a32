from collections import Counter
import csv
import os
from pathlib import Path
import time
from typing import Any, List, Tuple
import uuid
import cv2
import numpy as np
import onnxruntime
from numpy.linalg import norm as l2norm

from insightface.model_zoo import ArcFaceONNX

from cv2.typing import <PERSON><PERSON><PERSON>
from scipy.special import softmax
from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.face_detection.insightface_detection_onnx import Face, InsightFaceDetONNX
from orm.inference_task_orm import FacRecFrameResult
from orm.media_orm import MediaExtend
from orm.vector_task_orm import VectorExtendInfo
from routers.download_router import generate_download_url
from serve_utils import crop_image, is_valid_bbox, is_valid_kps
from utils.logger import get_logger

logger = get_logger()

class InsightFaceRecONNX(ICannONNX):
    def __init__(self, onnx_path, face_detection: InsightFaceDetONNX=None, device_id=None, provider_options=None,
                 vectors: List[List[float]]=None, media_extends: List[MediaExtend]=None, similarity_threshold: float=0.2):
        super().__init__(onnx_path, device_id, provider_options)
        # re_onnx_path = "model_zoo/models/buffalo_l/w600k_r50.onnx"

        self.recognition = ArcFaceONNX(onnx_path, self.onnx_session)
        self.recognition.prepare(ctx_id = 0)

        self.detection = face_detection
        
        # 存储已知人脸信息
        self.known_vectors = vectors if vectors is not None else []
        self.media_extends = media_extends if media_extends is not None else []
        self.similarity_threshold = similarity_threshold
        
        # 验证已知人脸数据的一致性
        if len(self.known_vectors) != len(self.media_extends):
            logger.warning(f"已知人脸向量数量 ({len(self.known_vectors)}) 与媒体扩展信息数量 ({len(self.media_extends)}) 不匹配")
            # 使用较小的长度
            min_len = min(len(self.known_vectors), len(self.media_extends))
            self.known_vectors = self.known_vectors[:min_len]
            self.media_extends = self.media_extends[:min_len]

    # def match_known_face(self, face_vector: List[float]) -> Tuple[int, str, float]:
    #     """
    #     将人脸向量与已知人脸进行匹配
        
    #     Args:
    #         face_vector (List[float]): 人脸特征向量
            
    #     Returns:
    #         Tuple[int, str, float]: (类别ID, 类别名称, 相似度分数)
    #             如果没有匹配到已知人脸，返回 (-1, "", 0.0)
    #     """
    #     if not self.known_vectors or not self.media_extends:
    #         return -1, "", 0.0
            
    #     max_similarity = -1
    #     best_match_idx = -1
        
    #     for idx, known_vector in enumerate(self.known_vectors):
    #         # 计算余弦相似度
    #         similarity = np.dot(face_vector, known_vector) / (np.linalg.norm(face_vector) * np.linalg.norm(known_vector))
            
    #         if similarity > max_similarity and similarity > self.similarity_threshold:
    #             max_similarity = similarity
    #             best_match_idx = idx
        
    #     # 如果找到匹配的人脸
    #     if best_match_idx >= 0:
    #         media_extend = self.media_extends[best_match_idx]
    #         if hasattr(media_extend, 'recognition_target_id') and hasattr(media_extend, 'recognition_target_name'):
    #             return media_extend.recognition_target_id, media_extend.recognition_target_name, max_similarity
        
    #     return -1, "", 0.0
    
    # def match_known_face(self, face_vector: List[float], top_k: int = 5, det_score: float = 1.0) -> Tuple[int, str, float]:
    #     """
    #     将人脸向量与已知人脸进行匹配，使用多种策略提高准确度
        
    #     Args:
    #         face_vector (List[float]): 人脸特征向量
    #         top_k (int): 考虑的候选人脸数量，默认为5
    #         det_score (float): 人脸检测置信度分数，默认为1.0
        
    #     Returns:
    #         Tuple[int, str, float]: (类别ID, 类别名称, 相似度分数)
    #             如果没有匹配到已知人脸，返回 (-1, "", 0.0)
    #     """
    #     if not self.known_vectors or not self.media_extends:
    #         return -1, "", 0.0
        
    #     # 1. 动态阈值：根据检测分数调整相似度阈值
    #     dynamic_threshold = self.similarity_threshold
    #     if det_score < 0.9:
    #         # 低检测分数时提高匹配阈值要求
    #         dynamic_threshold = self.similarity_threshold + (0.9 - det_score) * 0.1
        
    #     # 2. 计算所有已知人脸向量与输入向量的余弦相似度
    #     similarities = []
    #     for known_vector in self.known_vectors:
    #         # 计算余弦相似度
    #         similarity = np.dot(face_vector, known_vector) / (np.linalg.norm(face_vector) * np.linalg.norm(known_vector))
    #         similarities.append(similarity)
        
    #     similarities = np.array(similarities)
        
    #     # 3. 找出相似度最高的top_k个候选人脸
    #     if len(similarities) <= top_k:
    #         candidate_indices = np.argsort(similarities)[::-1]
    #     else:
    #         candidate_indices = np.argpartition(similarities, -top_k)[-top_k:]
    #         candidate_indices = candidate_indices[np.argsort(similarities[candidate_indices])[::-1]]
        
    #     # 4. 过滤掉相似度低于动态阈值的候选人
    #     filtered_indices = [idx for idx in candidate_indices if similarities[idx] >= dynamic_threshold]
        
    #     if not filtered_indices:
    #         return -1, "", 0.0
        
    #     # 5. 如果只有一个候选人，直接返回
    #     if len(filtered_indices) == 1:
    #         best_idx = filtered_indices[0]
    #         media_extend = self.media_extends[best_idx]
    #         if hasattr(media_extend, 'recognition_target_id') and hasattr(media_extend, 'recognition_target_name'):
    #             return media_extend.recognition_target_id, media_extend.recognition_target_name, similarities[best_idx]
    #         return -1, "", 0.0
        
    #     # 6. 使用加权投票机制确定最终标签
    #     from collections import Counter
        
    #     # 计算每个候选人的权重（相似度 * 检测分数）
    #     weighted_votes = {}
    #     for idx in filtered_indices:
    #         media_extend = self.media_extends[idx]
    #         if hasattr(media_extend, 'recognition_target_id') and hasattr(media_extend, 'recognition_target_name'):
    #             target_id = media_extend.recognition_target_id
    #             weight = similarities[idx] * det_score
            
    #             if target_id not in weighted_votes:
    #                 weighted_votes[target_id] = 0
    #             weighted_votes[target_id] += weight
        
    #     if not weighted_votes:
    #         return -1, "", 0.0
        
    #     # 7. 找出权重最高的ID
    #     most_common_id = max(weighted_votes.items(), key=lambda x: x[1])[0]
        
    #     # 8. 计算最高相似度的匹配结果
    #     best_idx = -1
    #     max_similarity = -1
        
    #     for idx in filtered_indices:
    #         media_extend = self.media_extends[idx]
    #         if (hasattr(media_extend, 'recognition_target_id') and 
    #             media_extend.recognition_target_id == most_common_id and 
    #             similarities[idx] > max_similarity):
    #             max_similarity = similarities[idx]
    #             best_idx = idx
        
    #     if best_idx >= 0:
    #         media_extend = self.media_extends[best_idx]
    #         return media_extend.recognition_target_id, media_extend.recognition_target_name, max_similarity
        
    #     return -1, "", 0.0

    def match_known_face(
        self, face_vector: List[float], top_k: int = 5, det_score: float = 1.0
    ) -> Tuple[str, str, float]:
        """
        使用 Glint360K 特征向量匹配人脸，结合 softmax 加权和相似度阈值提高鲁棒性。
        
        Returns:
            Tuple[int, str, float]: (target_id, target_name, similarity)
        """
        import numpy as np
        from collections import defaultdict
        from scipy.special import softmax

        if not self.known_vectors or not self.media_extends:
            return "", "", 0.0

        # === 1. 归一化输入向量（Glint360K 特征需单位向量）===
        face_vector = np.array(face_vector)
        face_vector /= np.linalg.norm(face_vector)

        known_vectors = np.array([
            v / np.linalg.norm(v) if np.linalg.norm(v) > 0 else v
            for v in self.known_vectors
        ])

        # === 2. 计算余弦相似度 ===
        similarities = np.dot(known_vectors, face_vector)  # shape: [N]

        # === 3. 获取 top_k 候选索引 ===
        top_k = min(top_k, len(similarities))
        candidate_indices = np.argsort(similarities)[-top_k:][::-1]

        # === 4. 应用动态阈值 ===
        dynamic_threshold = self.similarity_threshold
        if det_score < 0.9:
            dynamic_threshold += (0.9 - det_score) * 0.1

        filtered = [
            (idx, similarities[idx])
            for idx in candidate_indices
            if similarities[idx] >= dynamic_threshold
        ]

        if not filtered:
            return "", "", 0.0

        # === 5. softmax 加权投票，根据 recognition_target_id 分组 ===
        candidate_scores = defaultdict(float)
        candidate_sims = defaultdict(list)

        sims = np.array([sim for _, sim in filtered])
        weights = softmax(sims * det_score)

        for i, (idx, sim) in enumerate(filtered):
            media_extend = self.media_extends[idx]
            if not hasattr(media_extend, "recognition_target_id"):
                continue
            target_id = media_extend.recognition_target_id
            candidate_scores[target_id] += weights[i]
            candidate_sims[target_id].append(sim)

        if not candidate_scores:
            return "", "", 0.0

        # === 6. 选择得票最高的ID ===
        best_target_id = max(candidate_scores.items(), key=lambda x: x[1])[0]

        # === 7. 打印该ID的相似度均值 ===
        sims_list = candidate_sims[best_target_id]
        mean_sim = np.mean(sims_list)
        max_sim = np.max(sims_list)
        logger.info(f"[match_known_face] ID={best_target_id} 平均相似度={mean_sim:.4f}, 最大={max_sim:.4f}, 阈值={dynamic_threshold:.4f}")

        # === 8. 找出 best_target_id 中最高分的样本 ===
        best_idx = -1
        best_sim = -1.0
        best_name = ""
        for idx, sim in filtered:
            media_extend = self.media_extends[idx]
            if (hasattr(media_extend, "recognition_target_id") and
                media_extend.recognition_target_id == best_target_id and
                sim > best_sim):
                best_idx = idx
                best_sim = sim
                best_name = getattr(media_extend, "recognition_target_name", "")

        if best_idx >= 0:
            return best_target_id, best_name, best_sim
        else:
            return "", "", 0.0


    def extract_vectors(self, image: np.ndarray, 
                        image_name: str = '',
                        bbox: List[int] = [-1, -1, -1, -1], 
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0) -> List[Tuple[VectorExtendInfo, List[float]]]:
        latency = []
        start = time.time()
        faces = []
        vector_tuples = []
        try: 
            # 处理关键点和边界框
            if is_valid_kps(kps, image.shape[:2]):
                bbox_array = np.array(bbox, dtype=np.float32)
                kps_array = np.array(kps, dtype=np.float32)
                face = Face(bbox=bbox_array, kps=kps_array, det_score=score)
                faces = [face]
            elif self.detection is not None:
                faces = self.detection.get(image)
                if len(faces) == 0:
                    logger.info("No faces detected in the image.")
            
            # 处理没有检测到人脸的情况
            if len(faces) == 0:
                if is_valid_bbox(bbox, image.shape[:2]):
                    # 使用提供的边界框
                    cropped_image = crop_image(image, bbox)
                    input_size = self.recognition.input_size
                    resize_image = cv2.resize(cropped_image, input_size)
                    face_embedding = self.recognition.get_feat(resize_image).flatten()
                    latency.append(time.time() - start)
                    logger.debug("InsightFace recognition embedding bbox image time = {} ms".format(sum(latency) * 1000 / len(latency), '.2f'))
                    embedding = l2_normalize(face_embedding)
                    
                    # 创建向量扩展信息
                    vector_extend_info = VectorExtendInfo(
                        bbox=bbox,
                        frame_name=image_name,
                        score=score
                    )
                    
                    # 匹配已知人脸并填充类别信息
                    if self.known_vectors and self.media_extends:
                        target_id, target_name, rec_score = self.match_known_face(embedding.tolist(), det_score=score)
                        if target_id:  # 如果匹配到已知人脸
                            vector_extend_info.class_id = target_id
                            vector_extend_info.class_name = target_name
                            vector_extend_info.rec_score = rec_score  # 更新分数为识别分数
                    
                    vector_tuples.append((vector_extend_info, embedding.tolist()))
                elif bbox == [-2, -2, -2, -2]:
                    # 嵌入整个图像
                    input_size = self.recognition.input_size
                    resize_image = cv2.resize(image, input_size)
                    face_embedding = self.recognition.get_feat(resize_image).flatten()
                    latency.append(time.time() - start)
                    logger.debug("InsightFace recognition embedding full image time = {} ms".format(sum(latency) * 1000 / len(latency), '.2f'))
                    embedding = l2_normalize(face_embedding)
                    
                    # 创建向量扩展信息
                    vector_extend_info = VectorExtendInfo(
                        bbox=[-1, -1, -1, -1],
                        frame_name=image_name,
                        score=-1.0
                    )
                    
                    # 匹配已知人脸并填充类别信息
                    if self.known_vectors and self.media_extends:
                        target_id, target_name, rec_score = self.match_known_face(embedding.tolist(), det_score=-1.0)
                        if target_id:  # 如果匹配到已知人脸
                            vector_extend_info.class_id = target_id
                            vector_extend_info.class_name = target_name
                            vector_extend_info.rec_score = rec_score  # 更新分数为识别分数
                    
                    vector_tuples.append((vector_extend_info, embedding.tolist()))
            
            # 处理检测到的人脸
            for face in faces:
                self.recognition.get(image, face)
                latency.append(time.time() - start)
                logger.debug("InsightFace recognition embedding face time = {} ms".format(sum(latency) * 1000 / len(latency), '.2f'))
                
                # 对 embedding 做归一化并转成 list[float]
                if hasattr(face, 'embedding') and face.embedding is not None:
                    embedding = l2_normalize(face.embedding)
                    
                    # 创建向量扩展信息
                    vector_extend_info = VectorExtendInfo(
                        bbox=face.bbox.astype(np.int32),
                        frame_name=image_name,
                        kps=face.kps.astype(np.int32),
                        score=face.det_score
                    )
                    
                    # 匹配已知人脸并填充类别信息
                    if self.known_vectors and self.media_extends:
                        target_id, target_name, rec_score = self.match_known_face(embedding.tolist(), det_score=face.det_score)
                        if target_id:  # 如果匹配到已知人脸
                            vector_extend_info.class_id = target_id
                            vector_extend_info.class_name = target_name
                            vector_extend_info.rec_score = rec_score  # 更新分数为识别分数
                    
                    vector_tuples.append((vector_extend_info, embedding.tolist()))
                else:
                    return []
        except Exception as e:
            logger.info(f"Error processing image: {e}")
            return []

        return vector_tuples
        
    def reference_frame(self, frame: np.ndarray) -> Tuple[Any, np.ndarray]:
        """
        处理视频帧，进行人脸检测和识别，并与已知人脸进行比对
        
        Args:
            frame (np.ndarray): 输入视频帧
            
        Returns:
            Tuple[Any, np.ndarray]: (识别结果, 处理后的帧)
        """
        # 复制输入帧以便绘制
        result_frame = frame.copy()
        
        # 提取人脸向量（现在已包含识别结果）
        vector_tuples = self.extract_vectors(frame)
        
        # 如果没有检测到人脸，直接返回原始帧
        if not vector_tuples:
            return [], result_frame
        
        # 处理每个检测到的人脸
        face_results = []
        for vector_info, face_vector in vector_tuples:
            # 获取人脸边界框和关键点
            bbox = vector_info.bbox
            kps = vector_info.kps if hasattr(vector_info, 'kps') else None
            score = vector_info.score if hasattr(vector_info, 'score') else 0.0
            
            # 直接从vector_info获取类别信息
            class_id = getattr(vector_info, 'class_id', "")
            class_name = getattr(vector_info, 'class_name', "")
            rec_score = score  # 使用vector_info中的score作为识别分数
            
            # 构建人脸结果
            face_result = {
                'bbox': bbox,
                'kps': kps,
                'score': score,
                'embedding': face_vector,
                'class_id': class_id,
                'class_name': class_name,
                'rec_score': rec_score
            }
            face_results.append(face_result)
            
            # 绘制边界框 - 已识别的人脸用绿色，未识别的用红色
            color = (0, 255, 0) if class_id != "" else (0, 0, 255)
            cv2.rectangle(result_frame, 
                         (int(bbox[0]), int(bbox[1])), 
                         (int(bbox[2]), int(bbox[3])), 
                         color, 2)
            
            # 绘制关键点
            if kps is not None:
                for l in range(kps.shape[0]):
                    pt_color = (0, 0, 255)
                    if l == 0 or l == 3:  # 眼睛关键点用绿色
                        pt_color = (0, 255, 0)
                    cv2.circle(result_frame, 
                              (int(kps[l][0]), int(kps[l][1])), 
                              1, pt_color, 2)
            
            # 显示身份信息和置信度
            if class_name:
                label = f"{class_name} ({rec_score:.2f})"
            else:
                label = f"Unknown ({score:.2f})"
                
            cv2.putText(result_frame, 
                       label, 
                       (int(bbox[0]), int(bbox[1])-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 
                       0.5, color, 2)
        
        return face_results, result_frame

    def detect_object(self, image: np.ndarray, bbox: List[int] = [-1, -1, -1, -1]) -> List[Tuple[VectorExtendInfo, np.ndarray]]:
        pass

    def draw_on(self, img, faces):
        dimg = img.copy()
        for i in range(len(faces)):
            face = faces[i]
            try:
                box = face.bbox.astype(np.int32)
            except Exception as e:
                logger.info(f"Error processing face {i}: {e}")
            # box = face['bbox']  # 假设 bbox 是 [x, y, x+w, y+h] 格式
            # # 将浮点 bbox 转换为整数坐标
            # # 先乘以图像的宽度和高度，再转换为整数
            # box = box * np.array([img.shape[1], img.shape[0], img.shape[1], img.shape[0]])
            # box = box.astype(np.int)
            color = (0, 0, 255)
            cv2.rectangle(dimg, (box[0], box[1]), (box[2], box[3]), color, 2)
            if face.kps is not None:
                kps = face.kps.astype(np.int32)
                #logger.info(landmark.shape)
                for l in range(kps.shape[0]):
                    color = (0, 0, 255)
                    if l == 0 or l == 3:
                        color = (0, 255, 0)
                    cv2.circle(dimg, (kps[l][0], kps[l][1]), 1, color,
                               2)
            if face.gender is not None and face.age is not None:
                cv2.putText(dimg,'%s,%d'%(face.sex,face.age), (box[0]-1, box[1]-4),cv2.FONT_HERSHEY_COMPLEX,0.7,(0,255,0),1)

            #for key, value in face.items():
            #    if key.startswith('landmark_3d'):
            #        logger.info(key, value.shape)
            #        logger.info(value[0:10,:])
            #        lmk = np.round(value).astype(np.int)
            #        for l in range(lmk.shape[0]):
            #            color = (255, 0, 0)
            #            cv2.circle(dimg, (lmk[l][0], lmk[l][1]), 1, color,
            #                       2)
        return dimg

def l2_normalize(vec: np.ndarray) -> np.ndarray:
    norm = np.linalg.norm(vec)
    return vec if norm == 0 else vec / norm


def write_csv(image_name, face_results, text_path, crop_paths=None):
    """
    将人脸识别结果写入CSV文件
    
    Args:
        image_name (str): 图像名称
        face_results (List[dict]): 人脸识别结果列表
        text_path (str): 输出文件路径
        crop_paths (List[str], optional): 裁剪图像路径列表
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(text_path), exist_ok=True)
    
    # 检查文件是否存在，如果不存在则创建并写入表头
    file_exists = os.path.isfile(text_path) and os.path.getsize(text_path) > 0
    
    with open(text_path, 'a', newline='') as f:
        writer = csv.writer(f)
        
        # 如果文件不存在，写入表头
        if not file_exists:
            writer.writerow(['image_name', 'left', 'top', 'right', 'bottom', 
                            'landmark_x1', 'landmark_y1', 'landmark_x2', 'landmark_y2', 
                            'landmark_x3', 'landmark_y3', 'landmark_x4', 'landmark_y4', 
                            'landmark_x5', 'landmark_y5', 'score', 'crop_path', 
                            'class_id', 'class_name', 'rec_score'])
        
        # 写入识别结果
        for idx, face in enumerate(face_results):
            bbox = face['bbox']
            kps = face['kps'] if face['kps'] is not None else np.zeros((5, 2), dtype=np.int32)
            
            # 准备写入的行数据
            row = [image_name]
            # 添加边界框坐标
            row.extend([int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])])
            # 添加关键点坐标
            for j in range(5):
                row.extend([int(kps[j][0]), int(kps[j][1])])
            # 添加置信度
            row.append(f"{face['score']:.4f}")
            
            # 添加裁剪图像路径
            crop_path = ""
            if 'crop_path' in face:
                crop_path = face['crop_path']
            row.append(crop_path)
            
            # 添加类别ID和名称
            row.append(face['class_id'])
            row.append(face['class_name'])
            
            # 添加识别分数
            rec_score = face.get('rec_score', 0.0)
            row.append(f"{rec_score:.4f}")
            
            writer.writerow(row)

def load_insightface_rec_result(result_path: str) -> List[FacRecFrameResult]:
    """
    从CSV文件加载InsightFace人脸识别结果
    
    Args:
        result_path (str): 结果文件路径
        
    Returns:
        List[FacRecFrameResult]: 识别结果对象列表
    """
    path = Path(result_path)
    results = []
    
    if not path.exists():
        logger.info(f"识别结果文件不存在: {result_path}")
        return results
    
    try:
        with open(result_path, 'r', newline='') as f:
            reader = csv.reader(f)
            
            # 跳过表头
            header = next(reader, None)
            if not header:
                logger.info(f"结果文件为空: {result_path}")
                return results
            
            # 检查表头格式是否正确
            expected_header = ['image_name', 'left', 'top', 'right', 'bottom', 
                              'landmark_x1', 'landmark_y1', 'landmark_x2', 'landmark_y2', 
                              'landmark_x3', 'landmark_y3', 'landmark_x4', 'landmark_y4', 
                              'landmark_x5', 'landmark_y5', 'score', 'crop_path', 
                              'class_id', 'class_name', 'rec_score']
            if len(header) < len(expected_header):
                logger.warning(f"结果文件格式不正确: {result_path}, 实际表头: {header}")
                # 尝试继续处理，假设列的顺序是正确的
            
            # 读取数据行
            for row in reader:
                if len(row) < 18:  # 需要至少18个字段
                    logger.warning(f"跳过不完整行: {row}")
                    continue
                
                try:
                    image_name = row[0]
                    left, top, right, bottom = map(int, row[1:5])
                    kps_flat = list(map(int, row[5:15]))
                    kps = [
                        [kps_flat[0], kps_flat[1]],
                        [kps_flat[2], kps_flat[3]],
                        [kps_flat[4], kps_flat[5]],
                        [kps_flat[6], kps_flat[7]],
                        [kps_flat[8], kps_flat[9]]
                    ]
                    score = float(row[15])
                    
                    # 读取裁剪图像路径
                    crop_path = row[16] if len(row) > 16 else ""
                    
                    # 读取类别ID和名称
                    class_id = row[17] if len(row) > 17 else "-1"
                    class_name = row[18] if len(row) > 18 else ""
                    
                    # 读取识别分数
                    rec_score = float(row[19]) if len(row) > 19 else 0.0
                    
                    # 生成下载URL（如果有裁剪图像路径）
                    download_url = ""
                    if crop_path and os.path.exists(crop_path):
                        download_url = generate_download_url(crop_path)
                    
                    # 提取帧索引和时间戳
                    image_name_parts = image_name.split('.')[0].split('_')
                    if len(image_name_parts) >= 3:
                        frame_index = int(image_name_parts[1])
                        frame_pts = int(image_name_parts[2])
                    else:
                        logger.warning(f"无法从图像名称提取帧信息: {image_name}")
                        frame_index = 0
                        frame_pts = 0
                    
                    # 创建结果对象
                    frame_result = FacRecFrameResult(
                        frame_index=frame_index,
                        frame_pts=frame_pts,
                        bbox=[left, top, right, bottom],
                        kps=kps,
                        score=score,
                        download_url=download_url,
                        class_id=class_id,
                        class_name=class_name,
                        rec_score=rec_score
                    )
                    results.append(frame_result)
                except Exception as e:
                    logger.warning(f"解析行时出错: {e}, 行: {row}")
                    continue
    except Exception as e:
        logger.error(f"读取结果文件时出错: {e}")
    
    logger.info(f"从 {result_path} 加载了 {len(results)} 条人脸识别结果")
    return results

def run_insightface_rec_inference(onnx_path, frame_folder, text_path, task_local_path=None, 
                                  fac_det_model: InsightFaceDetONNX = None, vectors: list[list[float]] = [], 
                                  media_extends: list[MediaExtend] = [], progress_callback=None,
                                  similarity_threshold: float = 0.5):
    """
    运行InsightFace人脸识别推理并将结果保存到CSV文件
    
    :param onnx_path: ONNX模型路径
    :param frame_folder: 帧文件夹路径
    :param text_path: 输出结果文本路径
    :param task_local_path: 任务本地路径，用于保存裁剪图像
    :param fac_det_model: 人脸检测模型实例
    :param vectors: 已知人脸的向量数据列表
    :param media_extends: 已知人脸的媒体扩展信息列表
    :param progress_callback: 可选的进度回调函数，接收(进度百分比, 消息)作为参数
    :param similarity_threshold: 人脸匹配的相似度阈值，默认为0.5
    :return: 结果文本路径
    """
    # 初始化模型
    if progress_callback:
        progress_callback(0.01, "正在加载人脸识别模型...")
    
    # 使用传入的人脸检测模型或创建新的
    if fac_det_model is None:
        det_model_path = "model_zoo/insightface_buffalo_l_det_10g.onnx"
        fac_det_model = InsightFaceDetONNX(det_model_path)
    
    # 初始化人脸识别模型，传入已知人脸信息
    rec_model = InsightFaceRecONNX(
        onnx_path, 
        fac_det_model, 
        vectors=vectors, 
        media_extends=media_extends,
        similarity_threshold=similarity_threshold
    )
    
    # 获取所有图像文件并排序
    image_list = os.listdir(frame_folder)
    image_list = [img for img in image_list if img.startswith("frame_") and (img.endswith(".jpg") or img.endswith(".png"))]
    image_list.sort(key=lambda x: int(x.split('_')[1]))
    
    total_images = len(image_list)
    if total_images == 0:
        if progress_callback:
            progress_callback(0, "没有找到有效的帧图像")
        logger.info("没有找到有效的帧图像")
        return None
    
    if progress_callback:
        progress_callback(0.05, f"开始处理 {total_images} 个图像帧")
    
    # 确保输出文件目录存在
    os.makedirs(os.path.dirname(text_path), exist_ok=True)
    
    # 清空输出文件（如果存在）- 创建一个新的空文件
    with open(text_path, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['image_name', 'left', 'top', 'right', 'bottom', 
                        'landmark_x1', 'landmark_y1', 'landmark_x2', 'landmark_y2', 
                        'landmark_x3', 'landmark_y3', 'landmark_x4', 'landmark_y4', 
                        'landmark_x5', 'landmark_y5', 'score', 'crop_path', 
                        'class_id', 'class_name', 'rec_score'])
    
    # 处理开始时间
    start_time = time.time()
    processed_frames = 0
    detected_faces = 0
    recognized_faces = 0
    
    # 处理每一帧
    for i, image_name in enumerate(image_list):
        # 计算进度
        progress = 0.05 + (i / total_images) * 0.9
        
        # 计算处理速度和预计剩余时间
        if i > 0:
            elapsed = time.time() - start_time
            frames_per_second = processed_frames / elapsed if elapsed > 0 else 0
            remaining_frames = total_images - processed_frames
            remaining_time = remaining_frames / frames_per_second if frames_per_second > 0 else 0
            
            # 格式化剩余时间
            if remaining_time < 60:
                time_str = f"{remaining_time:.0f}秒"
            elif remaining_time < 3600:
                time_str = f"{remaining_time/60:.1f}分钟"
            else:
                time_str = f"{remaining_time/3600:.1f}小时"
            
            speed_message = f", 处理速度: {frames_per_second:.1f}帧/秒, 预计剩余: {time_str}"
        else:
            speed_message = ""
        
        if progress_callback and i % max(1, total_images // 100) == 0:  # 每1%更新一次进度
            progress_callback(progress, f"处理第 {i+1}/{total_images} 帧 ({progress:.1%}){speed_message}")
        
        image_path = os.path.join(frame_folder, image_name)
        image = cv2.imread(image_path)
        if image is None:
            logger.info(f"{image_name} 读取失败")
            continue
        
        # 提取人脸向量
        vector_tuples = rec_model.extract_vectors(image, image_name)
        processed_frames += 1
        
        if not vector_tuples:
            logger.info(f"{image_name} 未检测到人脸或提取向量失败")
            continue
        
        detected_faces += len(vector_tuples)
        logger.debug(f"{image_name}：检测到 {len(vector_tuples)} 个人脸")
        
        # 裁剪人脸区域并识别
        crop_paths = []
        face_results = []
        
        for vector_info, face_vector in vector_tuples:
            # 获取人脸边界框和关键点
            bbox = vector_info.bbox
            kps = vector_info.kps if hasattr(vector_info, 'kps') else None
            score = vector_info.score if hasattr(vector_info, 'score') else 0.0
            
            # 直接从vector_info获取类别信息
            class_id = getattr(vector_info, 'class_id', "")
            class_name = getattr(vector_info, 'class_name', "")
            rec_score = getattr(vector_info, 'rec_score', score)  # 如果没有rec_score，使用检测分数
            
            # 裁剪人脸区域
            crop_path = ""
            if task_local_path is not None:
                try:
                    # 确保裁剪目录存在
                    crop_dir = os.path.join(task_local_path, "crops")
                    os.makedirs(crop_dir, exist_ok=True)
                    
                    # 提取帧索引和时间戳
                    parts = image_name.split('.')[0].split('_')
                    frame_index = int(parts[1]) if len(parts) > 1 else 0
                    frame_pts = int(parts[2]) if len(parts) > 2 else 0
                    
                    # 生成唯一文件名
                    crop_filename = f"fac_rec_frame_{frame_index:08d}_{frame_pts:08d}_rec_{len(crop_paths):04d}_{uuid.uuid4()}.jpg"
                    crop_path = os.path.join(crop_dir, crop_filename)
                    
                    # 裁剪并保存
                    if all(x != -1 for x in bbox):
                        left, top, right, bottom = [int(x) for x in bbox]
                        h, w = image.shape[:2]
                        left, top = max(0, left), max(0, top)
                        right, bottom = min(w, right), min(h, bottom)
                        
                        if left < right and top < bottom:
                            crop_img = image[top:bottom, left:right]
                            cv2.imwrite(crop_path, crop_img, [cv2.IMWRITE_JPEG_QUALITY, 95])
                except Exception as e:
                    logger.error(f"保存裁剪图像时出错: {e}")
                    crop_path = ""
            
            crop_paths.append(crop_path)
            
            # 构建人脸结果
            face_result = {
                'bbox': bbox,
                'kps': kps,
                'score': score,
                'crop_path': crop_path,
                'class_id': class_id,
                'class_name': class_name,
                'rec_score': rec_score
            }
            face_results.append(face_result)
        
        # 写入结果
        write_csv(image_name, face_results, text_path)
        
        # 可选：生成可视化结果
        if i % 10 == 0 or i == total_images - 1:  # 每10帧或最后一帧生成可视化
            result_image = image.copy()
            for face in face_results:
                bbox = face['bbox']
                kps = face['kps']
                class_name = face['class_name']
                rec_score = face['rec_score']
                
                # 绘制边界框
                color = (0, 255, 0) if face['class_id'] != "" else (0, 0, 255)  # 已知人脸绿色，未知人脸红色
                cv2.rectangle(result_image, (int(bbox[0]), int(bbox[1])), (int(bbox[2]), int(bbox[3])), color, 2)
                
                # 绘制关键点
                if kps is not None:
                    for l in range(len(kps)):
                        pt_color = (0, 0, 255)
                        if l == 0 or l == 3:
                            pt_color = (0, 255, 0)
                        cv2.circle(result_image, (int(kps[l][0]), int(kps[l][1])), 1, pt_color, 2)
                
                # 显示人名和识别分数
                if class_name:
                    label = f"{class_name} ({rec_score:.2f})"
                    cv2.putText(result_image, label, (int(bbox[0]), int(bbox[1])-10), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            output_file = os.path.join(frame_folder, f'face_rec_{image_name}')
            cv2.imwrite(output_file, result_image)
    
    if progress_callback:
        # 计算最终处理速度
        total_time = time.time() - start_time
        frames_per_second = total_images / total_time if total_time > 0 else 0
        faces_per_frame = detected_faces / total_images if total_images > 0 else 0
        
        progress_callback(1.0, f"人脸识别完成，共处理 {total_images} 帧，" +
                         f"检测到 {detected_faces} 个人脸 (平均 {faces_per_frame:.1f}个/帧)，" +
                         f"识别出 {recognized_faces} 个已知人脸，" +
                         f"平均速度: {frames_per_second:.1f}帧/秒，" +
                         f"结果保存至 {text_path}")
    
    return text_path
