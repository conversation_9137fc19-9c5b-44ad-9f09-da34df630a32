
import os
import time
from typing import Any, List, <PERSON>ple

import cv2
import numpy as np
import onnxruntime as rt

from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.live_stream_publisher import LiveStreamPublisher
from onnx_sessions.subtitle_recognition.utils_seglink import cal_width, combine_segments_python, decode_segments_links_python, nms_python, rboxes_to_polygons
from orm.vector_task_orm import VectorExtendInfo
from serve_utils import crop_image
from orm.inference_task_orm import TextDetFrameResult, TextRegFrameResult
from pathlib import Path

from utils.logger import get_logger

logger = get_logger()

class DuGuangOcrDetOnnx(ICannONNX):
    """
    基于SegLink的读光OCR文本检测模型
    """
    def __init__(
        self, 
        model: str,
        class_list: List[str] = None,
        img_size: int = 1024, 
        device_id: int = None,
        provider_options: dict = None,
        ):
        """
        Args:
            model (str): onnx model path
            class_list (List[str]): 类别列表，OCR检测不需要
            img_size (int, optional): 模型限制图片大小, 默认为 1024, 越大越精确，但速度会变慢
            device_id (int): 设备ID，默认为None，会自动分配
            provider_options (dict): 提供商选项
        """
        self.model_path = model
        self.img_size = img_size
        self.class_list = class_list
        
        # 初始化ONNX会话
        super().__init__(model, device_id, provider_options)
    
    def inference(self, img_path):
        """
        对图像进行推理
        Args:
            img_path (str): 图像路径
        Returns:
            tuple: (预测结果, 原始图像)
        """
        img = cv2.imread(img_path)
        return self.inference_image(img)
    
    def inference_image(self, img):
        """
        对图像进行推理
        Args:
            img (np.ndarray): 图像数组
        Returns:
            tuple: (预测结果, 原始图像)
        """
        # 预处理
        result = self.preprocess(img)
        img_tensor = result["img"]
        orig_size = result["orig_size"]
        resize_size = result["resize_size"]
        
        # 前向传播
        latency = []
        start = time.time()
        output = self.forward(img_tensor)
        latency.append(time.time() - start)
        logger.debug("DuGuangOcrDetOnnx Runtime CPU/GPU Inference time = {} ms".format(format(sum(latency) * 1000 / len(latency), '.2f')))
        
        # 后处理
        img_info = {
            "orig_size": [orig_size],
            "resize_size": [resize_size],
            "combined_rboxes": output["combined_rboxes"],
            "combined_counts": output["combined_counts"]
        }
        result = self.postprocess(img_info)
        
        return result, img
    
    def preprocess(self, img):
        """
        图片预处理
        Args:
            img (np.ndarray): 输入图像
        Returns:
            dict: 预处理结果
        """
        # 将图像从 BGR 转换为 RGB
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        h, w, c = img.shape
        img_pad = np.zeros((max(h, w), max(h, w), 3), dtype=np.float32)
        img_pad[:h, :w, :] = img

        resize_size = self.img_size
        img_pad_resize = cv2.resize(img_pad, (resize_size, resize_size))
        img_pad_resize = cv2.cvtColor(img_pad_resize, cv2.COLOR_RGB2BGR)
        img_pad_resize = img_pad_resize - np.array([123.68, 116.78, 103.94], dtype=np.float32)

        resize_size = np.array([resize_size, resize_size])
        orig_size = np.array([max(h, w), max(h, w)])

        result = {
            'img': np.expand_dims(img_pad_resize, axis=0), 
            "orig_size": orig_size,
            "resize_size": resize_size
        }
        return result
    
    def forward(self, input_images):
        """
        前向传播
        Args:
            input_images (np.ndarray): 输入图像张量
        Returns:
            dict: 前向传播结果
        """
        # onnx预测
        input_name = self.onnx_session.get_inputs()[0].name
        output_names = ['dete_0/conv_cls/BiasAdd:0', 'dete_0/conv_lnk/BiasAdd:0', 'dete_0/conv_reg/BiasAdd:0', 
                        'dete_1/conv_cls/BiasAdd:0', 'dete_1/conv_lnk/BiasAdd:0', 'dete_1/conv_reg/BiasAdd:0', 
                        'dete_2/conv_cls/BiasAdd:0', 'dete_2/conv_lnk/BiasAdd:0', 'dete_2/conv_reg/BiasAdd:0', 
                        'dete_3/conv_cls/BiasAdd:0', 'dete_3/conv_lnk/BiasAdd:0', 'dete_3/conv_reg/BiasAdd:0', 
                        'dete_4/conv_cls/BiasAdd:0', 'dete_4/conv_lnk/BiasAdd:0', 'dete_4/conv_reg/BiasAdd:0', 
                        'dete_5/conv_cls/BiasAdd:0', 'dete_5/conv_lnk/BiasAdd:0', 'dete_5/conv_reg/BiasAdd:0']

        batch_all_maps = self.onnx_session.run([output_name for output_name in output_names], {input_name: input_images})

        # 切分
        batch_size = batch_all_maps[0].shape[0]
        new_batch_all_maps = [[] for _ in range(batch_size)]
        for all_maps in batch_all_maps:
            temp = [all_maps[i:i+1, :, :, :] for i in range(batch_all_maps[0].shape[0])]
            for j in range(batch_size):
                new_batch_all_maps[j].append(temp[j])
        # 解码
        combined_rboxe_list, combined_count_list = [], []

        input_images_shape = input_images.shape[1:3]
        for i in range(batch_size):
            combined_rboxe, combined_count = self.decode_model_output(new_batch_all_maps[i], input_images_shape)
            combined_rboxe_list.append(combined_rboxe)
            combined_count_list.append(combined_count)
        
        return {"combined_rboxes": combined_rboxe_list, "combined_counts": combined_count_list}
    
    def postprocess(self, inputs):
        """
        图片后处理
        Args:
            inputs (dict): 包含原始尺寸、调整尺寸和模型输出的字典
        Returns:
            dict: 后处理结果
        """
        res_list = []
        for i in range(len(inputs["orig_size"])):
            rboxes = inputs['combined_rboxes'][i][0]
            count = inputs['combined_counts'][i][0]
            if count == 0 or count < rboxes.shape[0]:
                # raise Exception('No text detected')
                return {"polygons": []}
            rboxes = rboxes[:count, :]

            # convert rboxes to polygons and find its coordinates on the original image
            orig_h, orig_w = inputs['orig_size'][i]
            resize_h, resize_w = inputs['resize_size'][i]
            polygons = rboxes_to_polygons(rboxes)
            scale_y = float(orig_h) / float(resize_h)
            scale_x = float(orig_w) / float(resize_w)

            # confine polygons inside image
            polygons[:, ::2] = np.maximum(0, np.minimum(polygons[:, ::2] * scale_x, orig_w - 1))
            polygons[:, 1::2] = np.maximum(0, np.minimum(polygons[:, 1::2] * scale_y, orig_h - 1))
            polygons = np.round(polygons).astype(np.int32)

            # nms
            dt_n9 = [o + [cal_width(o)] for o in polygons.tolist()]
            dt_nms = nms_python(dt_n9)
            dt_polygons = np.array([o[:8] for o in dt_nms])
            res_list.append(dt_polygons.tolist())
        result = {"polygons": res_list}
        return result
    
    def decode_model_output(self, all_maps, input_images_shape):
        """
        解码模型输出
        Args:
            all_maps (list): 模型输出的特征图列表
            input_images_shape (tuple): 输入图像的形状
        Returns:
            tuple: (combined_rboxe, combined_count)
        """
        all_maps = [all_maps[i:i + 3] for i in range(0, len(all_maps), 3)]

        # 模型推理结果解码
        all_nodes, all_links, all_reg = [], [], []
        for i, maps in enumerate(all_maps):
            cls_maps, lnk_maps, reg_maps = maps[0], maps[1], maps[2]
            offset_variance = [0.1, 0.1, 0.1, 0.1, 0.1, 0.1]
            reg_maps = np.multiply(reg_maps, offset_variance)

            # 将softmax应用到每个类映射
            cls_maps_reshaped = cls_maps.reshape(-1, 2)
            cls_prob = np.exp(cls_maps_reshaped) / np.sum(np.exp(cls_maps_reshaped), axis=1, keepdims=True)

            # 计算链接概率
            lnk_maps_reshaped = lnk_maps.reshape(-1, 4)
            lnk_prob_pos = np.exp(lnk_maps_reshaped[:, :2]) / np.sum(np.exp(lnk_maps_reshaped[:, :2]), axis=1, keepdims=True)
            lnk_prob_mut = np.exp(lnk_maps_reshaped[:, 2:]) / np.sum(np.exp(lnk_maps_reshaped[:, 2:]), axis=1, keepdims=True)
            lnk_prob = np.concatenate([lnk_prob_pos, lnk_prob_mut], axis=1)

            all_nodes.append(cls_prob)
            all_links.append(lnk_prob)
            all_reg.append(reg_maps)

        # decode segments and links
        image_size = np.array(list(input_images_shape))
        segments, group_indices, segment_counts, _ = decode_segments_links_python(
            image_size,
            all_nodes,
            all_links,
            all_reg,
            anchor_sizes=[6., 11.84210526, 23.68421053, 45., 90., 150.]
        )
        # combine segments
        combined_rboxe, combined_count = combine_segments_python(segments, group_indices, segment_counts)
        
        return combined_rboxe, combined_count
    
    def extract_vectors(self, image: np.ndarray, 
                        image_name: str = '', 
                        bbox: List[int] = [-1, -1, -1, -1], 
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0) -> List[Tuple[VectorExtendInfo, List[float]]]:
        """
        提取向量特征
        Args:
            image (np.ndarray): 输入图像
            image_name (str): 图像名称
            bbox (List[int]): 边界框 [x1, y1, x2, y2]
            kps (List[List[int]]): 关键点列表
        Returns:
            List[Tuple[VectorExtendInfo, List[float]]]: 向量特征列表
        """
        # OCR检测模型不支持向量化，返回空列表
        return []
    
    def reference_frame(self, frame: np.ndarray) -> Tuple[Any, np.ndarray]:
        """
        处理视频帧
        Args:
            frame (np.ndarray): 输入帧
        Returns:
            Tuple[Any, np.ndarray]: (检测结果, 处理后的帧)
        """
        result, processed_frame = self.inference_image(frame)
        
        # 在帧上绘制检测结果
        if result and "polygons" in result and len(result["polygons"]) > 0:
            for polygons in result["polygons"]:
                for polygon in polygons:
                    # 将多边形点转换为可绘制格式
                    points = np.array(polygon).reshape(-1, 2)
                    # 绘制多边形
                    cv2.polylines(processed_frame, [points], True, (0, 255, 0), 2)
        
        return result, processed_frame
    
    def detect_object(self, image: np.ndarray, bbox: List[int] = [-1, -1, -1, -1]) -> List[Tuple[VectorExtendInfo, np.ndarray]]:
        """
        检测对象
        Args:
            image (np.ndarray): 输入图像
            bbox (List[int]): 边界框 [x1, y1, x2, y2]
        Returns:
            List[Tuple[VectorExtendInfo, np.ndarray]]: 检测到的对象列表
        """
        det_obj_tuple_list = []
        
        # 如果提供了边界框，裁剪图像
        if bbox != [-1, -1, -1, -1]:
            image = crop_image(image, bbox)
        
        # 执行推理
        result, _ = self.inference_image(image)
        
        # 处理检测结果
        if result and "polygons" in result and len(result["polygons"]) > 0:
            for i, polygons in enumerate(result["polygons"]):
                for j, polygon in enumerate(polygons):
                    # 计算多边形的边界框
                    points = np.array(polygon).reshape(-1, 2)
                    x_min, y_min = np.min(points, axis=0)
                    x_max, y_max = np.max(points, axis=0)
                    
                    # 创建边界框
                    vector_bbox = [int(x_min), int(y_min), int(x_max), int(y_max)]
                    
                    # 创建向量扩展信息
                    vector_extend_info = VectorExtendInfo(
                        bbox=vector_bbox,
                        score=1.0,  # OCR检测没有置信度，设为1.0
                        class_id=0,  # OCR检测没有类别，设为0
                        class_name="text"  # 类别名称设为"text"
                    )
                    
                    # 裁剪检测到的文本区域
                    cropped_image = crop_image(image, vector_bbox)
                    
                    # 添加到结果列表
                    det_obj_tuple_list.append((vector_extend_info, cropped_image))
        
        return det_obj_tuple_list

def run_dg_ocr_det_inference(onnx_path, frame_folder, text_path):
    """
    对文件夹中的图像进行OCR检测
    Args:
        onnx_path (str): 模型路径
        frame_folder (str): 图像文件夹路径
        text_path (str): 结果保存路径
    Returns:
        str: 结果文件路径
    """
    model = DuGuangOcrDetOnnx(onnx_path)
    image_list = os.listdir(frame_folder)
    image_list.sort()
    
    # 确保结果文件存在
    with open(text_path, 'w') as f:
        f.write("")  # 创建空文件
    
    for image_name in image_list:
        if not image_name.lower().endswith(('.png', '.jpg', '.jpeg')):
            continue
            
        image_path = os.path.join(frame_folder, image_name)
        result, _ = model.inference(image_path)
        
        # 保存结果
        write_result_to_file(image_name, result, text_path)
        
        # 可视化结果（可选）
        # if result and "polygons" in result and len(result["polygons"]) > 0:
        #     image = cv2.imread(image_path)
        #     for polygons in result["polygons"]:
        #         for polygon in polygons:
        #             points = np.array(polygon).reshape(-1, 2)
        #             cv2.polylines(image, [points], True, (0, 255, 0), 2)
        #     cv2.imwrite(os.path.join(frame_folder, 'ocr_' + image_name), image)
    
    return text_path

def run_dg_ocr_det_live_reference(onnx_path, source_url, target_rtmp_url):
    """
    处理直播流并实时推送到新的 RTMP 地址
    Args:
        onnx_path (str): 模型路径
        source_url (str): 源视频流URL
        target_rtmp_url (str): 目标RTMP URL
    Returns:
        tuple: (目标URL, 发布器实例)
    """
    model = DuGuangOcrDetOnnx(onnx_path)
    
    publisher = LiveStreamPublisher(model, source_url, target_rtmp_url)
    publisher.start()
    
    return target_rtmp_url, publisher

def write_result_to_file(image_name, result, text_path):
    """
    将OCR检测结果写入文件
    Args:
        image_name (str): 图像名称
        result (dict): OCR检测结果
        text_path (str): 结果保存路径
    """
    # 只有存在检测结果时才写入文件
    if not (result and "polygons" in result and len(result["polygons"]) > 0):
        return
        
    with open(text_path, 'a') as f:
        for i, polygons in enumerate(result["polygons"]):
            for polygon in polygons:
                # 计算多边形的边界框
                points = np.array(polygon).reshape(-1, 2)
                x_min, y_min = np.min(points, axis=0)
                x_max, y_max = np.max(points, axis=0)
                
                # 将多边形点转换为逗号分隔的字符串
                polygon_str = ','.join(map(str, polygon))
                
                # 写入格式: image_name,polygon_points
                f.write(f"{image_name},{polygon_str}\n")

def load_text_det_results(result_path: str) -> List[TextDetFrameResult]:
    """
    从结果文件中加载字幕检测结果
    
    Args:
        result_path (str): 结果文件路径
        
    Returns:
        List[TextDetFrameResult]: 字幕检测结果列表
    """
    results = []
    path = Path(result_path)
    
    if not path.exists():
        logger.info(f"检测结果文件不存在: {result_path}")
        return results
        
    with open(result_path, 'r') as f:
        lines = f.readlines()
        for line in lines:
            parts = line.strip().split(',')
            if len(parts) < 9:  # 至少需要图像名称和8个多边形点坐标
                continue
                
            image_name = parts[0]
            # 提取多边形点
            polygon = [int(float(p)) for p in parts[1:9]]  # 取前8个值作为多边形点
            
            # 从图像名称中提取帧索引和时间戳
            frame_index, frame_pts = 0, 0
            frame_info = image_name.split('.')[0]  # 去掉扩展名
            try:
                parts = frame_info.split('_')
                if len(parts) >= 3:
                    frame_index = int(parts[1])
                    frame_pts = int(parts[2])
            except:
                pass  # 使用默认值
            
            frame_result = TextDetFrameResult(
                frame_index=frame_index,
                frame_pts=frame_pts,
                polygon=polygon
            )
            results.append(frame_result)
    
    return results

if __name__=="__main__":
    import time
    img1 = r'xxx'
    img2 = r'xxx'

    model=r"xxx/model_1024x1024.onnx"
    a = SegLinkOCRDetection(model=model, device="gpu")
    result = a.run([img1,img2])
    logger.info(result)
    t1 = time.time()
    # for _ in range(10):
    #     result = a.run(img1)
    #     logger.info(result)
    logger.info(f"耗时 = {time.time() - t1}")
