import csv
import os
import time
import numpy as np
import onnxruntime as rt
import cv2
from typing import Any, List, Tuple

from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.subtitle_recognition.duguang_ocr_det_onnx import DuGuangOcrDetOnnx
from orm.vector_task_orm import VectorExtendInfo
from serve_utils import crop_image
from orm.inference_task_orm import TextDetFrameResult, TextRegFrameResult, SubtitleRegResult
from pathlib import Path

from utils.logger import get_logger

logger = get_logger()

class DuGuangOcrRegOnnx(ICannONNX):
    def __init__(
        self, 
        model_path: str, 
        ocr_detection: DuGuangOcrDetOnnx = None,
        device_id: int = None,
        provider_options: dict = None
    ) -> None:
        """读光OCR文字识别模型 onnx 版本使用

        Args:
            model_path (str): 模型路径
            ocr_detection (DuGuangOcrDetOnnx, optional): OCR检测模型实例，用于先检测再识别的流程
            device_id (int): 设备ID，默认为None，会自动分配
            provider_options (dict): 提供商选项
        """
        self.model_file = self.find_model_file(model_path)
        self.ocr_detection = ocr_detection
        
        # 加载模型
        super().__init__(self.model_file["model"], device_id, provider_options)
        
        # 获取输入输出名称
        self.input_name = self.onnx_session.get_inputs()[0].name
        self.output_name = self.onnx_session.get_outputs()[0].name
        
        # 获取输入形状
        self.input_shape = self.onnx_session.get_inputs()[0].shape
        logger.info(f"Model input shape: {self.input_shape}")

        self.labelMapping = dict()
        with open(self.model_file["vocab"], 'r', encoding='utf-8') as f:
            lines = f.readlines()
            cnt = 2
            for line in lines:
                line = line.strip('\n')
                self.labelMapping[cnt] = line
                cnt += 1
    
    def run(self, inputs):
        """
        运行模型
        
        Args:
            inputs: 单张图像或图像列表
            
        Returns:
            dict: {
                'preds': ['<img1_str>', '<img2_str>'...], 
                'probs': [<img1_str_prob>, <img2_str_prob>...]
            }
        """
        input_data = self.batch_preprocess(inputs)
        
        batch_size = input_data.shape[0] // 3
        
        # 运行模型
        latency = []
        start = time.time()
        outputs = self.onnx_session.run([self.output_name], {self.input_name: input_data})
        latency.append(time.time() - start)
        logger.debug("DuGuangOcrRegOnnx Runtime CPU/GPU Inference time = {} ms".format(format(sum(latency) * 1000 / len(latency), '.2f')))

        outputs = self.postprocess(batch_size, outputs)

        return outputs
    
    def postprocess(self, batch_size, inputs):
        """
        后处理模型输出
        
        Args:
            batch_size: 批次大小
            inputs: 模型输出
            
        Returns:
            dict: 处理后的结果
        """
        outprobs = np.exp(inputs[0]) / np.sum(np.exp(inputs[0]), axis=-1, keepdims=True)
        preds = np.argmax(outprobs, -1)
        max_scores = np.amax(outprobs, axis=-1)    # 每个字符的预测概率

        batchSize, length = preds.shape
        final_str_list = []
        str_score_list = []
        for i in range(batchSize):
            pred_idx = preds[i].data.tolist()
            probability_list = max_scores[i]  # 概率
            last_p = 0
            str_score = 1.0
            str_pred = []
            for j, p in enumerate(pred_idx):
                if p != last_p and p != 0:
                    str_pred.append(self.labelMapping[p])
                    str_score *= probability_list[j]
                last_p = p
            final_str = ''.join(str_pred)
            final_str_list.append(final_str)
            str_score_list.append(str_score)
        
        assert len(final_str_list) == batch_size or len(final_str_list) == batch_size * 3, "模型预测的字符串需要等于batch_size或batch_size*3"

        #如果是LightweightEdge模型，还需要进下一步处理
        if len(final_str_list) == batch_size * 3:
            new_final_str_list, new_str_score_list = [], []
            for i in range(0, len(final_str_list), 3):
                temp_strs = final_str_list[i:i+3]
                temp_probs = str_score_list[i:i+3]
                s, prob = self.merge_strings_with_overlap(temp_strs, temp_probs)
                new_final_str_list.append(s)
                new_str_score_list.append(prob)
            final_str_list, str_score_list = new_final_str_list, new_str_score_list
        return {'preds': final_str_list, 'probs': str_score_list}
    
    def merge_strings_with_overlap(self, strings, probs):
        """
        合并有重叠部分的字符串
        
        Args:
            strings: 字符串列表
            probs: 概率列表
            
        Returns:
            tuple: (合并后的字符串, 合并后的概率)
        """
        if not strings:
            return "", 0.0
        
        result = strings[0]
        for s in strings[1:]:
            # 找到最长可能的重叠部分
            max_overlap = min(len(result), len(s))
            best_overlap = 0
            
            for overlap in range(max_overlap, -1, -1):
                if result.endswith(s[:overlap]):
                    best_overlap = overlap
                    break
            
            # 合并字符串，只保留一个重叠部分
            result += s[best_overlap:]
        prob = 1.0
        for i in probs:
            prob *= i
        return result, prob

    def batch_preprocess(self, inputs):
        """
        批量预处理
        
        Args:
            inputs: 单张图像或图像列表
            
        Returns:
            np.ndarray: 预处理后的图像数据
        """
        if isinstance(inputs, list):
            im_list = [self.preprocess(im) for im in inputs]
            images = np.vstack(im_list)
            return images
        else:
            image = self.preprocess(inputs)
            return image
    
    def preprocess(self, img):
        """
        预处理单张图像
        
        Args:
            img: 图片路径或图像数组
            
        Returns:
            np.ndarray: 预处理后的图像数据
        """
        if isinstance(img, str):
            img = cv2.imread(img)
        img = self.keepratio_resize(img)
        
        img = np.float32(img)
        chunk_img = []
        for i in range(3):
            left = (300 - 48) * i
            chunk_img.append(img[:, left:left + 300, :])
        
        merge_img = np.concatenate(chunk_img, axis=0)
        data = merge_img.reshape(3, 32, 300, 3) / 255.
        input_data = np.transpose(data, (0, 3, 1, 2))
        return input_data

    def extract_vectors(self, image: np.ndarray, 
                        image_name: str = '',
                        bbox: List[int] = [-1, -1, -1, -1], 
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0) -> List[Tuple[VectorExtendInfo, List[float]]]:
        """
        提取向量特征
        
        Args:
            image: 输入图像
            image_name: 图像名称
            bbox: 边界框 [x1, y1, x2, y2]
            kps: 关键点列表
            
        Returns:
            List[Tuple[VectorExtendInfo, List[float]]]: 向量特征列表
        """
        vector_tuples = []
        
        try:
            # 如果提供了边界框，裁剪图像
            if bbox != [-1, -1, -1, -1]:
                cropped_image = crop_image(image, bbox)
            else:
                cropped_image = image
                
                # 如果有OCR检测模型且没有提供边界框，先进行检测
                if self.ocr_detection is not None:
                    det_results = self.ocr_detection.detect_object(image)
                    for det_info, det_img in det_results:
                        # 对每个检测到的文本区域进行识别
                        result = self.run(det_img)
                        if result and 'preds' in result and len(result['preds']) > 0:
                            for i, (text, prob) in enumerate(zip(result['preds'], result['probs'])):
                                # 创建向量扩展信息
                                vector_extend_info = VectorExtendInfo(
                                    bbox=det_info.bbox,
                                    score=float(prob),
                                    class_id=0,
                                    class_name=text,
                                    frame_name=image_name
                                )
                                
                                # 将文本转换为向量表示
                                text_vector = self.text_to_vector(text)
                                
                                vector_tuples.append((vector_extend_info, text_vector))
                    return vector_tuples
            
            # 直接执行OCR识别
            result = self.run(cropped_image)
            
            if result and 'preds' in result and len(result['preds']) > 0:
                for i, (text, prob) in enumerate(zip(result['preds'], result['probs'])):
                    # 创建向量扩展信息
                    vector_extend_info = VectorExtendInfo(
                        bbox=bbox,
                        score=float(prob),
                        class_id=0,
                        class_name=text,
                        frame_name=image_name
                    )
                    
                    # 将文本转换为向量表示
                    text_vector = self.text_to_vector(text)
                    
                    vector_tuples.append((vector_extend_info, text_vector))
        except Exception as e:
            logger.info(f"Error processing image for OCR recognition: {e}")
        
        return vector_tuples
    
    def text_to_vector(self, text: str) -> List[float]:
        """
        将文本转换为向量表示
        
        Args:
            text: 输入文本
            
        Returns:
            List[float]: 文本向量
        """
        # 简单地使用ASCII码作为示例
        # 实际应用中可能需要更复杂的文本向量化方法
        text_vector = [ord(c) / 255.0 for c in text]
        if not text_vector:
            text_vector = [0.0]  # 空文本的默认向量
        return text_vector
    
    def reference_frame(self, frame: np.ndarray) -> Tuple[Any, np.ndarray]:
        """
        处理视频帧
        
        Args:
            frame: 输入帧
            
        Returns:
            Tuple[Any, np.ndarray]: (识别结果, 处理后的帧)
        """
        processed_frame = frame.copy()
        
        # 如果有OCR检测模型，先进行检测
        if self.ocr_detection is not None:
            det_result, _ = self.ocr_detection.inference_image(frame)
            if det_result and "polygons" in det_result and len(det_result["polygons"]) > 0:
                for polygons in det_result["polygons"]:
                    for polygon in polygons:
                        # 计算多边形的边界框
                        points = np.array(polygon).reshape(-1, 2)
                        x_min, y_min = np.min(points, axis=0)
                        x_max, y_max = np.max(points, axis=0)
                        
                        # 创建边界框并裁剪图像
                        bbox = [int(x_min), int(y_min), int(x_max), int(y_max)]
                        cropped_image = crop_image(frame, bbox)
                        
                        # 执行OCR识别
                        result = self.run(cropped_image)
                        
                        if result and 'preds' in result and len(result['preds']) > 0:
                            for i, (text, prob) in enumerate(zip(result['preds'], result['probs'])):
                                # 在图像上绘制多边形和文本
                                cv2.polylines(processed_frame, [points], True, (0, 255, 0), 2)
                                cv2.putText(processed_frame, f"{text} ({prob:.2f})", 
                                           (bbox[0], bbox[1] - 10), cv2.FONT_HERSHEY_SIMPLEX, 
                                           0.5, (0, 255, 0), 1)
            return det_result, processed_frame
        
        # 如果没有OCR检测模型，直接对整帧进行识别
        result = self.run(frame)
        
        # 在帧上显示识别结果
        if result and 'preds' in result and len(result['preds']) > 0:
            for i, (text, prob) in enumerate(zip(result['preds'], result['probs'])):
                # 在图像底部显示识别的文本
                cv2.putText(processed_frame, f"{text} ({prob:.2f})", 
                           (10, 30 + i * 30), cv2.FONT_HERSHEY_SIMPLEX, 
                           1.0, (0, 255, 0), 2)
        
        return result, processed_frame
    
    def detect_object(self, image: np.ndarray, bbox: List[int] = [-1, -1, -1, -1]) -> List[Tuple[VectorExtendInfo, np.ndarray]]:
        """
        检测对象
        
        Args:
            image: 输入图像
            bbox: 边界框 [x1, y1, x2, y2]
            
        Returns:
            List[Tuple[VectorExtendInfo, np.ndarray]]: 检测到的对象列表
        """
        # OCR识别模型通常不执行检测任务
        # 如果有OCR检测模型，使用它进行检测
        if self.ocr_detection is not None:
            return self.ocr_detection.detect_object(image, bbox)
        return []
    
    def inference(self, image_path: str) -> dict:
        """
        对图像文件进行推理
        
        Args:
            image_path: 图像路径
            
        Returns:
            dict: 识别结果
        """
        img = cv2.imread(image_path)
        return self.inference_image(img)
    
    def inference_image(self, image: np.ndarray) -> dict:
        """
        对图像进行推理
        
        Args:
            image: 图像数组
        
        Returns:
            dict: 识别结果，包含文本、概率和多边形信息
        """
        result = {
            'preds': [], 
            'probs': [], 
            'polygons': []
        }
        # 如果有OCR检测模型，先进行检测再识别
        if self.ocr_detection is not None:
            det_result, _ = self.ocr_detection.inference_image(image)
            all_texts = []
            all_probs = []
            all_polygons = []  # 存储每个文本对应的多边形
            if det_result and "polygons" in det_result and len(det_result["polygons"]) > 0:
                for polygons in det_result["polygons"]:
                    for polygon in polygons:
                        # 计算多边形的边界框
                        points = np.array(polygon).reshape(-1, 2)
                        x_min, y_min = np.min(points, axis=0)
                        x_max, y_max = np.max(points, axis=0)
                        
                        # 创建边界框并裁剪图像
                        bbox = [int(x_min), int(y_min), int(x_max), int(y_max)]
                        cropped_image = crop_image(image, bbox)
                        
                        # 执行OCR识别
                        result = self.run(cropped_image)
                        
                        if result and 'preds' in result and len(result['preds']) > 0:
                            for text, prob in zip(result['preds'], result['probs']):
                                all_texts.append(text)
                                all_probs.append(prob)
                                all_polygons.append(polygon)  # 保存对应的多边形
            
            if all_texts:
                return {'preds': all_texts, 'probs': all_probs, 'polygons': all_polygons}
        else:
            # 如果没有OCR检测模型
            result = self.run(image)
        
        # 为直接识别的结果添加空多边形信息
        if result and 'preds' in result and len(result['preds']) > 0:
            result['polygons'] = [[0, 0, 0, 0, 0, 0, 0, 0]] * len(result['preds'])
        
        return result
    
    def find_model_file(self, model_path):
        """
        发现模型文件, model.onnx, vocab.txt
        
        Args:
            model_path: 放置模型的文件夹
            
        Returns:
            dict: 模型文件路径字典
        """
        model_file = {"model":"", "vocab":""}
        file_names = os.listdir(model_path)
        for file_name in file_names:
            if file_name.endswith("model.onnx"):
                model_file["model"] = os.path.join(model_path, file_name)
            elif file_name.endswith("vocab.txt"):
                model_file["vocab"] = os.path.join(model_path, file_name)

        if not model_file["model"]:
            raise ValueError("not find file for model.onnx")
        if not model_file["vocab"]:
            raise ValueError("not find file for vocab.txt")

        return model_file
    
    def keepratio_resize(self, img):
        """
        保持宽高比进行缩放
        
        Args:
            img: 输入图像
            
        Returns:
            np.ndarray: 缩放后的图像
        """
        # 计算当前图像的宽高比
        cur_ratio = img.shape[1] / float(img.shape[0])
        # 定义目标图像的高度和宽度
        mask_height = 32
        mask_width = 804

        # 如果当前图像的宽高比大于目标宽高比，则将图像高度缩放到 32，宽度按比例缩放
        if cur_ratio > float(mask_width) / mask_height:
            cur_target_height = mask_height
            cur_target_width = mask_width
        # 如果当前图像的宽高比小于或等于目标宽高比，则将图像宽度缩放到 804，高度按比例缩放
        else:
            cur_target_height = mask_height
            cur_target_width = int(mask_height * cur_ratio)

        # 使用 OpenCV 的 resize 函数将图像缩放到目标尺寸
        img = cv2.resize(img, (cur_target_width, cur_target_height))

        # 创建一个与目标图像大小相同的全零掩码
        mask = np.zeros([mask_height, mask_width, 3]).astype(np.uint8)
        # 将缩放后的图像复制到掩码的左上角
        mask[:img.shape[0], :img.shape[1], :] = img

        # 将掩码赋值给 img，即返回缩放后的图像
        img = mask

        return img

def run_dg_ocr_reg_inference(onnx_path, frame_folder, text_path, ocr_detection: DuGuangOcrDetOnnx = None, progress_callback=None):
    """
    对文件夹中的图像进行OCR识别
    
    Args:
        onnx_path: 模型路径
        frame_folder: 图像文件夹路径
        text_path: 结果保存路径
        ocr_detection: OCR检测模型实例
        progress_callback: 可选的进度回调函数，接收(进度百分比, 消息)作为参数
        
    Returns:
        str: 结果文件路径
    """
    # 初始化OCR识别模型
    if progress_callback:
        progress_callback(0.05, "正在加载OCR识别模型...")
    
    model = DuGuangOcrRegOnnx(onnx_path, ocr_detection)
    
    # 获取所有图像文件并排序
    image_list = os.listdir(frame_folder)
    image_list = [img for img in image_list if img.startswith("frame_") and (img.endswith(".jpg") or img.endswith(".png"))]
    image_list.sort(key=lambda x: int(x.split('_')[1]))
    
    total_images = len(image_list)
    if total_images == 0:
        if progress_callback:
            progress_callback(0, "没有找到有效的图像文件")
        logger.info("没有找到有效的图像文件")
        return None
    
    if progress_callback:
        progress_callback(0.1, f"开始处理 {total_images} 张图像...")
    
    # 确保结果文件存在
    os.makedirs(os.path.dirname(text_path), exist_ok=True)
    with open(text_path, 'w') as f:
        f.write("")  # 创建空文件
    
    # 处理每一张图像
    for i, image_name in enumerate(image_list):
        # 计算当前进度 (10%-95%范围)
        progress = 0.1 + (i / total_images) * 0.85
        
        if progress_callback:
            progress_callback(progress, f"处理第 {i+1}/{total_images} 张图像 ({progress:.1%})")
        
        image_path = os.path.join(frame_folder, image_name)
        result = model.inference(image_path)
        
        # 保存结果 - 只有在有识别结果时才写入
        write_result_to_file(image_name, result, text_path)
        
        # 可视化结果（可选，但保留注释以便需要时启用）
        # if result and 'preds' in result and len(result['preds']) > 0:
        #     image = cv2.imread(image_path)
        #     
        #     # 获取多边形信息，如果不存在则使用默认值
        #     polygons = result.get('polygons', [[0, 0, 0, 0, 0, 0, 0, 0]] * len(result['preds']))
        #     
        #     for i, (text, prob, polygon) in enumerate(zip(result['preds'], result['probs'], polygons)):
        #         # 如果有有效的多边形，绘制多边形
        #         if polygon != [0, 0, 0, 0, 0, 0, 0, 0]:
        #             points = np.array(polygon).reshape(-1, 2)
        #             cv2.polylines(image, [points], True, (0, 255, 0), 2)
        #             # 计算多边形的边界框，用于放置文本
        #             x_min, y_min = np.min(points, axis=0)
        #             cv2.putText(image, f"{text} ({prob:.2f})", 
        #                        (int(x_min), int(y_min) - 10), cv2.FONT_HERSHEY_SIMPLEX, 
        #                        0.5, (0, 255, 0), 1)
        #         else:
        #             # 如果没有多边形，在图像底部显示文本
        #             cv2.putText(image, f"{text} ({prob:.2f})", 
        #                        (10, 30 + i * 30), cv2.FONT_HERSHEY_SIMPLEX, 
        #                        1.0, (0, 255, 0), 2)
        #     
        #     cv2.imwrite(os.path.join(frame_folder, 'ocr_' + image_name), image)
    
    if progress_callback:
        progress_callback(0.95, "正在处理识别结果...")
        progress_callback(1.0, f"识别完成，共处理 {total_images} 张图像，结果保存至 {text_path}")
    
    return text_path

def run_dg_ocr_reg_live_reference(onnx_path, source_url, target_rtmp_url):
    """
    处理直播流并实时推送到新的 RTMP 地址
    
    Args:
        onnx_path: 模型路径
        source_url: 源视频流URL
        target_rtmp_url: 目标RTMP URL
        
    Returns:
        tuple: (目标URL, 发布器实例)
    """
    model = DuGuangOcrRegOnnx(onnx_path)
    
    from onnx_sessions.live_stream_publisher import LiveStreamPublisher
    publisher = LiveStreamPublisher(model, source_url, target_rtmp_url)
    publisher.start()
    
    return target_rtmp_url, publisher

def write_result_to_file(image_name, result, text_path):
    """
    将OCR识别结果写入文件
    
    Args:
        image_name: 图像名称
        result: OCR识别结果
        text_path: 结果保存路径
    """
    # 只有存在识别结果时才写入文件
    if not (result and 'preds' in result and len(result['preds']) > 0):
        return
        
    polygons = result.get('polygons', [[0] * 8] * len(result['preds']))

    os.makedirs(os.path.dirname(text_path), exist_ok=True)

    with open(text_path, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        for text, prob, polygon in zip(result['preds'], result['probs'], polygons):
            row = [image_name, 0, text, f"{prob:.4f}"] + polygon
            writer.writerow(row)

def load_text_reg_results(result_path: str) -> List[TextRegFrameResult]:
    """
    从结果文件中加载字幕识别结果
    
    Args:
        result_path: 结果文件路径
        
    Returns:
        List[TextRegFrameResult]: 字幕识别结果列表
    """
    results = []
    path = Path(result_path)
    
    if not path.exists():
        logger.info(f"识别结果文件不存在: {result_path}")
        return results
        
    with open(result_path, 'r', newline='', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            if len(row) < 12:
                continue  # 至少需要图像名、类索引、类名、置信度 + 8个点

            image_name = row[0]
            try:
                class_index = int(row[1])
                class_name = row[2]
                score = float(row[3])
                polygon = [int(float(p)) for p in row[4:12]]
            except Exception as e:
                logger.info(f"跳过解析异常行: {e}")
                continue

            # 提取 frame_index 和 frame_pts
            frame_index, frame_pts = 0, 0
            try:
                parts = Path(image_name).stem.split('_')
                if len(parts) >= 3:
                    frame_index = int(parts[1])
                    frame_pts = int(parts[2])
            except:
                pass

            results.append(TextRegFrameResult(
                frame_index=frame_index,
                frame_pts=frame_pts,
                polygon=polygon,
                class_index=class_index,
                class_name=class_name,
                score=score
            ))
    return results

def generate_subtitle_results(text_reg_results: List[TextRegFrameResult], 
                             roi_bbox: List[int] = [-1, -1, -1, -1]) -> List[SubtitleRegResult]:
    """
    根据文本识别结果生成字幕结果
    
    Args:
        text_reg_results: 文本识别结果列表
        roi_bbox: ROI边界框 [x1, y1, x2, y2]，相对于原始图像尺寸，默认为[-1, -1, -1, -1]表示不进行ROI过滤
        
    Returns:
        List[SubtitleRegResult]: 字幕结果列表
    """
    if not text_reg_results:
        return []
    
    # 按帧索引和时间戳排序
    sorted_results = sorted(text_reg_results, key=lambda x: (x.frame_index, x.frame_pts))
    
    # 是否进行ROI过滤
    roi_filter = not (roi_bbox[0] == -1 and roi_bbox[1] == -1 and roi_bbox[2] == -1 and roi_bbox[3] == -1)
    
    # 筛选符合ROI的文本
    filtered_results: list[TextRegFrameResult] = []
    for result in sorted_results:
        if roi_filter:
            # 计算多边形的中心点 - 多边形坐标已经是相对于原始图像的
            polygon = np.array(result.polygon).reshape(-1, 2)
            center_x, center_y = np.mean(polygon, axis=0)
            
            # 检查中心点是否在ROI内
            if not (roi_bbox[0] <= center_x <= roi_bbox[2] and roi_bbox[1] <= center_y <= roi_bbox[3]):
                continue
        
        filtered_results.append(result)
    
    if not filtered_results:
        return []
    
    # 按文本内容分组
    text_groups = {}
    for result in filtered_results:
        if result.class_name not in text_groups:
            text_groups[result.class_name] = []
        text_groups[result.class_name].append(result)
    
    # 识别字幕的开始和结束
    subtitle_results = []
    
    for text, results in text_groups.items():
        # 按帧索引排序
        results.sort(key=lambda x: (x.frame_index, x.frame_pts))
        
        # 初始化字幕段
        current_segment = []
        
        for i, result in enumerate(results):
            if not current_segment:
                # 开始新的字幕段
                current_segment.append(result)
            else:
                # 检查是否与上一帧连续
                prev_result = current_segment[-1]
                frame_gap = result.frame_index - prev_result.frame_index
                
                # 如果帧间隔小于等于5，认为是连续的
                if frame_gap <= 5:
                    current_segment.append(result)
                else:
                    # 结束当前字幕段，创建SubtitleRegResult
                    first_result = current_segment[0]
                    last_result = current_segment[-1]
                    
                    # 计算平均得分
                    avg_score = sum(r.score for r in current_segment) / len(current_segment)
                    
                    subtitle_result = SubtitleRegResult(
                        frame_index=[first_result.frame_index, last_result.frame_index],
                        frame_pts=[first_result.frame_pts, last_result.frame_pts],
                        polygon=first_result.polygon,
                        text=text,
                        score=avg_score
                    )
                    subtitle_results.append(subtitle_result)
                    
                    # 开始新的字幕段
                    current_segment = [result]
        
        # 处理最后一个字幕段
        if current_segment:
            first_result = current_segment[0]
            last_result = current_segment[-1]
            
            # 计算平均得分
            avg_score = sum(r.score for r in current_segment) / len(current_segment)
            
            subtitle_result = SubtitleRegResult(
                frame_index=[first_result.frame_index, last_result.frame_index],
                frame_pts=[first_result.frame_pts, last_result.frame_pts],
                polygon=first_result.polygon,
                text=text,
                score=avg_score
            )
            subtitle_results.append(subtitle_result)
    
    # 按开始帧排序
    subtitle_results.sort(key=lambda x: (x.frame_index[0], x.frame_pts[0]))
    
    return subtitle_results

def process_subtitle_recognition(result_path: str, roi_bbox: List[int] = [-1, -1, -1, -1]) -> List[SubtitleRegResult]:
    """
    处理字幕识别结果，生成字幕信息
    
    Args:
        result_path: 结果文件路径
        roi_bbox: ROI边界框 [x1, y1, x2, y2]，相对于原始图像尺寸，默认为[-1, -1, -1, -1]表示不进行ROI过滤
        
    Returns:
        List[SubtitleRegResult]: 字幕结果列表
    """
    # 加载文本识别结果
    text_reg_results = load_text_reg_results(result_path)
    
    # 生成字幕结果
    subtitle_results = generate_subtitle_results(text_reg_results, roi_bbox)
    
    return subtitle_results

if __name__=="__main__":
    model_path = r"models/recognition_model_general"

    # 单独使用识别模型
    rec = DuGuangOcrRegOnnx(model_path)
    img1 = r"test_images/rec-1.png"
    img2 = r"test_images/rec-2.png"

    # 批量处理
    result = rec.run([img1, img2])
    logger.info(f"Recognition results: {result}")
    
    # 测试性能
    import time
    t1 = time.time()
    for i in range(5):
        result = rec.run(img1)
    logger.info(f"Average inference time: {(time.time() - t1) / 5:.4f} seconds")
    
    # 使用检测+识别的端到端流程
    from onnx_sessions.subtitle_recognition.duguang_ocr_det_onnx import DuGuangOcrDetOnnx
    det_model_path = r"models/detection_model.onnx"
    det = DuGuangOcrDetOnnx(det_model_path)
    
    # 创建包含检测模型的识别模型
    end2end_rec = DuGuangOcrRegOnnx(model_path, ocr_detection=det)
    
    # 端到端推理
    img_path = r"test_images/full_image.jpg"
    result = end2end_rec.inference(img_path)
    logger.info(f"End-to-end results: {result}")
