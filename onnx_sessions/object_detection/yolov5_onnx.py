from collections import defaultdict, deque
import colorsys
from enum import Enum
import json
import os
from pathlib import Path
import random
from typing import Any, Dict, List, Set, Tuple, Union
import uuid
import cv2
import numpy as np
import onnxruntime
import time
import itertools
import csv

from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.live_stream_publisher import LiveStreamPublisher
from orm.inference_task_orm import ObjDetFrameResult
from orm.task_orm import DirectionEnum
from orm.vector_task_orm import VectorExtendInfo
from routers.download_router import generate_download_url, restore_file_path
from serve_utils import crop_image, crop_image_with_path, to_json_serializable
from utils.logger import get_logger, info

logger = get_logger()

CLASSES=['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck', 'boat', 'traffic light',
        'fire hydrant', 'stop sign', 'parking meter', 'bench', 'bird', 'cat', 'dog', 'horse', 'sheep', 'cow',
        'elephant', 'bear', 'zebra', 'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
        'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove', 'skateboard', 'surfboard',
        'tennis racket', 'bottle', 'wine glass', 'cup', 'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple',
        'sandwich', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
        'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse', 'remote', 'keyboard', 'cell phone',
        'microwave', 'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear',
        'hair drier', 'toothbrush', 'Ambulance_a', 'Bus_a', 'Car_a', 'Motorcycle_a', 'Truck_a'] #coco80类别

class YOLOV5(ICannONNX):
    def __init__(self, onnxpath, class_list, device_id=None, provider_options=None):
        super().__init__(onnxpath, device_id, provider_options)
        self.input_name = [input.name for input in self.onnx_session.get_inputs()]
        self.output_name = [output.name for output in self.onnx_session.get_outputs()]
        self.class_list = class_list
    #-------------------------------------------------------
	#   获取输入输出的名字
	#-------------------------------------------------------
    def get_input_name(self):
        input_name=[]
        for node in self.onnx_session.get_inputs():
            input_name.append(node.name)
        return input_name
    def get_output_name(self):
        output_name=[]
        for node in self.onnx_session.get_outputs():
            output_name.append(node.name)
        return output_name
    #-------------------------------------------------------
	#   输入图像
	#-------------------------------------------------------
    def get_input_feed(self,img_tensor):
        input_feed={}
        for name in self.input_name:
            input_feed[name]=img_tensor
        return input_feed
    #-------------------------------------------------------
	#   1.cv2读取图像并resize
	#	2.图像转BGR2RGB和HWC2CHW
	#	3.图像归一化
	#	4.图像增加维度
	#	5.onnx_session 推理
	#-------------------------------------------------------
    def inference(self, img_path):
        img=cv2.imread(img_path)
        return self.inference_image(img)

    def inference_image(self, img):
        or_img=cv2.resize(img,(640,640))
        img=or_img[:,:,::-1].transpose(2,0,1)  #BGR2RGB和HWC2CHW
        img=img.astype(dtype=np.float32)
        img/=255.0
        img=np.expand_dims(img,axis=0)
        input_feed=self.get_input_feed(img)
        latency = []
        start = time.time()
        pred=self.onnx_session.run(None,input_feed)[0]
        latency.append(time.time() - start)
        logger.debug("YOLOV5 Runtime CPU/GPU Inference time = {} ms".format(format(sum(latency) * 1000 / len(latency), '.2f')))
        return pred,or_img
    
    def extract_vectors(self, image: np.ndarray, 
                        image_name: str = '', 
                        bbox: List[int] = [-1, -1, -1, -1], 
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0) -> List[Tuple[VectorExtendInfo, List[float]]]:
            # 为了可以表示一个图片中包含多个向量化目标（例如多个人脸，目标等），返回值是一个二维列表，
            # 每一个子项为一个元组，元组中第一个元素为向量化信息，第二个元素为向量。
        pass

    def reference_frame(self, frame: np.ndarray) -> Tuple[Any, np.ndarray]:
        output, resize_image = self.inference_image(frame)
        outbox = filter_box(output, 0.25, 0.45)
        if len(outbox) > 0:
            # 将识别bbox转换为对应原始图像尺寸
            origin_h, origin_w = frame.shape[:2]
            input_h, input_w = resize_image.shape[:2]
            outbox[:, 0:4] = scale_coords_to_original((input_h, input_w), outbox[:, 0:4], (origin_h, origin_w))
            draw(frame, outbox, self.class_list)
        return outbox, frame

    def detect_object(self, image: np.ndarray, bbox: List[int] = [-1, -1, -1, -1]) -> List[Tuple[VectorExtendInfo, np.ndarray]]:
        det_obj_tuple_list = []
        if bbox != [-1, -1, -1, -1]:
            image = crop_image(image, bbox)
        obj_det_output, or_img = self.inference_image(image)
        if obj_det_output is not None:
            outbox = filter_box(obj_det_output, 0.25, 0.45)
            logger.info(f"detect_object：共检测到 {len(outbox)} 个目标")
            if len(outbox) > 0:
                for bbox_out in outbox:
                    vector_bbox = bbox_out[:4].astype(np.int32)
                    score = float(bbox_out[4])
                    class_id = int(bbox_out[5])
                    if self.class_list and class_id < len(self.class_list):
                        class_name = self.class_list[class_id]
                    else:
                        class_name = ""
                    vector_extend_info = VectorExtendInfo(
                        bbox=vector_bbox,
                        score=score,
                        class_id=class_id,
                        class_name=class_name
                    )
                    croped_image = crop_image(image, vector_bbox)
                    det_obj_tuple_list.append((vector_extend_info, croped_image))
            else:
                logger.info(f"detect_object 没有有效框")
        else:
            logger.info(f"detect_object 推理失败")

        return det_obj_tuple_list

#dets:  array [x,6] 6个值分别为x1,y1,x2,y2,score,class 
#thresh: 阈值
def nms(dets, thresh):
    x1 = dets[:, 0]
    y1 = dets[:, 1]
    x2 = dets[:, 2]
    y2 = dets[:, 3]
    #-------------------------------------------------------
	#   计算框的面积
    #	置信度从大到小排序
	#-------------------------------------------------------
    areas = (y2 - y1 + 1) * (x2 - x1 + 1)
    scores = dets[:, 4]
    keep = []
    index = scores.argsort()[::-1] 

    while index.size > 0:
        i = index[0]
        keep.append(i)
		#-------------------------------------------------------
        #   计算相交面积
        #	1.相交
        #	2.不相交
        #-------------------------------------------------------
        x11 = np.maximum(x1[i], x1[index[1:]]) 
        y11 = np.maximum(y1[i], y1[index[1:]])
        x22 = np.minimum(x2[i], x2[index[1:]])
        y22 = np.minimum(y2[i], y2[index[1:]])

        w = np.maximum(0, x22 - x11 + 1)                              
        h = np.maximum(0, y22 - y11 + 1) 

        overlaps = w * h
        #-------------------------------------------------------
        #   计算该框与其它框的IOU，去除掉重复的框，即IOU值大的框
        #	IOU小于thresh的框保留下来
        #-------------------------------------------------------
        ious = overlaps / (areas[i] + areas[index[1:]] - overlaps)
        idx = np.where(ious <= thresh)[0]
        index = index[idx + 1]
    return keep


def xywh2xyxy(x):
    # [x, y, w, h] to [x1, y1, x2, y2]
    y = np.copy(x)
    y[:, 0] = x[:, 0] - x[:, 2] / 2
    y[:, 1] = x[:, 1] - x[:, 3] / 2
    y[:, 2] = x[:, 0] + x[:, 2] / 2
    y[:, 3] = x[:, 1] + x[:, 3] / 2
    return y


def filter_box(org_box, conf_thres = 0.25, iou_thres = 0.45): #过滤掉无用的框
    #-------------------------------------------------------
	#   删除为1的维度
    #	删除置信度小于conf_thres的BOX
	#-------------------------------------------------------
    org_box=np.squeeze(org_box)
    conf = org_box[..., 4] > conf_thres
    box = org_box[conf == True]
    #-------------------------------------------------------
    #	通过argmax获取置信度最大的类别
	#-------------------------------------------------------
    cls_cinf = box[..., 5:]
    cls = []
    for i in range(len(cls_cinf)):
        cls.append(int(np.argmax(cls_cinf[i])))
    all_cls = list(set(cls))     
    #-------------------------------------------------------
	#   分别对每个类别进行过滤
	#	1.将第6列元素替换为类别下标
	#	2.xywh2xyxy 坐标转换
	#	3.经过非极大抑制后输出的BOX下标
	#	4.利用下标取出非极大抑制后的BOX
	#-------------------------------------------------------
    output = []
    for i in range(len(all_cls)):
        curr_cls = all_cls[i]
        curr_cls_box = []
        curr_out_box = []
        for j in range(len(cls)):
            if cls[j] == curr_cls:
                box[j][5] = curr_cls
                curr_cls_box.append(box[j][:6])
        curr_cls_box = np.array(curr_cls_box)
        # curr_cls_box_old = np.copy(curr_cls_box)
        curr_cls_box = xywh2xyxy(curr_cls_box)
        curr_out_box = nms(curr_cls_box,iou_thres)
        for k in curr_out_box:
            output.append(curr_cls_box[k])
    output = np.array(output)
    return output

def get_color_map(class_list):
    """生成类别到颜色的映射字典"""
    random.seed(42)  # 固定随机种子保证颜色一致
    color_map = {}
    for cls in class_list:
        color_map[cls] = tuple(random.randint(0, 255) for _ in range(3))
    return color_map

def draw_1(image, box_data, class_list):
    # box_data: [..., 6] -> [x1, y1, x2, y2, score, class_id]
    boxes = box_data[..., :4].astype(np.int32)
    scores = box_data[..., 4]
    classes = box_data[..., 5].astype(np.int32)

    color_map = get_color_map(class_list)

    for box, score, cls_id in zip(boxes, scores, classes):
        left, top, right, bottom = box  # 注意：left, top, right, bottom
        class_name = class_list[cls_id]
        color = color_map[class_name]

        # 绘制矩形框
        cv2.rectangle(image, (left, top), (right, bottom), color, 1)

        label = f'{class_name} {score:.2f}'
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        font_thickness = 1

        # 计算文字尺寸
        (label_width, label_height), baseline = cv2.getTextSize(label, font, font_scale, font_thickness)
        label_top = max(top, label_height + 4)

        # 绘制文字背景矩形
        cv2.rectangle(image, (left, label_top - label_height - 4), (left + label_width, label_top), color, -1)
        # 写文字
        cv2.putText(image, label, (left, label_top - 2), font, font_scale, (255, 255, 255), font_thickness)

        # 日志输出
        # logger.info(f'class: {class_name}, score: {score:.2f}')
        # logger.info(f'box: [{left}, {top}, {right}, {bottom}]')


def draw(image, box_data, class_list, show_label=True):
    """
    Draws bounding boxes on the image.

    Args:
        image (np.ndarray): The input image.
        box_data (np.ndarray): Array of shape (N, 6), where each row is [x1, y1, x2, y2, score, class_id].
        class_list (List[str]): List of class names.
        show_label (bool): Whether to show label and score text.
    """
    num_classes = len(class_list)
    image_h, image_w = image.shape[:2]

    # Generate HSV color map and convert to RGB
    hsv_tuples = [(1.0 * x / num_classes, 1., 1.) for x in range(num_classes)]
    colors = list(map(lambda x: colorsys.hsv_to_rgb(*x), hsv_tuples))
    colors = list(map(lambda x: (int(x[0]*255), int(x[1]*255), int(x[2]*255)), colors))

    random.seed(0)
    random.shuffle(colors)
    random.seed(None)

    for bbox in box_data:
        x1, y1, x2, y2 = map(int, bbox[:4])
        score = float(bbox[4])
        class_id = int(bbox[5])
        class_name = class_list[class_id]

        bbox_color = colors[class_id]
        bbox_thick = max(1, int(0.6 * (image_h + image_w) / 640))

        # Draw bounding box
        cv2.rectangle(image, (x1, y1), (x2, y2), bbox_color, thickness=bbox_thick)

        if show_label:
            label = f"{class_name}: {score:.2f}"
            font_scale = 0.5
            font = cv2.FONT_HERSHEY_SIMPLEX
            (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness=bbox_thick)
            # Make background rectangle for text
            cv2.rectangle(image, (x1, y1 - text_height - 4), (x1 + text_width, y1), bbox_color, -1)
            # Draw the text
            cv2.putText(image, label, (x1, y1 - 2), font, font_scale, (255, 255, 255),
                        thickness=bbox_thick // 2, lineType=cv2.LINE_AA)

    return image

def write_txt(image_name, box_data, class_list, txt_path, crop_paths=None):
    """
    将检测结果写入CSV文件
    
    Args:
        image_name (str): 图像名称
        box_data (np.ndarray): 检测框数据，格式为 [x1, y1, x2, y2, score, class_id]
        class_list (List[str]): 类别名称列表
        txt_path (str): 输出文件路径
        crop_paths (List[str], optional): 裁剪图像路径列表
    """
    boxes = box_data[..., :4].astype(np.int32)
    scores = box_data[..., 4]
    classes = box_data[..., 5].astype(np.int32)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(txt_path), exist_ok=True)
    
    # 检查文件是否存在，如果不存在则创建并写入表头
    file_exists = os.path.isfile(txt_path) and os.path.getsize(txt_path) > 0
    
    with open(txt_path, 'a', newline='') as f:
        writer = csv.writer(f)
        
        # 如果文件不存在，写入表头
        if not file_exists:
            writer.writerow(['image_name', 'left', 'top', 'right', 'bottom', 'class_index', 'class_name', 'score', 'crop_path'])
        
        # 写入检测结果
        for i, (box, score, cl) in enumerate(zip(boxes, scores, classes)):
            left, top, right, bottom = box
            class_name = class_list[cl] if class_list and cl < len(class_list) else ""
            
            # 获取裁剪图像路径（如果有）
            crop_path = ""
            if crop_paths and i < len(crop_paths):
                crop_path = crop_paths[i]
            
            writer.writerow([image_name, left, top, right, bottom, cl, class_name, f"{score:.4f}", crop_path])

def load_yolo_det_result(result_path: str) -> List[ObjDetFrameResult]:
    """
    从CSV文件加载YOLO检测结果
    
    Args:
        result_path (str): 结果文件路径
        
    Returns:
        List[ObjDetFrameResult]: 检测结果对象列表
    """
    path = Path(result_path)
    results = []
    
    if not path.exists():
        logger.info(f"识别结果文件不存在: {result_path}")
        return results
    
    try:
        with open(result_path, 'r', newline='') as f:
            reader = csv.reader(f)
            
            # 跳过表头
            header = next(reader, None)
            if not header:
                logger.info(f"结果文件为空: {result_path}")
                return results
            
            # 读取数据行
            for row in reader:
                if len(row) < 8:
                    logger.warning(f"跳过不完整行: {row}")
                    continue
                
                try:
                    image_name, left, top, right, bottom, class_index, class_name, score = row[:8]
                    crop_path = row[8] if len(row) > 8 else ""
                    
                    # 提取帧索引和时间戳
                    image_name_parts = image_name.split('.')[0].split('_')
                    if len(image_name_parts) >= 3:
                        frame_index = int(image_name_parts[1])
                        frame_pts = int(image_name_parts[2])
                    else:
                        logger.warning(f"无法从图像名称提取帧信息: {image_name}")
                        frame_index = 0
                        frame_pts = 0
                    
                    # 生成下载URL（如果有裁剪图像路径）
                    download_url = ""
                    if crop_path and os.path.exists(crop_path):
                        download_url = generate_download_url(crop_path)
                    
                    # 创建结果对象
                    frame_result = ObjDetFrameResult(
                        frame_index=frame_index,
                        frame_pts=frame_pts,
                        bbox=[int(left), int(top), int(right), int(bottom)],
                        class_index=int(class_index),
                        class_name=class_name,
                        score=float(score),
                        download_url=download_url
                    )
                    results.append(frame_result)
                except Exception as e:
                    logger.warning(f"解析行时出错: {e}, 行: {row}")
                    continue
    except Exception as e:
        logger.error(f"读取结果文件时出错: {e}")
    
    logger.info(f"从 {result_path} 加载了 {len(results)} 条检测结果")
    return results

def get_yolo_result_by_image_name(image_name: str, frame_results: List[ObjDetFrameResult]) -> Tuple[np.ndarray, List[str]]:
    """
    根据 image_name 返回 YOLO 风格的识别结果数组。
    每项格式为：[left, top, right, bottom, score, class_index]

    若无结果，返回空数组，shape 为 (0, 6)
    """
    try:
        image_name = image_name.split('.')[0]  # 去掉扩展名
        parts = image_name.split('_')
        if len(parts) < 3:
            logger.warning(f"非法 image_name 格式: {image_name}")
            return np.empty((0, 6), dtype=np.float32)

        frame_index = int(parts[1])
        frame_pts = int(parts[2])

        matched = [
            r for r in frame_results
            if r.frame_index == frame_index and r.frame_pts == frame_pts
        ]

        if not matched:
            return np.empty((0, 6), dtype=np.float32), []

        result_array = np.array([
            [
                float(r.bbox[0]),
                float(r.bbox[1]),
                float(r.bbox[2]),
                float(r.bbox[3]),
                float(r.score),
                float(r.class_index)
            ]
            for r in matched
        ], dtype=np.float32)

        crop_paths = [restore_file_path(r.download_url) for r in matched]

        return result_array, crop_paths

    except Exception as e:
        logger.warning(f"获取识别结果失败: {e}")
        return np.empty((0, 6), dtype=np.float32)

def get_person_bboxes(frame_results: List[ObjDetFrameResult]) -> List[Tuple[int, int, List[List[int]]]]:
    """
    获取每一帧中 class_name 为 'person' 的 bbox 列表，按帧聚合
    :return: List of tuples: (frame_index, frame_pts, List of bboxes)
    """
    frame_dict = defaultdict(lambda: {"pts": None, "bboxes": []})

    for result in frame_results:
        if result.class_name == "person":
            frame_index = result.frame_index
            frame_dict[frame_index]["pts"] = result.frame_pts
            frame_dict[frame_index]["bboxes"].append(result.bbox)

    # 转换为 list[tuple]
    result_list = [
        (frame_index, data["pts"], data["bboxes"])
        for frame_index, data in sorted(frame_dict.items())
    ]
    return result_list

def crop_detection_area(origin_image: np.ndarray, outbox: np.ndarray, local_path: str, image_name: str) -> List[str]:
    # 裁切并保存检测到的目标区域
    crop_paths = []
    if local_path is not None:
        # 创建裁剪图像目录
        crop_dir = os.path.join(local_path, "crops", "obj_det")
        os.makedirs(crop_dir, exist_ok=True)
        
        # 提取帧索引和时间戳
        image_name_parts = image_name.split('.')[0].split('_')
        if len(image_name_parts) >= 3:
            frame_index = int(image_name_parts[1])
            frame_pts = int(image_name_parts[2])
        else:
            frame_index = 0
            frame_pts = 0
        
        origin_h, origin_w = origin_image.shape[:2]
        # 对每个检测框进行裁剪
        for i, box in enumerate(outbox):
            left, top, right, bottom = box[:4].astype(np.int32)
            
            # 确保边界框在图像范围内
            left = max(0, left)
            top = max(0, top)
            right = min(origin_w, right)
            bottom = min(origin_h, bottom)
            
            # 检查边界框是否有效
            if left >= right or top >= bottom:
                logger.warning(f"无效的边界框: {[left, top, right, bottom]}")
                crop_paths.append("")
                continue
            
            # 生成唯一文件名
            crop_filename = f"obj_det_frame_{frame_index:08d}_{frame_pts:08d}_{i:04d}_{uuid.uuid1()}.jpg"
            crop_path = os.path.join(crop_dir, crop_filename)
            
            try:
                # 裁剪并保存
                crop_img = origin_image[top:bottom, left:right]
                cv2.imwrite(crop_path, crop_img, [cv2.IMWRITE_JPEG_QUALITY, 95])
                crop_paths.append(crop_path)
            except Exception as e:
                logger.error(f"保存裁剪图像时出错: {e}")
                crop_paths.append("")
        return crop_paths

def run_yolov5_obj_det_inference(onnx_path, class_list, frame_folder, text_path, task_local_path=None, progress_callback=None):
    """
    运行YOLO模型推理并将结果保存到文本文件
    
    :param onnx_path: ONNX模型路径
    :param class_list: 类别列表
    :param frame_folder: 帧文件夹路径
    :param text_path: 输出结果文本路径
    :param progress_callback: 可选的进度回调函数，接收(进度百分比, 消息)作为参数
    :return: 结果文本路径
    """
    model = YOLOV5(onnx_path, class_list)
    
    # 获取所有图像文件并排序
    image_list = os.listdir(frame_folder)
    image_list = [img for img in image_list if img.startswith("frame_") and (img.endswith(".jpg") or img.endswith(".png"))]
    image_list.sort(key=lambda x: int(x.split('_')[1]))
    
    total_images = len(image_list)
    if total_images == 0:
        if progress_callback:
            progress_callback(0, "没有找到有效的帧图像")
        logger.info("没有找到有效的帧图像")
        return None
    
    if progress_callback:
        progress_callback(0.01, f"开始处理 {total_images} 个图像帧")
    
    # 确保输出文件目录存在
    os.makedirs(os.path.dirname(text_path), exist_ok=True)
    
    # 清空输出文件（如果存在）
    with open(text_path, 'w') as f:
        pass
    
    # 处理每一帧
    for i, image_name in enumerate(image_list):
        # 计算进度
        progress = (i / total_images)
        
        if progress_callback:
            progress_callback(progress, f"处理第 {i+1}/{total_images} 帧 ({progress:.1%})")
        
        image_path = os.path.join(frame_folder, image_name)
        output, resize_image = model.inference(image_path)
        
        if output is None:
            logger.info(f"{image_name} 推理失败")
            continue
        
        outbox = filter_box(output, 0.25, 0.45)
        logger.info(f"{image_name}：共检测到 {len(outbox)} 个目标")
        
        if len(outbox) == 0:
            logger.info(f"{image_name} 没有有效框")
            continue
        
        # 将识别bbox转换为对应原始图像尺寸
        origin_image = cv2.imread(image_path)
        if origin_image is None:
            logger.error(f"读取图像失败: {image_path}")
            continue
        
        origin_h, origin_w = origin_image.shape[:2]
        input_h, input_w = resize_image.shape[:2]
        outbox[:, 0:4] = scale_coords_to_original((input_h, input_w), outbox[:, 0:4], (origin_h, origin_w))
        
        # 裁切并保存检测到的目标区域
        crop_paths = crop_detection_area(origin_image, outbox, task_local_path, image_name)
        
        # draw image, for debug
        draw(origin_image, outbox, class_list)
        cv2.imwrite(os.path.join(frame_folder, 'yolov5_output_' + image_name), origin_image)
        
        # write txt with crop paths
        write_txt(image_name, outbox, class_list, text_path, crop_paths)
    
    if progress_callback:
        progress_callback(1.0, f"推理完成，共处理 {total_images} 帧，结果保存至 {text_path}")
    
    return text_path

def run_live_reference(onnx_path, class_list, source_url, target_rtmp_url):
    """处理直播流并实时推送到新的 RTMP 地址"""
    model = YOLOV5(onnx_path, class_list)

    publisher = LiveStreamPublisher(model, source_url, target_rtmp_url)
    publisher.start()

    return target_rtmp_url, publisher

def scale_coords_to_original(model_input_shape, coords, original_shape):
    """
    将模型推理输出的 bbox 坐标从模型输入尺寸线性映射回原始图像尺寸（无 letterbox）
    
    :param input_shape: 模型输入尺寸 (h, w)，如 (640, 640)
    :param coords: numpy 数组 (N, 4)，每行为 [x1, y1, x2, y2]
    :param original_shape: 原始图像尺寸 (h0, w0)
    :return: 映射后的坐标（numpy 数组）
    """
    ih, iw = model_input_shape
    h0, w0 = original_shape

    gain_w = w0 / iw
    gain_h = h0 / ih

    coords[:, [0, 2]] *= gain_w  # x1, x2
    coords[:, [1, 3]] *= gain_h  # y1, y2

    coords[:, [0, 2]] = np.clip(coords[:, [0, 2]], 0, w0)
    coords[:, [1, 3]] = np.clip(coords[:, [1, 3]], 0, h0)

    return coords

def get_yolov5_default_extend_json():
    data = {"classes": CLASSES}
    return json.dumps(data)

def valid_yolo_result(yolo_result):
    if not isinstance(yolo_result, np.ndarray):
        return False
    if yolo_result.ndim == 0:
        return False
    if yolo_result.shape[-1] < 6:
        return False
    return True

class CrowdRiskEnum(str, Enum):
    """
    Crowd risk level enumeration
    """
    HIGH_RISK = "high_risk"
    LOW_RISK = "low_risk"
    NO_RISK = "no_risk"

def has_high_risk(risk_result: List[Dict]) -> bool:
    """
    检查风险结果中是否包含 high risk 数据
    """
    for group in risk_result:
        if group.get("risk_level") == CrowdRiskEnum.HIGH_RISK and group.get("results"):
            return True
    return False


# from sklearn.cluster import DBSCAN
def detect_crowd_clusters(
    person_bboxes,
    person_space_coefficient=1.2,
    person_count_threshold=5,
    roi_bbox=(-1, -1, -1, -1)
):
    """
    不使用 DBSCAN，基于图搜索实现人群聚集检测

    返回格式同前：
        {
            "crowd_regions": List[Tuple[x1, y1, x2, y2]],
            "crowd_bboxes": List[Tuple[x1, y1, x2, y2]],
            "non_crowd_bboxes": List[Tuple[x1, y1, x2, y2]],
            "cluster_map": Dict[int, List[Tuple[x1, y1, x2, y2]]]
        }
    """

    if not person_bboxes:
        return {
            "crowd_regions": [],
            "crowd_bboxes": [],
            "non_crowd_bboxes": [],
            "cluster_map": {},
        }

    # Step 1: ROI 筛选
    x_roi1, y_roi1, x_roi2, y_roi2 = roi_bbox
    roi_filter = not (x_roi1 == -1 and y_roi1 == -1 and x_roi2 == -1 and y_roi2 == -1)

    centers = []
    widths = []
    filtered_bboxes = []

    for box in person_bboxes:
        x1, y1, x2, y2 = box
        cx, cy = (x1 + x2) // 2, (y1 + y2) // 2
        if roi_filter:
            if not (x_roi1 <= cx <= x_roi2 and y_roi1 <= cy <= y_roi2):
                continue
        centers.append((cx, cy))
        widths.append(x2 - x1)
        filtered_bboxes.append(box)

    if not filtered_bboxes:
        return {
            "crowd_regions": [],
            "crowd_bboxes": [],
            "non_crowd_bboxes": [],
            "cluster_map": {},
        }

    # Step 2: 计算距离阈值
    avg_width = np.mean(widths)
    dist_thresh = avg_width * person_space_coefficient

    n = len(filtered_bboxes)
    adj = [[] for _ in range(n)]

    # Step 3: 建图（距离小于阈值即为相邻）
    for i in range(n):
        for j in range(i + 1, n):
            d = np.linalg.norm(np.array(centers[i]) - np.array(centers[j]))
            if d < dist_thresh:
                adj[i].append(j)
                adj[j].append(i)

    # Step 4: BFS 查找连通块
    visited = [False] * n
    clusters = []
    for i in range(n):
        if not visited[i]:
            q = deque([i])
            cluster = []
            visited[i] = True
            while q:
                u = q.popleft()
                cluster.append(u)
                for v in adj[u]:
                    if not visited[v]:
                        visited[v] = True
                        q.append(v)
            if len(cluster) >= person_count_threshold:
                clusters.append(cluster)

    # Step 5: 聚集区域提取
    crowd_regions = []
    crowd_bboxes = []
    cluster_map = {}
    all_crowd_idx = set()

    for cid, cluster in enumerate(clusters):
        cluster_boxes = [filtered_bboxes[i] for i in cluster]
        cluster_map[cid] = cluster_boxes
        crowd_bboxes.extend(cluster_boxes)
        all_crowd_idx.update(cluster)

        points = np.array([[box[0], box[1], box[2], box[3]] for box in cluster_boxes])
        x_min, y_min = map(int, np.min(points[:, [0, 1]], axis=0))
        x_max, y_max = map(int, np.max(points[:, [2, 3]], axis=0))
        crowd_regions.append((x_min, y_min, x_max, y_max))

    # Step 6: 非聚集人（ROI 内，未在任何聚集组里）
    non_crowd_bboxes = [
        filtered_bboxes[i] for i in range(n) if i not in all_crowd_idx
    ]

    result = {
        "roi_bbox": roi_bbox,
        "crowd_regions": crowd_regions,
        "crowd_bboxes": crowd_bboxes,
        "non_crowd_bboxes": non_crowd_bboxes,
        "cluster_map": cluster_map,
    }
    return to_json_serializable(result)

def draw_crowd_results(image, crowd_info):
    """
    可视化聚集检测结果
    """
    # 画聚集区域框
    for (x1, y1, x2, y2) in crowd_info["crowd_regions"]:
        cv2.rectangle(image, (x1, y1), (x2, y2), (0, 0, 255), 2)  # 红色框

    # 画聚集人员框
    for (x1, y1, x2, y2) in crowd_info["crowd_bboxes"]:
        cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 255), 1)  # 黄色

    # 画非聚集人员框
    for (x1, y1, x2, y2) in crowd_info["non_crowd_bboxes"]:
        cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 1)  # 绿色

    return image

def has_crowd_region(crowd_info):
    return len(crowd_info["crowd_regions"]) > 0


class SimpleTracker:
    def __init__(self, max_distance: float = 50.0, max_age: int = 10):
        self.next_id = 1
        self.tracks: Dict[int, Dict[str, Union[List[int], int]]] = {}  # {track_id: {"center": [cx, cy], "age": int}}
        self.max_distance = max_distance
        self.max_age = max_age

    def update(self, centers: List[List[int]]) -> Dict[int, List[int]]:
        assigned_tracks = {}
        unmatched_centers = centers.copy()

        updated_track_ids = set()
        track_items = list(self.tracks.items())

        for track_id, track_info in track_items:
            if not unmatched_centers:
                break

            prev_center = np.array(track_info["center"])
            dists = np.linalg.norm(np.array(unmatched_centers) - prev_center, axis=1)
            min_idx = int(np.argmin(dists))
            min_dist = dists[min_idx]

            if min_dist <= self.max_distance:
                matched_center = unmatched_centers.pop(min_idx)
                self.tracks[track_id]["center"] = matched_center
                self.tracks[track_id]["age"] = 0
                assigned_tracks[track_id] = matched_center
                updated_track_ids.add(track_id)

        # 新增未匹配目标
        for center in unmatched_centers:
            track_id = self.next_id
            self.next_id += 1
            self.tracks[track_id] = {"center": center, "age": 0}
            assigned_tracks[track_id] = center

        # 更新未匹配目标的年龄
        for track_id in list(self.tracks.keys()):
            if track_id not in updated_track_ids:
                self.tracks[track_id]["age"] += 1
                if self.tracks[track_id]["age"] > self.max_age:
                    del self.tracks[track_id]

        return assigned_tracks

def detect_roi_crossing_with_transition(
    target_bboxes: Dict[int, List[List[int]]],
    trackers: Dict[int, SimpleTracker],
    last_centers: Dict[int, Dict[int, List[int]]],
    roi_bbox: List[int] = [-1, -1, -1, -1],
    direction: DirectionEnum = DirectionEnum.ENTER,
    ratio: float = 0.0
) -> Tuple[Dict[str, Union[str, List[Dict[str, Union[int, List[List[int]]]]]]], Dict[int, Dict[int, List[int]]]]:
    x_roi1, y_roi1, x_roi2, y_roi2 = roi_bbox
    roi_rect = [x_roi1, y_roi1, x_roi2, y_roi2]
    roi_filter = not (x_roi1 == -1 and y_roi1 == -1 and x_roi2 == -1 and y_roi2 == -1)

    enter_results: List[Dict[str, Union[int, List[List[int]]]]] = []
    leave_results: List[Dict[str, Union[int, List[List[int]]]]] = []
    new_centers: Dict[int, Dict[int, List[int]]] = {}

    def is_inside_center(cx: int, cy: int) -> bool:
        return x_roi1 <= cx <= x_roi2 and y_roi1 <= cy <= y_roi2

    def is_intersect(rect1: List[int], rect2: List[int]) -> bool:
        l1, t1, r1, b1 = rect1
        l2, t2, r2, b2 = rect2
        return not (r1 < l2 or r2 < l1 or b1 < t2 or b2 < t1)

    for class_id, bboxes in target_bboxes.items():
        if class_id not in trackers:
            trackers[class_id] = SimpleTracker()
        centers = [[(x1 + x2) // 2, (y1 + y2) // 2] for x1, y1, x2, y2 in bboxes]
        tracked = trackers[class_id].update(centers)
        prev_class_centers = last_centers.get(class_id, {})

        for bbox, cxcy in zip(bboxes, centers):
            x1, y1, x2, y2 = bbox
            cx, cy = cxcy
            track_id = None

            for tid, center in tracked.items():
                if center == cxcy:
                    track_id = tid
                    break
            if track_id is None:
                continue

            prev_center = prev_class_centers.get(track_id, cxcy)
            px, py = prev_center

            # 当前帧状态
            if not roi_filter:
                inside_now = True
                inside_before = False
            else:
                if ratio <= 0:
                    inside_now = is_inside_center(cx, cy)
                    inside_before = is_inside_center(px, py)
                elif ratio >= 1:
                    inside_now = is_intersect([x1, y1, x2, y2], roi_rect)
                    # 上一帧构造一个bbox为中心点的极小框
                    inside_before = is_intersect([px, py, px+1, py+1], roi_rect)
                else:
                    # 计算缩放后的 bbox
                    w, h = x2 - x1, y2 - y1
                    dx = int(w * (1 - ratio) / 2)
                    dy = int(h * (1 - ratio) / 2)
                    crop_box_now = [x1 + dx, y1 + dy, x2 - dx, y2 - dy]
                    crop_box_prev = [px - w//2 + dx, py - h//2 + dy, px + w//2 - dx, py + h//2 - dy]

                    inside_now = is_intersect(crop_box_now, roi_rect)
                    inside_before = is_intersect(crop_box_prev, roi_rect)

            crossed_in = not inside_before and inside_now
            crossed_out = inside_before and not inside_now

            if crossed_in:
                enter_results.append({"class_id": class_id, "bboxes": [bbox]})
            if crossed_out:
                leave_results.append({"class_id": class_id, "bboxes": [bbox]})

        new_centers[class_id] = tracked

    result_dict = {
        "roi_bbox": roi_bbox,
        "direction": direction.value,
        "enter_results": enter_results,
        "leave_results": leave_results
    }

    return to_json_serializable(result_dict), new_centers


def has_crossing_result(cross_info):
    return len(cross_info["enter_results"]) > 0 or len(cross_info["leave_results"]) > 0

if __name__=="__main__":
    onnx_path='/usr/local/src/onnx-model/yolov5s.onnx'
    # onnx_path='best.onnx'
    # onnx_path='/usr/local/src/yolov5/runs/train/exp6/weights/best.onnx'
    model=YOLOV5(onnx_path)
    # output,or_img=model.inference('tabby_cat.jpg')
    output,or_img=model.inference('many_car.jpg')
    # output,or_img=model.inference('yolo_test_1.png')
    outbox=filter_box(output, 0.25, 0.45)
    draw(or_img,outbox)
    cv2.imwrite('output_res_3.jpg',or_img)
    
    

