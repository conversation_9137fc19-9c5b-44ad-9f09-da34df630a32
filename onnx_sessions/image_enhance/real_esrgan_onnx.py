import os
import subprocess
import threading
import time
from typing import Any, List, Tuple
import cv2
import onnxruntime
import numpy as np

from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.live_stream_publisher import LiveStreamPublisher
from orm.vector_task_orm import VectorExtendInfo
from utils.logger import get_logger

logger = get_logger()

class RealESRGAN_ONNX(ICannONNX):
    def __init__(self, onnx_path, device_id=None, provider_options=None):
        # 使用父类的初始化方法
        super().__init__(onnx_path, device_id, provider_options)
        # 获取输入输出名称
        self.input_name = [input.name for input in self.onnx_session.get_inputs()]
        self.output_name = [output.name for output in self.onnx_session.get_outputs()]

    def preprocess(self, image_path):
        img = cv2.imread(image_path)
        height, width, channels = img.shape
        if width %2 != 0:
            width = width - 1
        if height %2 != 0:
            height = height - 1
        img = cv2.resize(img, (width,height))
        return img

    def inference(self, img):
        img = img.astype(np.float32)
        img = img.transpose((2, 0, 1))
        img = img /255
        img = np.expand_dims(img, axis=0).astype(np.float32)
        latency = []
        start = time.time()
        result = self.onnx_session.run(None, {(self.onnx_session.get_inputs()[0].name):img})[0][0]
        latency.append(time.time() - start)
        logger.debug("RealESRGAN Runtime CPU/GPU Inference time = {} ms".format(sum(latency) * 1000 / len(latency), '.2f'))
        result = (result.squeeze().transpose((1,2,0)) * 255).clip(0, 255).astype(np.uint8)
        # test
        # cv2.imwrite(opt.result, result)
        return result
    
    def inference_fp16(self, img):
        img = img.astype(np.float16)
        img = img.transpose((2, 0, 1))
        img = img /255
        img = np.expand_dims(img, axis=0).astype(np.float16)
        latency = []
        start = time.time()
        result = self.onnx_session.run(None, {(self.onnx_session.get_inputs()[0].name):img})[0][0]
        latency.append(time.time() - start)
        logger.info("RealESRGAN fp16 Runtime CPU/GPU Inference time = {} ms".format(sum(latency) * 1000 / len(latency), '.2f'))
        result = (result.squeeze().transpose((1,2,0)) * 255).clip(0, 255).astype(np.uint8)
        return result
    
    def extract_vectors(self, image: np.ndarray, 
                        image_name: str = '', 
                        bbox: List[int] = [-1, -1, -1, -1], 
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0) -> List[Tuple[VectorExtendInfo, List[float]]]:
            # 为了可以表示一个图片中包含多个向量化目标（例如多个人脸，目标等），返回值是一个二维列表，
            # 每一个子项为一个元组，元组中第一个元素为向量化信息，第二个元素为向量。
        pass

    def reference_frame(self, frame: np.ndarray) -> Tuple[Any, np.ndarray]:
        result = self.inference(frame)
        return None, result

    def detect_object(self, image: np.ndarray, bbox: List[int] = [-1, -1, -1, -1]) -> List[Tuple[VectorExtendInfo, np.ndarray]]:
        pass

def run_img_enh_image_inference(onnx_path, image_path, output_path, progress_callback=None):
    """
    对单张图像进行超分辨率增强
    
    :param onnx_path: ONNX模型路径
    :param image_path: 输入图像路径
    :param output_path: 输出图像路径
    :param progress_callback: 可选的进度回调函数，接收(进度百分比, 消息)作为参数
    :return: (增强后的图像数组, 输出图像路径)
    """
    if progress_callback:
        progress_callback(0.1, "加载图像增强模型...")
    
    folder_path, file_name = os.path.split(image_path)
    model = RealESRGAN_ONNX(onnx_path)
    
    if progress_callback:
        progress_callback(0.3, "预处理图像...")
    
    img = model.preprocess(image_path)
    
    if progress_callback:
        progress_callback(0.5, "执行图像增强推理...")
    
    # 使用fp32进行推理
    start_time = time.time()
    result_img = model.inference(img)
    inference_time = time.time() - start_time
    
    if progress_callback:
        # 计算处理速度（每秒百万像素）
        h, w, _ = img.shape
        mpixels = (h * w) / 1000000  # 百万像素
        mpixels_per_second = mpixels / inference_time if inference_time > 0 else 0
        progress_callback(0.8, f"推理完成，耗时: {inference_time:.2f}秒, 处理速度: {mpixels_per_second:.2f}Mpix/s")
    
    if len(output_path) > 0:
        if progress_callback:
            progress_callback(0.9, "保存增强图像...")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        cv2.imwrite(output_path, result_img)
        
        if progress_callback:
            progress_callback(1.0, f"图像增强完成，结果保存至 {output_path}")
    
    return result_img, output_path


import tempfile

def run_img_enh_video_inference(onnx_path, video_path, output_path, progress_callback=None):
    """
    处理本地视频文件并保存（保留原始音频）
    
    :param onnx_path: ONNX模型路径
    :param video_path: 输入视频路径
    :param output_path: 输出视频路径
    :param progress_callback: 可选的进度回调函数，接收(进度百分比, 消息)作为参数
    :return: 输出视频路径
    """
    if progress_callback:
        progress_callback(0.05, "加载图像增强模型...")
    
    model = RealESRGAN_ONNX(onnx_path)
    
    if progress_callback:
        progress_callback(0.1, "打开视频文件...")
    
    video = cv2.VideoCapture(video_path)
    if not video.isOpened():
        if progress_callback:
            progress_callback(0, "无法打开视频文件")
        raise Exception(f"无法打开视频文件 {video_path}")
    
    w = int(video.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(video.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = video.get(cv2.CAP_PROP_FPS)
    n_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
    
    if progress_callback:
        progress_callback(0.15, f"视频信息: {w}x{h}, {fps}fps, 共{n_frames}帧")
    
    # 1. 先提取音频到临时文件
    if progress_callback:
        progress_callback(0.2, "提取音频...")
    
    with tempfile.NamedTemporaryFile(delete=False, suffix=".aac") as tmp_audio:
        audio_path = tmp_audio.name
    
    subprocess.run([
        'ffmpeg', '-y', '-i', video_path, '-vn',
        '-acodec', 'copy', audio_path
    ], check=True)
    
    # 2. 启动用于写入视频的 ffmpeg 进程
    if progress_callback:
        progress_callback(0.25, "准备视频编码器...")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    record_cmd = [
        'ffmpeg',
        '-y',
        '-f', 'rawvideo',
        '-pix_fmt', 'bgr24',
        '-s', f'{w}x{h}',
        '-r', str(fps),
        '-i', '-',  # 视频帧通过stdin
        '-i', audio_path,  # 加载音频
        '-c:v', 'libx264',
        '-preset', 'medium',
        '-c:a', 'aac',
        '-pix_fmt', 'yuv420p',
        '-movflags', 'faststart',
        output_path
    ]
    record_process = subprocess.Popen(record_cmd, stdin=subprocess.PIPE)
    
    if progress_callback:
        progress_callback(0.3, f"开始处理视频，共 {n_frames} 帧...")
    
    # 处理开始时间和已处理帧数
    start_time = time.time()
    processed_frames = 0
    
    try:
        for frame_idx in range(n_frames):
            # 计算进度 (30%-95%范围)
            progress = 0.3 + (frame_idx / n_frames) * 0.65
            
            # 计算处理速度和预计剩余时间
            if frame_idx > 0:
                elapsed = time.time() - start_time
                frames_per_second = processed_frames / elapsed if elapsed > 0 else 0
                remaining_frames = n_frames - processed_frames
                remaining_time = remaining_frames / frames_per_second if frames_per_second > 0 else 0
                
                # 计算处理倍速
                speed_ratio = frames_per_second / fps if fps > 0 else 0
                
                # 格式化剩余时间
                if remaining_time < 60:
                    time_str = f"{remaining_time:.0f}秒"
                elif remaining_time < 3600:
                    time_str = f"{remaining_time/60:.1f}分钟"
                else:
                    time_str = f"{remaining_time/3600:.1f}小时"
                
                speed_message = f", 处理速度: {frames_per_second:.1f}帧/秒 ({speed_ratio:.1f}倍速), 预计剩余: {time_str}"
            else:
                speed_message = ""
            
            if progress_callback and frame_idx % max(1, n_frames // 100) == 0:  # 每1%更新一次进度
                progress_callback(progress, f"处理第 {frame_idx+1}/{n_frames} 帧 ({progress:.1%}){speed_message}")
            
            ret, frame = video.read()
            if not ret:
                break
            
            # 执行图像增强
            result = model.inference(frame)
            # 可选：使用fp16加速
            # result = model.inference_fp16(frame)
            
            # 调整大小以匹配原始分辨率
            resized_result = cv2.resize(result, (w, h), interpolation=cv2.INTER_AREA)
            
            # 写入处理后的帧
            record_process.stdin.write(resized_result.tobytes())
            
            processed_frames += 1
            
    finally:
        video.release()
        
        if progress_callback:
            progress_callback(0.95, "完成视频处理，正在保存...")
        
        record_process.stdin.close()
        record_process.wait()
        
        if progress_callback:
            # 计算最终处理速度和总时间
            total_time = time.time() - start_time
            frames_per_second = processed_frames / total_time if total_time > 0 else 0
            speed_ratio = frames_per_second / fps if fps > 0 else 0
            
            progress_callback(0.98, f"清理临时文件...")
        
        os.remove(audio_path)  # 清理临时音频文件
        cv2.destroyAllWindows()
        
        if progress_callback:
            progress_callback(1.0, f"视频增强完成，共处理 {processed_frames} 帧，" +
                             f"平均速度: {frames_per_second:.1f}帧/秒 ({speed_ratio:.1f}倍速)，" +
                             f"结果保存至 {output_path}")
    
    return output_path


# def run_img_enh_video_inference(onnx_path, video_path, output_path):
#     """处理本地视频文件并保存（保留原始音频）"""
#     model = RealESRGAN_ONNX(onnx_path)

#     video = cv2.VideoCapture(video_path)
#     if not video.isOpened():
#         raise Exception(f"无法打开视频文件 {video_path}")

#     w = int(video.get(cv2.CAP_PROP_FRAME_WIDTH))
#     h = int(video.get(cv2.CAP_PROP_FRAME_HEIGHT))
#     fps = video.get(cv2.CAP_PROP_FPS)
#     n_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))

#     # 提取音频
#     audio_pipe = subprocess.Popen([
#         'ffmpeg',
#         '-i', video_path,
#         '-vn',
#         '-acodec', 'copy',
#         '-f', 'aac',
#         '-'
#     ], stdout=subprocess.PIPE)

#     # 保存新视频
#     record_cmd = [
#         'ffmpeg',
#         '-y',
#         '-f', 'rawvideo',
#         '-pix_fmt', 'bgr24',
#         '-s', f'{w}x{h}',
#         '-r', str(fps),
#         '-i', '-',  # 视频帧stdin输入
#         '-f', 'aac',
#         '-i', '-',  # 音频流stdin输入
#         '-c:v', 'libx264',
#         '-preset', 'medium',
#         '-c:a', 'aac',
#         '-pix_fmt', 'yuv420p',
#         '-movflags', 'faststart',
#         output_path
#     ]
#     record_process = subprocess.Popen(record_cmd, stdin=subprocess.PIPE)

#     def feed_audio():
#         while True:
#             chunk = audio_pipe.stdout.read(4096)
#             if not chunk:
#                 break
#             try:
#                 record_process.stdin.write(chunk)
#             except BrokenPipeError:
#                 break

#     audio_thread = threading.Thread(target=feed_audio)
#     audio_thread.start()

#     logger.info(f"开始处理文件 {video_path}，保存到 {output_path}")

#     try:
#         for _ in range(n_frames):
#             ret, frame = video.read()
#             if not ret:
#                 break
#             # result = model.enhance_fp16(frame)
#             result = model.enhance(frame)
#             record_process.stdin.write(result.tobytes())
#             # cv2.imshow("Video File Enhancing", result)
#             if cv2.waitKey(1) == 27:
#                 logger.info("检测到ESC，提前结束处理")
#                 break
#     finally:
#         video.release()
#         record_process.stdin.close()
#         record_process.wait()
#         audio_thread.join()
#         cv2.destroyAllWindows()
    
#     return output_path

def run_live_reference(onnx_path, source_url, target_rtmp_url):
    """处理直播流并实时推送到新的 RTMP 地址"""
    model = RealESRGAN_ONNX(onnx_path)

    publisher = LiveStreamPublisher(model, source_url, target_rtmp_url)
    publisher.start()

    return target_rtmp_url, publisher


# def run_live_reference(onnx_path, source_url, target_rtmp_url):
#     """兼容音频的直播流转推，后台线程运行，立即返回 target_rtmp_url"""

#     def push_stream():
#         model = RealESRGAN_ONNX(onnx_path)
#         cap = cv2.VideoCapture(source_url)
#         if not cap.isOpened():
#             logger.info(f"无法打开视频流 {source_url}")
#             return

#         w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
#         h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
#         fps = cap.get(cv2.CAP_PROP_FPS) or 25

#         cmd = [
#             'ffmpeg',
#             '-y',
#             '-f', 'rawvideo',
#             '-pix_fmt', 'bgr24',
#             '-s', f'{w}x{h}',
#             '-r', str(fps),
#             '-i', '-',              # stdin: 图像增强后的视频帧
#             '-i', source_url,       # 第二输入：原始视频流用于音频（如果有）
#             '-map', '0:v:0',        # 使用增强后的视频
#             '-map', '1:a:0?',       # 可选地使用音频
#             '-c:v', 'libx264',
#             '-preset', 'ultrafast',
#             '-tune', 'zerolatency',
#             '-c:a', 'aac',
#             '-pix_fmt', 'yuv420p',
#             '-f', 'flv',
#             target_rtmp_url
#         ]

#         process = subprocess.Popen(cmd, stdin=subprocess.PIPE)

#         logger.info(f"开始转推直播流 {source_url} -> {target_rtmp_url}")

#         try:
#             while True:
#                 ret, frame = cap.read()
#                 if not ret:
#                     logger.info("读取直播帧失败，重试中...")
#                     continue
#                 result = model.inference(frame)
#                 resized_result = cv2.resize(result, (w, h), interpolation=cv2.INTER_AREA)
#                 # test
#                 # resized_result = frame
#                 process.stdin.write(resized_result.tobytes())
#         except Exception as e:
#             logger.info(f"推流过程异常中止：{e}")
#         finally:
#             cap.release()
#             if process.stdin:
#                 process.stdin.close()
#             process.wait()

#     # 启动推流线程
#     thread = threading.Thread(target=push_stream, daemon=True)
#     thread.start()

#     return target_rtmp_url, thread

