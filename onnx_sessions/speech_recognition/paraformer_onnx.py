import os.path
from pathlib import Path
import time
from typing import Any, List, Union, Tuple, Dict, Optional
import json
import csv
import uuid
import logging
from dataclasses import dataclass

import copy
import librosa
import numpy as np

from onnx_sessions.speech_recognition.utils.utils import (
    CharTokenizer,
    Hypothesis,
    ONNXRuntimeError,
    OrtInferSession,
    TokenIDConverter,
    read_yaml,
)
from onnx_sessions.speech_recognition.utils.postprocess_utils import sentence_postprocess, sentence_postprocess_sentencepiece
from onnx_sessions.speech_recognition.utils.frontend import WavFrontend
from onnx_sessions.speech_recognition.utils.timestamp_utils import time_stamp_lfr6_onnx
from onnx_sessions.speech_recognition.utils.utils import pad_list
from onnx_sessions.cann_onnx_interface import ICannONNX
from orm.inference_task_orm import SpeechRecResult
from orm.vector_task_orm import VectorExtendInfo
from serve_utils import crop_image
from utils.logger import get_logger

logger = get_logger()


@dataclass
class Sentence:
    start: float
    end: float
    text: str

    def __post_init__(self):
        self.start = round(self.start, 2)
        self.end = round(self.end, 2)


class Paraformer(ICannONNX):
    """
    Author: Speech Lab of DAMO Academy, Alibaba Group
    Paraformer: Fast and Accurate Parallel Transformer for Non-autoregressive End-to-End Speech Recognition
    https://arxiv.org/abs/2206.08317
    """

    def __init__(
        self,
        model_dir: Optional[Union[str, Path]] = None,
        class_list: Optional[List[str]] = None,
        batch_size: int = 1,
        device_id: Union[int, None] = None,
        plot_timestamp_to: str = "",
        quantize: bool = False,
        intra_op_num_threads: int = 4,
        cache_dir: Optional[str] = None,
        provider_options: Optional[dict] = None,
        **kwargs,
    ):
        if model_dir is None or not Path(model_dir).exists():
            raise Exception(
                "model_dir must be model_name in modelscope or local path downloaded from modelscope, but is {}".format(
                    model_dir
                )
            )

        model_file = os.path.join(model_dir, "model.onnx")
        if quantize:
            model_file = os.path.join(model_dir, "model_quant.onnx")
        if not os.path.exists(model_file):
            raise Exception(".onnx does not exist")

        # 初始化ICannONNX
        super().__init__(model_file, device_id, provider_options)

        config_file = os.path.join(model_dir, "config.yaml")
        cmvn_file = os.path.join(model_dir, "am.mvn")
        config = read_yaml(config_file)
        token_list = os.path.join(model_dir, "tokens.json")
        with open(token_list, "r", encoding="utf-8") as f:
            token_list = json.load(f)

        self.converter = TokenIDConverter(token_list)
        self.tokenizer = CharTokenizer()
        self.frontend = WavFrontend(cmvn_file=cmvn_file, **config["frontend_conf"])
        self.batch_size = batch_size
        self.plot_timestamp_to = plot_timestamp_to
        self.class_list = class_list
        if "predictor_bias" in config["model_conf"].keys():
            self.pred_bias = config["model_conf"]["predictor_bias"]
        else:
            self.pred_bias = 0
        if "lang" in config:
            self.language = config["lang"]
        else:
            self.language = None

    def __call__(self, wav_content: Union[str, np.ndarray, List[str]], **kwargs) -> List:
        waveform_list = self.load_data(wav_content, self.frontend.opts.frame_opts.samp_freq)
        waveform_nums = len(waveform_list)
        asr_res = []
        for beg_idx in range(0, waveform_nums, self.batch_size):

            end_idx = min(waveform_nums, beg_idx + self.batch_size)
            feats, feats_len = self.extract_feat(waveform_list[beg_idx:end_idx])
            try:
                outputs = self.infer(feats, feats_len)
                am_scores, valid_token_lens = outputs[0], outputs[1]
                if len(outputs) == 4:
                    # for BiCifParaformer Inference
                    us_alphas, us_peaks = outputs[2], outputs[3]
                else:
                    us_alphas, us_peaks = None, None
            except ONNXRuntimeError:
                # logging.warning(traceback.format_exc())
                logging.warning("input wav is silence or noise")
                for _ in range(end_idx - beg_idx):
                    asr_res.append({"preds": ""})
                continue

            preds = self.decode(am_scores, valid_token_lens)
            if us_peaks is None:
                # 没有时间戳信息时，生成基本的时间戳
                for i, pred in enumerate(preds):
                    if self.language == "en-bpe":
                        text = sentence_postprocess_sentencepiece(pred)
                    else:
                        text = sentence_postprocess(pred)

                    # 生成基本时间戳：基于音频长度和文本长度估算
                    if isinstance(text, tuple):
                        text_content = text[0] if text else ""
                    else:
                        text_content = text if text else ""

                    # 估算时间戳
                    if text_content and len(waveform_list) > i:
                        audio_duration = len(waveform_list[i]) / self.frontend.opts.frame_opts.samp_freq
                        char_duration = audio_duration / len(text_content) if len(text_content) > 0 else 0.1

                        timestamps = []
                        for j, char in enumerate(text_content):
                            start_time = j * char_duration
                            end_time = (j + 1) * char_duration
                            timestamps.append([char, start_time, end_time])

                        asr_res.append({
                            "preds": text_content,
                            "timestamp": timestamps
                        })
                    else:
                        asr_res.append({"preds": text_content})
            else:
                for pred, us_peaks_ in zip(preds, us_peaks):
                    raw_tokens = pred
                    timestamp, timestamp_raw = time_stamp_lfr6_onnx(
                        us_peaks_, copy.copy(raw_tokens)
                    )
                    text_proc, timestamp_proc, _ = sentence_postprocess(
                        raw_tokens, timestamp_raw
                    )
                    # logging.warning(timestamp)
                    if len(self.plot_timestamp_to):
                        self.plot_wave_timestamp(
                            waveform_list[0], timestamp, self.plot_timestamp_to
                        )
                    asr_res.append(
                        {
                            "preds": text_proc,
                            "timestamp": timestamp_proc,
                            "raw_tokens": raw_tokens,
                        }
                    )
        return asr_res

    def plot_wave_timestamp(self, wav, text_timestamp, dest):
        # TODO: Plot the wav and timestamp results with matplotlib
        import matplotlib

        matplotlib.use("Agg")
        matplotlib.rc(
            "font", family="Alibaba PuHuiTi"
        )  # set it to a font that your system supports
        import matplotlib.pyplot as plt

        fig, ax1 = plt.subplots(figsize=(11, 3.5), dpi=320)
        ax2 = ax1.twinx()
        ax2.set_ylim((0, 2.0))
        # plot waveform
        ax1.set_ylim((-0.3, 0.3))
        time = np.arange(wav.shape[0]) / 16000
        ax1.plot(time, wav / wav.max() * 0.3, color="gray", alpha=0.4)
        # plot lines and text
        for char, start, end in text_timestamp:
            ax1.vlines(start, -0.3, 0.3, ls="--")
            ax1.vlines(end, -0.3, 0.3, ls="--")
            x_adj = 0.045 if char != "<sil>" else 0.12
            ax1.text((start + end) * 0.5 - x_adj, 0, char)
        # plt.legend()
        plotname = "{}/timestamp.png".format(dest)
        plt.savefig(plotname, bbox_inches="tight")

    def load_data(
        self, wav_content: Union[str, np.ndarray, List[str]], fs: Optional[int] = 16000
    ) -> List:
        def convert_to_wav(input_path, output_path):
            from pydub import AudioSegment
            try:
                audio = AudioSegment.from_mp3(input_path)
                audio.export(output_path, format="wav")
                print("音频文件为mp3格式，已转换为wav格式")
                
            except Exception as e:
                print(f"转换失败:{e}")

        def load_wav(path: str) -> np.ndarray:
            if not path.lower().endswith('.wav'):
                import os
                input_path = path
                path = os.path.splitext(path)[0]+'.wav'
                convert_to_wav(input_path,path) #将mp3格式转换成wav格式

            waveform, _ = librosa.load(path, sr=fs)
            return waveform

        if isinstance(wav_content, np.ndarray):
            return [wav_content]

        if isinstance(wav_content, str):
            return [load_wav(wav_content)]

        if isinstance(wav_content, list):
            return [load_wav(path) for path in wav_content]

        raise TypeError(f"The type of {wav_content} is not in [str, np.ndarray, list]")

    def extract_feat(self, waveform_list: List[np.ndarray]) -> Tuple[np.ndarray, np.ndarray]:
        feats, feats_len = [], []
        for waveform in waveform_list:
            speech, _ = self.frontend.fbank(waveform)
            feat, feat_len = self.frontend.lfr_cmvn(speech)
            feats.append(feat)
            feats_len.append(feat_len)

        feats = self.pad_feats(feats, np.max(feats_len))
        feats_len = np.array(feats_len).astype(np.int32)
        return feats, feats_len

    @staticmethod
    def pad_feats(feats: List[np.ndarray], max_feat_len: int) -> np.ndarray:
        def pad_feat(feat: np.ndarray, cur_len: int) -> np.ndarray:
            pad_width = ((0, max_feat_len - cur_len), (0, 0))
            return np.pad(feat, pad_width, "constant", constant_values=0)

        feat_res = [pad_feat(feat, feat.shape[0]) for feat in feats]
        return np.array(feat_res).astype(np.float32)

    def infer(self, feats: np.ndarray, feats_len: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        执行推理
        
        Args:
            feats: 特征
            feats_len: 特征长度
            
        Returns:
            tuple: 模型输出
        """
        input_feed = {
            self.onnx_session.get_inputs()[0].name: feats,
            self.onnx_session.get_inputs()[1].name: feats_len,
        }
        # 运行模型
        latency = []
        start = time.time()
        outputs = self.onnx_session.run(None, input_feed)
        latency.append(time.time() - start)
        logger.debug("ParaformerOnnx Runtime CPU/GPU Inference time = {} ms".format(format(sum(latency) * 1000 / len(latency), '.2f')))
        return outputs

    def decode(self, am_scores: np.ndarray, token_nums: np.ndarray) -> List[List[str]]:
        return [
            self.decode_one(am_score, token_num)
            for am_score, token_num in zip(am_scores, token_nums)
        ]

    def decode_one(self, am_score: np.ndarray, valid_token_num: int) -> List[str]:
        yseq = am_score.argmax(axis=-1)
        score = am_score.max(axis=-1)
        score = np.sum(score, axis=-1)

        # pad with mask tokens to ensure compatibility with sos/eos tokens
        # asr_model.sos:1  asr_model.eos:2
        yseq = np.array([1] + yseq.tolist() + [2])
        hyp = Hypothesis(yseq=yseq, score=score)

        # remove sos/eos and get results
        last_pos = -1
        token_int = hyp.yseq[1:last_pos].tolist()

        # remove blank symbol id, which is assumed to be 0
        token_int = list(filter(lambda x: x not in (0, 2), token_int))

        # Change integer-ids to tokens
        token = self.converter.ids2tokens(token_int)
        token = token[: valid_token_num - self.pred_bias]
        # texts = sentence_postprocess(token)
        return token

    def extract_vectors(self, image: np.ndarray, 
                        image_name: str = '', 
                        bbox: List[int] = [-1, -1, -1, -1], 
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0) -> List[Tuple[VectorExtendInfo, List[float]]]:
        """
        语音识别模型不支持向量提取，返回空列表
        """
        return []

    def reference_frame(self, frame: np.ndarray) -> Tuple[Any, np.ndarray]:
        """
        语音识别模型不支持视频帧处理，返回原始帧
        """
        return None, frame

    def detect_object(self, image: np.ndarray, bbox: List[int] = [-1, -1, -1, -1]) -> List[Tuple[VectorExtendInfo, np.ndarray]]:
        """
        语音识别模型不支持对象检测，返回空列表
        """
        return []

    def inference(self, audio_path: str):
        """
        对音频文件进行推理
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            tuple: (识别结果, 原始音频)
        """
        waveform, _ = librosa.load(audio_path, sr=self.frontend.opts.frame_opts.samp_freq)
        return self.inference_audio(waveform)

    def inference_audio(self, waveform: np.ndarray):
        """
        对音频数据进行推理

        Args:
            waveform: 音频数据

        Returns:
            tuple: (识别结果, 原始音频)
        """
        # 使用滑动窗口进行推理
        chunk_size = 30 * 16000  # 30秒
        step_size = 25 * 16000   # 25秒, 5秒重叠
        overlap_size = chunk_size - step_size  # 5秒重叠

        chunks_info = self.audio_chunk_with_info(waveform, chunk_size, step_size)

        chunk_results = []
        for chunk_info in chunks_info:
            chunk = chunk_info['audio']
            start_time = chunk_info['start_time']
            end_time = chunk_info['end_time']
            chunk_index = chunk_info['index']

            asr_res, _ = self.inference_chunk(chunk)

            # 为每个结果添加时间偏移和块信息
            adjusted_results = self.adjust_timestamps_with_chunk_info(
                asr_res, start_time, end_time, chunk_index, overlap_size / 16000.0
            )
            chunk_results.append({
                'chunk_index': chunk_index,
                'start_time': start_time,
                'end_time': end_time,
                'results': adjusted_results
            })

        # 合并重叠区域的结果
        merged_results = self.merge_overlapping_results(chunk_results)

        # 检测和优化句子边界
        optimized_results = self.detect_sentence_boundaries(merged_results)

        return optimized_results, waveform

    def adjust_timestamps(self, asr_results: List[Dict], offset_time: float) -> List[Dict]:
        """
        为ASR结果调整时间戳偏移

        Args:
            asr_results: ASR识别结果列表
            offset_time: 时间偏移量（秒）

        Returns:
            List[Dict]: 调整后的结果列表
        """
        adjusted_results = []
        for result in asr_results:
            adjusted_result = result.copy()

            # 如果有时间戳信息，调整时间戳
            if "timestamp" in result and result["timestamp"]:
                adjusted_timestamps = []
                for start, end in result["timestamp"]:
                    adjusted_timestamps.append([start + offset_time, end + offset_time])
                adjusted_result["timestamp"] = adjusted_timestamps
            else:
                # 如果没有时间戳，尝试生成基本的时间戳
                raw_tokens = result.get("raw_tokens", [])
                if raw_tokens:
                    # 简单估算：假设每个字符平均0.1秒
                    char_duration = 0.1

                    timestamps = []
                    for i in range(len(raw_tokens)):
                        start_time = offset_time + i * char_duration
                        end_time = offset_time + (i + 1) * char_duration
                        timestamps.append([start_time, end_time])

                    adjusted_result["timestamp"] = timestamps

            adjusted_results.append(adjusted_result)

        return adjusted_results

    def adjust_timestamps_with_chunk_info(self, asr_results: List[Dict], start_time: float,
                                        end_time: float, chunk_index: int, overlap_duration: float) -> List[Dict]:
        """
        为ASR结果调整时间戳偏移，包含块信息

        Args:
            asr_results: ASR识别结果列表
            start_time: 块开始时间（秒）
            end_time: 块结束时间（秒）
            chunk_index: 块索引
            overlap_duration: 重叠时长（秒）

        Returns:
            List[Dict]: 调整后的结果列表
        """
        adjusted_results = []

        for result in asr_results:
            adjusted_result = result.copy()
            adjusted_result['chunk_index'] = chunk_index
            adjusted_result['chunk_start_time'] = start_time
            adjusted_result['chunk_end_time'] = end_time

            # 调整时间戳
            if "timestamp" in result and result["timestamp"]:
                adjusted_timestamps = []
                raw_tokens = result.get("raw_tokens", [])

                for i, (rel_start, rel_end) in enumerate(result["timestamp"]):
                    # 将相对时间转换为绝对时间
                    abs_start = start_time + rel_start
                    abs_end = start_time + rel_end

                    # 标记是否在重叠区域
                    in_overlap = False
                    if chunk_index > 0 and rel_start < overlap_duration:
                        in_overlap = True

                    # 创建扩展的时间戳信息，包含字符、时间和重叠标记
                    char = raw_tokens[i] if i < len(raw_tokens) else ""
                    adjusted_timestamps.append({
                        'char': char,
                        'start': abs_start,
                        'end': abs_end,
                        'in_overlap': in_overlap,
                        'original_format': [abs_start, abs_end]  # 保持原格式兼容
                    })

                adjusted_result["timestamp"] = adjusted_timestamps
                adjusted_result["timestamp_original"] = [[start_time + s, start_time + e] for s, e in result["timestamp"]]
            else:
                # 生成基本时间戳
                raw_tokens = result.get("raw_tokens", [])
                if raw_tokens:
                    chunk_duration = end_time - start_time
                    char_duration = chunk_duration / len(raw_tokens) if len(raw_tokens) > 0 else 0.1

                    timestamps = []
                    timestamp_original = []
                    for i, char in enumerate(raw_tokens):
                        rel_start = i * char_duration
                        rel_end = (i + 1) * char_duration
                        abs_start = start_time + rel_start
                        abs_end = start_time + rel_end

                        in_overlap = chunk_index > 0 and rel_start < overlap_duration
                        timestamps.append({
                            'char': char,
                            'start': abs_start,
                            'end': abs_end,
                            'in_overlap': in_overlap,
                            'original_format': [abs_start, abs_end]
                        })
                        timestamp_original.append([abs_start, abs_end])

                    adjusted_result["timestamp"] = timestamps
                    adjusted_result["timestamp_original"] = timestamp_original

            adjusted_results.append(adjusted_result)

        return adjusted_results

    def inference_chunk(self, waveform_chunk: np.ndarray):
        """
        对单个音频块进行推理
        """
        asr_res = self(waveform_chunk)
        return asr_res, waveform_chunk

    def audio_chunk(self, waveform: np.ndarray, chunk_size: int, step_size: int) -> List[np.ndarray]:
        """
        将音频数据分割成重叠的块（保持向后兼容）
        """
        chunks = []
        total_len = len(waveform)

        for i in range(0, total_len, step_size):
            end = i + chunk_size
            chunk = waveform[i:end]
            chunks.append(chunk)

            if end >= total_len:
                break
        return chunks

    def audio_chunk_with_info(self, waveform: np.ndarray, chunk_size: int, step_size: int) -> List[Dict]:
        """
        将音频数据分割成重叠的块，并返回详细信息

        Args:
            waveform: 音频数据
            chunk_size: 块大小（采样点数）
            step_size: 步长（采样点数）

        Returns:
            List[Dict]: 包含音频块和元信息的列表
        """
        chunks_info = []
        total_len = len(waveform)
        sample_rate = 16000  # 假设采样率为16kHz

        chunk_index = 0
        for i in range(0, total_len, step_size):
            end = min(i + chunk_size, total_len)
            chunk = waveform[i:end]

            # 如果块太短，进行零填充
            if len(chunk) < chunk_size and chunk_index > 0:
                padded_chunk = np.zeros(chunk_size)
                padded_chunk[:len(chunk)] = chunk
                chunk = padded_chunk

            chunk_info = {
                'audio': chunk,
                'start_time': i / sample_rate,
                'end_time': end / sample_rate,
                'start_sample': i,
                'end_sample': end,
                'index': chunk_index,
                'is_last': end >= total_len
            }
            chunks_info.append(chunk_info)
            chunk_index += 1

            if end >= total_len:
                break

        return chunks_info

    def merge_overlapping_results(self, chunk_results: List[Dict]) -> List[Dict]:
        """
        合并重叠区域的识别结果

        Args:
            chunk_results: 每个音频块的识别结果

        Returns:
            List[Dict]: 合并后的结果列表
        """
        if not chunk_results:
            return []

        # 如果只有一个块，直接返回并恢复原格式
        if len(chunk_results) == 1:
            results = []
            for result in chunk_results[0]['results']:
                clean_result = result.copy()
                # 恢复原始时间戳格式
                if 'timestamp_original' in result:
                    clean_result['timestamp'] = result['timestamp_original']
                    del clean_result['timestamp_original']
                # 移除块信息
                for key in ['chunk_index', 'chunk_start_time', 'chunk_end_time']:
                    if key in clean_result:
                        del clean_result[key]
                results.append(clean_result)
            return results

        merged_results = []

        # 处理第一个块（完整保留）
        first_chunk = chunk_results[0]
        for result in first_chunk['results']:
            clean_result = result.copy()
            # 恢复原始时间戳格式
            if 'timestamp_original' in result:
                clean_result['timestamp'] = result['timestamp_original']
                del clean_result['timestamp_original']
            # 移除块信息
            for key in ['chunk_index', 'chunk_start_time', 'chunk_end_time']:
                if key in clean_result:
                    del clean_result[key]
            merged_results.append(clean_result)

        # 处理后续块（去除重叠部分）
        for i in range(1, len(chunk_results)):
            current_chunk = chunk_results[i]

            for result in current_chunk['results']:
                if 'timestamp' in result and result['timestamp']:
                    # 过滤掉重叠区域的内容
                    non_overlap_timestamps = []
                    non_overlap_tokens = []

                    for j, ts_info in enumerate(result['timestamp']):
                        # 检查是否在重叠区域
                        if not ts_info.get('in_overlap', False):
                            non_overlap_timestamps.append(ts_info['original_format'])
                            # 从raw_tokens获取对应字符
                            if 'raw_tokens' in result and j < len(result['raw_tokens']):
                                non_overlap_tokens.append(result['raw_tokens'][j])

                    if non_overlap_timestamps and non_overlap_tokens:
                        new_result = result.copy()
                        new_result['timestamp'] = non_overlap_timestamps
                        new_result['raw_tokens'] = non_overlap_tokens
                        # 重新生成preds文本
                        new_result['preds'] = ''.join(non_overlap_tokens)

                        # 移除扩展信息
                        if 'timestamp_original' in new_result:
                            del new_result['timestamp_original']
                        for key in ['chunk_index', 'chunk_start_time', 'chunk_end_time']:
                            if key in new_result:
                                del new_result[key]

                        merged_results.append(new_result)

        return merged_results

    def detect_sentence_boundaries(self, results: List[Dict]) -> List[Dict]:
        """
        检测和优化句子边界

        Args:
            results: 识别结果列表

        Returns:
            List[Dict]: 优化后的结果列表
        """
        if not results:
            return results

        optimized_results = []

        for result in results:
            if 'timestamp' not in result or not result['timestamp'] or 'raw_tokens' not in result:
                optimized_results.append(result)
                continue

            timestamps = result['timestamp']
            raw_tokens = result['raw_tokens']

            # 确保时间戳和字符数量匹配
            if len(timestamps) != len(raw_tokens):
                optimized_results.append(result)
                continue

            sentences = []
            current_sentence = {
                'tokens': [],
                'timestamps': [],
                'start_time': None,
                'end_time': None
            }

            for i, (char, (start, end)) in enumerate(zip(raw_tokens, timestamps)):
                # 初始化句子开始时间
                if current_sentence['start_time'] is None:
                    current_sentence['start_time'] = start

                current_sentence['tokens'].append(char)
                current_sentence['timestamps'].append([start, end])
                current_sentence['end_time'] = end

                # 检测句子结束条件
                is_sentence_end = False

                # 1. 标点符号
                if char in ['。', '！', '？', '.', '!', '?', '；', ';']:
                    is_sentence_end = True

                # 2. 长时间停顿（如果下一个字符开始时间间隔较大）
                elif i < len(timestamps) - 1:
                    next_start = timestamps[i + 1][0]
                    if next_start - end > 1.0:  # 停顿超过1秒
                        is_sentence_end = True

                # 3. 句子过长（超过100个字符）
                elif len(current_sentence['tokens']) > 100:
                    is_sentence_end = True

                # 如果检测到句子结束，保存当前句子并开始新句子
                if is_sentence_end or i == len(timestamps) - 1:
                    if current_sentence['tokens']:
                        sentences.append({
                            'preds': ''.join(current_sentence['tokens']),
                            'raw_tokens': current_sentence['tokens'].copy(),
                            'timestamp': current_sentence['timestamps'].copy(),
                            'start_time': current_sentence['start_time'],
                            'end_time': current_sentence['end_time']
                        })

                    # 重置当前句子
                    current_sentence = {
                        'tokens': [],
                        'timestamps': [],
                        'start_time': None,
                        'end_time': None
                    }

            # 如果只有一个句子，保持原格式
            if len(sentences) == 1:
                optimized_results.append(result)
            else:
                # 多个句子，分别添加
                for sentence in sentences:
                    new_result = result.copy()
                    new_result.update(sentence)
                    optimized_results.append(new_result)

        return optimized_results

    def merge_results(self, results_cache: List[Tuple[List[Dict], float]]) -> List[Dict]:
        """
        合并重叠的识别结果
        """
        if not results_cache:
            return []

        # 首先将所有识别结果转换成带绝对时间戳的句子列表
        all_sentences = []
        for asr_results, offset_time in results_cache:
            for result in asr_results:
                if "timestamp" in result:
                    for char, start, end in result["timestamp"]:
                        if char != "<sil>":
                            all_sentences.append(Sentence(start=start + offset_time, end=end + offset_time, text=char))

        if not all_sentences:
            # 如果没有时间戳信息，则直接拼接
            merged_text = "".join([res[0][0].get('preds', '') for res in results_cache if res[0]])
            return [{"preds": merged_text}]

        # 合并相邻的字
        merged_sentences = []
        if all_sentences:
            current_sentence = all_sentences[0]
            for i in range(1, len(all_sentences)):
                next_sentence = all_sentences[i]
                if next_sentence.start == current_sentence.end:
                    current_sentence.text += next_sentence.text
                    current_sentence.end = next_sentence.end
                else:
                    merged_sentences.append(current_sentence)
                    current_sentence = next_sentence
            merged_sentences.append(current_sentence)

        # 转换回ASR结果格式
        final_results = []
        if merged_sentences:
            text_proc = "".join([s.text for s in merged_sentences])
            timestamp_proc = [[s.text, s.start, s.end] for s in merged_sentences]
            final_results.append({
                "preds": text_proc,
                "timestamp": timestamp_proc
            })
        return final_results

    def process_results_to_sentences(self, asr_results: List[Dict]) -> List[Dict]:
        """
        将ASR结果处理为句子列表，根据时间戳的连续性分割

        Args:
            asr_results: ASR识别结果

        Returns:
            List[Dict]: 处理后的句子列表，每个句子包含文本和时间戳
        """
        sentences = []

        for result in asr_results:
            if "timestamp" not in result or not result.get("timestamp"):
                # 如果没有时间戳，直接添加文本
                text = result.get("preds", "")
                if text:
                    sentences.append({
                        "text": text,
                        "start_time": 0.0,
                        "end_time": 0.0,
                        "words": []
                    })
                continue

            # 处理带时间戳的结果
            timestamps = result.get("timestamp", [])
            raw_tokens = result.get("raw_tokens", [])

            if not timestamps or not raw_tokens:
                continue

            # 确保时间戳和字符数量匹配
            if len(timestamps) != len(raw_tokens):
                # 如果不匹配，尝试从preds获取字符
                text = result.get("preds", "")
                if text and timestamps:
                    sentences.append({
                        "text": text,
                        "start_time": timestamps[0][0] if timestamps else 0.0,
                        "end_time": timestamps[-1][1] if timestamps else 0.0,
                        "words": []
                    })
                continue

            # 将字符级时间戳合并为句子
            current_sentence = {
                "text": "",
                "start_time": timestamps[0][0] if timestamps else 0.0,
                "end_time": 0.0,
                "words": []
            }

            for i, ((start, end), char) in enumerate(zip(timestamps, raw_tokens)):
                # 跳过静音标记
                if char == "<sil>":
                    # 如果当前句子有内容且静音时间较长，结束当前句子
                    if current_sentence["text"] and i < len(timestamps) - 1:
                        next_start = timestamps[i+1][0]
                        if next_start - end > 0.5:  # 如果静音超过0.5秒，认为是句子间隔
                            current_sentence["end_time"] = end
                            sentences.append(current_sentence.copy())
                            # 创建新句子
                            current_sentence = {
                                "text": "",
                                "start_time": next_start,
                                "end_time": 0.0,
                                "words": []
                            }
                    continue

                # 如果是第一个字符，设置开始时间
                if not current_sentence["text"]:
                    current_sentence["start_time"] = start

                # 添加字符到当前句子
                current_sentence["text"] += char
                current_sentence["end_time"] = end
                current_sentence["words"].append({
                    "text": char,
                    "start_time": start,
                    "end_time": end
                })

                # 检查是否应该分割句子（基于标点符号或长度）
                if char in ['。', '！', '？', '.', '!', '?'] or len(current_sentence["text"]) > 50:
                    if current_sentence["text"]:
                        sentences.append(current_sentence.copy())
                        # 重置当前句子
                        current_sentence = {
                            "text": "",
                            "start_time": 0.0,
                            "end_time": 0.0,
                            "words": []
                        }

            # 添加最后一个句子
            if current_sentence["text"]:
                sentences.append(current_sentence)

        return sentences

    def save_results_to_csv(self, sentences: List[Dict], output_path: str):
        """
        将处理后的句子结果保存到CSV文件
        
        Args:
            sentences: 处理后的句子列表
            output_path: 输出CSV文件路径
        """
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            # 写入表头
            writer.writerow(['sentence_id', 'text', 'start_time', 'end_time', 'duration'])
            
            # 写入句子数据
            for i, sentence in enumerate(sentences):
                sentence_id = i + 1
                text = sentence["text"]
                start_time = sentence["start_time"]
                end_time = sentence["end_time"]
                duration = end_time - start_time
                
                writer.writerow([sentence_id, text, f"{start_time:.3f}", f"{end_time:.3f}", f"{duration:.3f}"])
        
        logger.info(f"保存了 {len(sentences)} 个句子到 {output_path}")
        return output_path

def run_paraformer_asr_inference(model_dir, audio_file, output_csv, device_id: Union[int, None] = None, progress_callback=None):
    """
    运行Paraformer语音识别并将结果保存到CSV文件
    
    Args:
        model_dir: 模型目录路径
        audio_file: 音频文件路径
        output_csv: 输出CSV文件路径
        device_id: 设备ID
        progress_callback: 进度回调函数
        
    Returns:
        str: 结果CSV文件路径
    """
    if progress_callback:
        progress_callback(0.1, "正在加载语音识别模型...")
    
    # 初始化模型
    # model = Paraformer(model_dir=model_dir, device_id=device_id, quantize=True)
    model = Paraformer(model_dir=model_dir, device_id=-1, quantize=True)
    
    if progress_callback:
        progress_callback(0.3, "正在处理音频文件...")
    
    # 执行推理
    try:
        asr_results, _ = model.inference(audio_file)
        
        if progress_callback:
            progress_callback(0.6, "正在处理识别结果...")
        
        # 处理结果为句子
        sentences = model.process_results_to_sentences(asr_results)
        
        if progress_callback:
            progress_callback(0.8, "正在保存结果到CSV文件...")
        
        # 保存结果到CSV
        output_path = model.save_results_to_csv(sentences, output_csv)
        
        if progress_callback:
            progress_callback(1.0, f"语音识别完成，结果保存至 {output_path}")
        
        return output_path
    
    except Exception as e:
        logger.error(f"语音识别过程中出错: {e}")
        if progress_callback:
            progress_callback(1.0, f"语音识别失败: {e}")
        return None

def load_speech_rec_result(result_path: str) -> List[SpeechRecResult]:
    """
    从CSV文件加载语音识别结果
    """
    results = []
    with open(result_path, 'r', newline='') as f:
        reader = csv.reader(f)
        for row in reader:
            if len(row) < 4:
                continue
            result = SpeechRecResult(
                text=row[1],
                start_time=float(row[2]),
                end_time=float(row[3])
            )
            results.append(result)
    return results

if __name__ == "__main__":
    # 示例用法
    model_dir = "/path/to/paraformer/model"
    audio_file = "example.wav"
    output_csv = "asr_results.csv"
    
    run_paraformer_asr_inference(model_dir, audio_file, output_csv)
