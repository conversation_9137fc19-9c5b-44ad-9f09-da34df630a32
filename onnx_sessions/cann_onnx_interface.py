from abc import ABCMeta, abstractmethod
from typing import Any, Dict, List, Tuple, Union
import numpy as np
import onnxruntime
import os
import json
from filelock import FileLock
import pathlib
import threading

from cv2.typing import MatLike
from orm.vector_task_orm import VectorExtendInfo
from config import settings
from utils.logger import get_logger

logger = get_logger()

# 获取项目根目录
PROJECT_ROOT = pathlib.Path(__file__).parent.parent.absolute()

# 设备ID锁文件路径，放在项目根目录下
DEVICE_ID_LOCK_FILE = os.path.join(PROJECT_ROOT, "device_id_lock.json")

# 进程内缓存设备ID
_PROCESS_DEVICE_ID = None
_PROCESS_DEVICE_ID_LOCK = threading.Lock()

def get_next_device_id():
    """获取下一个可用的设备ID，使用文件锁确保多进程安全
    
    同一进程内的多次调用将返回相同的设备ID
    """
    global _PROCESS_DEVICE_ID
    
    # 如果进程已经有分配的设备ID，直接返回
    with _PROCESS_DEVICE_ID_LOCK:
        if _PROCESS_DEVICE_ID is not None:
            logger.info(f"Reusing process device_id: {_PROCESS_DEVICE_ID}")
            return _PROCESS_DEVICE_ID
    
    device_ids = settings.onnx_provider.device_ids
    if not device_ids:
        with _PROCESS_DEVICE_ID_LOCK:
            _PROCESS_DEVICE_ID = 0
        return 0
    
    # 使用文件锁确保多进程安全
    lock_file = f"{DEVICE_ID_LOCK_FILE}.lock"
    with FileLock(lock_file):
        # 读取或初始化当前索引
        if os.path.exists(DEVICE_ID_LOCK_FILE):
            try:
                with open(DEVICE_ID_LOCK_FILE, 'r') as f:
                    data = json.load(f)
                    current_index = data.get('index', 0)
            except (json.JSONDecodeError, FileNotFoundError):
                current_index = 0
        else:
            current_index = 0
        
        # 获取下一个设备ID
        device_id = device_ids[current_index % len(device_ids)]
        
        # 更新索引
        next_index = (current_index + 1) % len(device_ids)
        with open(DEVICE_ID_LOCK_FILE, 'w') as f:
            json.dump({'index': next_index}, f)
        
        # 缓存到进程变量
        with _PROCESS_DEVICE_ID_LOCK:
            _PROCESS_DEVICE_ID = device_id
        
        logger.info(f"Allocated device_id {device_id} (index {current_index}) for process {os.getpid()}")
        return device_id

class ICannONNX(metaclass=ABCMeta):
    def __init__(self, onnx_path, device_id=None, provider_options=None):
        # 如果未指定设备ID，则从配置的设备ID列表中获取下一个
        if device_id is None:
            device_id = get_next_device_id()
            logger.info(f"Using device_id: {device_id}")
        
        # 从配置文件获取默认选项
        default_options = {
            "device_id": device_id,
            "arena_extend_strategy": settings.onnx_provider.arena_extend_strategy,
            "npu_mem_limit": settings.onnx_provider.npu_mem_limit,
            "op_select_impl_mode": settings.onnx_provider.op_select_impl_mode,
            "enable_cann_graph": settings.onnx_provider.enable_cann_graph,
        }
        
        # 合并用户提供的选项
        if provider_options:
            default_options.update(provider_options)
        
        options = onnxruntime.SessionOptions()
        
        # 根据配置决定是否使用CANNExecutionProvider
        if settings.onnx_provider.enable:
            providers = [
                (
                    "CANNExecutionProvider",
                    default_options
                ),
                "CPUExecutionProvider",
            ]
            logger.info("CANNExecutionProvider is enabled")
        else:
            providers = ["CPUExecutionProvider"]
            logger.info("CANNExecutionProvider is disabled, using CPUExecutionProvider only")

        logger.info(f"Using onnxruntime providers: {providers}")
        self.onnx_session = onnxruntime.InferenceSession(
            onnx_path, sess_options=options, providers=providers
        )

        meta = self.onnx_session.get_modelmeta()
        logger.info(f"custom_metadata_map={meta.custom_metadata_map}")
        logger.info(f"description={meta.description}")
        logger.info(f"domain={meta.domain}")
        logger.info(f"graph_name={meta.graph_name}")
        logger.info(f"producer_name={meta.producer_name}")
        logger.info(f"version={meta.version}")

    @abstractmethod
    def extract_vectors(self, image: np.ndarray, 
                        image_name: str = '',
                        bbox: List[int] = [-1, -1, -1, -1], 
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0) -> List[Tuple[VectorExtendInfo, List[float]]]:
            # 为了可以表示一个图片中包含多个向量化目标（例如多个人脸，目标等），返回值是一个二维列表，
            # 每一个子项为一个元组，元组中第一个元素为向量化信息，第二个元素为向量。
        pass


    @abstractmethod
    def reference_frame(self, frame: np.ndarray) -> Tuple[Any, np.ndarray]:
        # 输入为原始帧，输出为处理后的帧
        # 供直播转推使用
        pass

    @abstractmethod
    def detect_object(self, image: np.ndarray, bbox: List[int] = [-1, -1, -1, -1]) -> List[Tuple[VectorExtendInfo, np.ndarray]]:
        # 输入为图片，输出为检测到的物体信息
        # 如果bbox可用，则识别bbox范围内的物体，返回包含向量化扩展信息与对应裁切出的识别区域的图像数据
        # 供向量化或向量搜索使用
        pass
