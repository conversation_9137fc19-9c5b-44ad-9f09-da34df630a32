import threading
import subprocess
import time
from typing import Any, Callable, Optional
import cv2
import numpy as np

from onnx_sessions.cann_onnx_interface import ICannONNX

LIVE_FRAME_VIRTUAl_NAME = "frame_-1_-1.jpg"

class LiveStreamPublisher:
    def __init__(self, cann_onnx: ICannONNX, source_url, target_rtmp_url = ''):
        self.cann_onnx: ICannONNX = cann_onnx
        self.source_url = source_url
        self.target_rtmp_url = target_rtmp_url
        self._stop_event = threading.Event()
        self._thread = None
        self._process = None
        self._result_frame_callback: Optional[Callable[[int, int, Any, np.ndarray], np.ndarray]] = None

    def start(self, target_rtmp_url: Optional[str] = None):
        self.start_capture()
        return self.start_publish(target_rtmp_url)
    
    def start_capture(self):
        if self._thread and self._thread.is_alive():
            print("采集已在进行中")
            return
        
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()

    def start_publish(self, target_rtmp_url: Optional[str] = None):
        if target_rtmp_url:
            self.target_rtmp_url = target_rtmp_url

        if len(self.target_rtmp_url) == 0:
            print("推流地址无效")

        return self.target_rtmp_url

    def set_result_frame_callback(self, callback: Callable[[int, int, Any, np.ndarray], np.ndarray]):
        self._result_frame_callback = callback

    def _run(self):
        cap = cv2.VideoCapture(self.source_url)
        if not cap.isOpened():
            print(f"无法打开视频流 {self.source_url}")
            return

        w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS) or 25

        frame_index = 0  # 帧序号初始化
        max_counter = 99999999

        try:
            while not self._stop_event.is_set():
                if self._process is None and self.target_rtmp_url:
                    # start publish
                    cmd = [
                        'ffmpeg',
                        '-y',
                        '-f', 'rawvideo',
                        '-pix_fmt', 'bgr24',
                        '-s', f'{w}x{h}',
                        '-r', str(fps),
                        '-i', '-',  # stdin: 图像增强后的视频帧
                        '-i', self.source_url,  # 再次打开用于音频
                        '-map', '0:v:0',
                        '-map', '1:a:0?',
                        '-c:v', 'libx264',
                        '-preset', 'ultrafast',
                        '-tune', 'zerolatency',
                        '-c:a', 'aac',
                        '-pix_fmt', 'yuv420p',
                        '-f', 'flv',
                        self.target_rtmp_url
                    ]
                    self._process = subprocess.Popen(cmd, stdin=subprocess.PIPE)
                    print(f"开始转推直播流 {self.source_url} -> {self.target_rtmp_url}")
                # start read
                ret, frame = cap.read()
                if not ret:
                    print("读取直播帧失败，重试中...")
                    continue
                # if self._process.poll() is not None:
                #     print("ffmpeg 子进程已退出，终止推流线程")
                #     break
                output, result_frame = self.cann_onnx.reference_frame(frame)
                # 获取毫秒级时间戳
                frame_pts = int(time.time() * 1000)
                # 限制 frame_index 和 frame_pts 最大为 8 位整数
                frame_index %= (max_counter + 1)
                frame_pts %= (max_counter + 1)

                if self._result_frame_callback:
                    result_frame = self._result_frame_callback(frame_index, frame_pts, output, result_frame)

                if self._process:
                    resized_result_frame = cv2.resize(result_frame, (w, h), interpolation=cv2.INTER_AREA)
                    self._process.stdin.write(resized_result_frame.tobytes())
                
                frame_index += 1
        except Exception as e:
            print(f"推流异常中止：{e}")
        finally:
            cap.release()
            if self._process:
                if self._process.stdin:
                    self._process.stdin.close()
                self._process.wait()

    def stop(self):
        print("请求停止推流...")
        self._stop_event.set()
        # 如果子进程还在，尝试终止
        if self._process and self._process.poll() is None:
            print("终止 ffmpeg 子进程...")
            try:
                self._process.terminate()  # 发送 SIGTERM
                self._process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("子进程未响应，强制杀死")
                self._process.kill()
        if self._thread:
            self._thread.join()
        print("推流线程已结束")

    def is_running(self):
        return self._thread and self._thread.is_alive()
