import threading
import subprocess
import time
import queue
from typing import Any, Callable, List, Optional, Tuple, Dict
import cv2
import numpy as np
from collections import deque

from onnx_sessions.cann_onnx_interface import ICannONNX
from orm.inference_task_orm import InferRun

class LiveInferRunner:
    def __init__(self, cann_onnx: ICannONNX, infer_run: InferRun):
        self.cann_onnx: ICannONNX = cann_onnx
        self.infer_run = infer_run

class FrameData:
    def __init__(self, frame: np.ndarray, frame_index: int, frame_pts: int):
        self.frame = frame
        self.frame_index = frame_index
        self.frame_pts = frame_pts
        self.processed = False
        self.result_frame = None
        self.output = None
        self.timestamp = time.time()

class LiveStreamPublisher2:
    def __init__(self):
        self.source_url = ""
        self.target_rtmp_url = ""
        self._stop_event = threading.Event()
        self._capture_thread = None
        self._process_thread = None
        self._publish_thread = None
        self._process = None
        self._infer_runners: List[LiveInferRunner] = []
        self._result_frame_callback: Optional[Callable[[int, int, Any, np.ndarray, LiveInferRunner], np.ndarray]] = None
        
        # 帧缓冲队列
        self._frame_queue = queue.Queue(maxsize=30)  # 限制队列大小，避免内存溢出
        self._result_queue = queue.Queue(maxsize=30)
        
        # 性能监控
        self._fps_counter = deque(maxlen=100)
        self._processing_times = deque(maxlen=100)
        self._last_fps_print = 0
        self._skip_frame_threshold = 0.5  # 当处理时间超过帧间隔的这个比例时，考虑跳帧
        self._adaptive_skip = True  # 是否启用自适应跳帧
        self._max_skip_frames = 3   # 最大连续跳帧数
        self._current_skip_count = 0
        
        # 配置参数
        self.config = {
            "max_queue_size": 30,       # 最大队列大小
            "monitor_interval": 5.0,    # 性能监控打印间隔(秒)
            "enable_skip_frames": True, # 是否启用跳帧
            "skip_frame_threshold": 0.5, # 跳帧阈值
            "max_skip_frames": 3,       # 最大连续跳帧数
            "adaptive_skip": True,      # 自适应跳帧
            "low_latency_mode": True,   # 低延迟模式
        }

    def add_infer_runner(self, infer_runner: LiveInferRunner):
        """添加推理运行器到处理链中"""
        self._infer_runners.append(infer_runner)
        return self

    def start(self, target_rtmp_url: Optional[str] = None):
        """启动采集和推流"""
        if not self.source_url:
            print("未设置视频源URL，请先调用start_capture")
            return ""
        return self.start_publish(target_rtmp_url)
    
    def start_capture(self, source_url: str):
        """启动视频采集"""
        self.source_url = source_url
        
        if self._capture_thread and self._capture_thread.is_alive():
            print("采集已在进行中")
            return self
        
        # 清空队列
        while not self._frame_queue.empty():
            try:
                self._frame_queue.get_nowait()
            except queue.Empty:
                break
                
        while not self._result_queue.empty():
            try:
                self._result_queue.get_nowait()
            except queue.Empty:
                break
        
        # 启动采集线程
        self._capture_thread = threading.Thread(target=self._capture_worker, daemon=True)
        self._capture_thread.start()
        
        # 启动处理线程
        self._process_thread = threading.Thread(target=self._process_worker, daemon=True)
        self._process_thread.start()
        
        return self

    def start_publish(self, target_rtmp_url: Optional[str] = None):
        """启动视频推流"""
        if target_rtmp_url:
            self.target_rtmp_url = target_rtmp_url

        if len(self.target_rtmp_url) == 0:
            print("推流地址无效")
            return ""
            
        # 启动推流线程
        if not self._publish_thread or not self._publish_thread.is_alive():
            self._publish_thread = threading.Thread(target=self._publish_worker, daemon=True)
            self._publish_thread.start()
            
        return self.target_rtmp_url

    def set_result_frame_callback(self, callback: Callable[[int, int, Any, np.ndarray, LiveInferRunner], np.ndarray]):
        """设置结果帧回调函数"""
        self._result_frame_callback = callback
        return self
        
    def _capture_worker(self):
        """视频采集线程"""
        print(f"开始视频采集: {self.source_url}")
        cap = cv2.VideoCapture(self.source_url)
        if not cap.isOpened():
            print(f"无法打开视频流 {self.source_url}")
            return

        # 获取视频信息
        self.frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        self.fps = cap.get(cv2.CAP_PROP_FPS) or 25
        self.frame_interval = 1.0 / self.fps
        
        print(f"视频信息: {self.frame_width}x{self.frame_height} @ {self.fps}fps")

        frame_index = 0  # 帧序号初始化
        max_counter = 99999999
        last_frame_time = time.time()

        try:
            while not self._stop_event.is_set():
                # 读取视频帧
                ret, frame = cap.read()
                if not ret:
                    print("读取直播帧失败，重试中...")
                    time.sleep(0.1)  # 短暂等待后重试
                    continue
                
                current_time = time.time()
                frame_time = current_time - last_frame_time
                last_frame_time = current_time
                
                # 计算实际FPS
                self._fps_counter.append(frame_time)
                
                # 获取毫秒级时间戳
                frame_pts = int(current_time * 1000)
                # 限制 frame_index 和 frame_pts 最大为 8 位整数
                frame_index %= (max_counter + 1)
                frame_pts %= (max_counter + 1)
                
                # 检查是否需要跳帧
                skip_this_frame = False
                if self.config["enable_skip_frames"] and self._frame_queue.qsize() > 5:
                    # 队列积压，考虑跳帧
                    if self._current_skip_count < self.config["max_skip_frames"]:
                        skip_this_frame = True
                        self._current_skip_count += 1
                    else:
                        self._current_skip_count = 0
                else:
                    self._current_skip_count = 0
                
                if not skip_this_frame:
                    # 创建帧数据对象
                    frame_data = FrameData(frame.copy(), frame_index, frame_pts)
                    
                    # 尝试放入队列，如果队列满则丢弃最旧的帧
                    try:
                        if self.config["low_latency_mode"] and self._frame_queue.full():
                            # 低延迟模式：队列满时丢弃旧帧
                            try:
                                self._frame_queue.get_nowait()
                            except queue.Empty:
                                pass
                        self._frame_queue.put(frame_data, block=False)
                    except queue.Full:
                        # 队列已满，跳过此帧
                        pass
                
                frame_index += 1
                
                # 性能监控
                if current_time - self._last_fps_print > self.config["monitor_interval"]:
                    self._last_fps_print = current_time
                    if len(self._fps_counter) > 0:
                        avg_frame_time = sum(self._fps_counter) / len(self._fps_counter)
                        capture_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
                        
                        avg_process_time = 0
                        if len(self._processing_times) > 0:
                            avg_process_time = sum(self._processing_times) / len(self._processing_times)
                            
                        print(f"性能监控 - 采集FPS: {capture_fps:.2f}, "
                              f"处理时间: {avg_process_time*1000:.2f}ms, "
                              f"队列: {self._frame_queue.qsize()}/{self._result_queue.qsize()}")
                        
                        # 自适应调整跳帧策略
                        if self.config["adaptive_skip"]:
                            process_frame_ratio = avg_process_time / self.frame_interval
                            if process_frame_ratio > 0.9:
                                # 处理时间接近帧间隔，增加跳帧
                                self.config["skip_frame_threshold"] = max(0.3, self.config["skip_frame_threshold"] - 0.1)
                            elif process_frame_ratio < 0.7:
                                # 处理时间充裕，减少跳帧
                                self.config["skip_frame_threshold"] = min(0.8, self.config["skip_frame_threshold"] + 0.1)
                
        except Exception as e:
            print(f"采集线程异常: {e}")
        finally:
            cap.release()
            print("视频采集线程结束")
            
    def _process_worker(self):
        """推理处理线程"""
        print("开始推理处理线程")
        
        try:
            while not self._stop_event.is_set():
                try:
                    # 从队列获取帧，超时1秒
                    frame_data = self._frame_queue.get(timeout=1.0)
                    
                    # 记录处理开始时间
                    start_time = time.time()
                    
                    # 串联执行所有推理模型
                    current_frame = frame_data.frame.copy()
                    current_output = None
                    
                    # 如果没有推理模型，直接使用原始帧
                    if not self._infer_runners:
                        result_frame = current_frame
                    else:
                        # 依次执行每个推理模型
                        for i, runner in enumerate(self._infer_runners):
                            # 执行推理
                            current_output, current_frame = runner.cann_onnx.reference_frame(current_frame)
                            
                            # 对每个推理结果调用回调
                            if self._result_frame_callback:
                                current_frame = self._result_frame_callback(
                                    frame_data.frame_index, frame_data.frame_pts, 
                                    current_output, current_frame, runner
                                )
                        
                        result_frame = current_frame
                    
                    # 记录处理结果
                    frame_data.processed = True
                    frame_data.result_frame = result_frame
                    frame_data.output = current_output
                    
                    # 放入结果队列
                    try:
                        if self.config["low_latency_mode"] and self._result_queue.full():
                            # 低延迟模式：队列满时丢弃旧帧
                            try:
                                self._result_queue.get_nowait()
                            except queue.Empty:
                                pass
                        self._result_queue.put(frame_data, block=False)
                    except queue.Full:
                        # 结果队列已满，丢弃此帧
                        pass
                    
                    # 记录处理时间
                    process_time = time.time() - start_time
                    self._processing_times.append(process_time)
                    
                except queue.Empty:
                    # 队列为空，继续等待
                    continue
                    
        except Exception as e:
            print(f"处理线程异常: {e}")
        finally:
            print("推理处理线程结束")
            
    def _publish_worker(self):
        """推流线程"""
        print(f"开始推流线程: {self.target_rtmp_url}")
        
        # 等待获取第一帧以确定视频参数
        first_frame = None
        while not self._stop_event.is_set() and first_frame is None:
            try:
                frame_data = self._result_queue.get(timeout=1.0)
                first_frame = frame_data.result_frame
            except queue.Empty:
                continue
                
        if first_frame is None or self._stop_event.is_set():
            print("无法获取第一帧，推流线程退出")
            return
            
        # 获取视频参数
        h, w = first_frame.shape[:2]
        fps = self.fps
        
        # 初始化ffmpeg进程
        try:
            cmd = [
                'ffmpeg',
                '-y',
                '-f', 'rawvideo',
                '-pix_fmt', 'bgr24',
                '-s', f'{w}x{h}',
                '-r', str(fps),
                '-i', '-',  # stdin: 处理后的视频帧
                '-i', self.source_url,  # 再次打开用于音频
                '-map', '0:v:0',
                '-map', '1:a:0?',
                '-c:v', 'libx264',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-c:a', 'aac',
                '-pix_fmt', 'yuv420p',
                '-f', 'flv',
                self.target_rtmp_url
            ]
            self._process = subprocess.Popen(cmd, stdin=subprocess.PIPE)
            print(f"开始转推直播流 {self.source_url} -> {self.target_rtmp_url}")
            
            # 先写入第一帧
            self._process.stdin.write(first_frame.tobytes())
            
            # 帧率控制
            target_frame_time = 1.0 / fps
            last_frame_time = time.time()
            
            while not self._stop_event.is_set():
                try:
                    # 从结果队列获取处理好的帧
                    frame_data = self._result_queue.get(timeout=1.0)
                    
                    # 计算此帧应该等待的时间
                    current_time = time.time()
                    elapsed = current_time - last_frame_time
                    wait_time = max(0, target_frame_time - elapsed)
                    
                    # 控制帧率
                    if wait_time > 0 and not self.config["low_latency_mode"]:
                        time.sleep(wait_time)
                    
                    # 写入帧数据
                    if self._process and self._process.poll() is None:
                        self._process.stdin.write(frame_data.result_frame.tobytes())
                    else:
                        print("ffmpeg进程已退出，推流线程终止")
                        break
                        
                    last_frame_time = time.time()
                    
                except queue.Empty:
                    # 结果队列为空，继续等待
                    continue
                    
        except Exception as e:
            print(f"推流线程异常: {e}")
        finally:
            if self._process:
                if self._process.stdin:
                    self._process.stdin.close()
                self._process.wait()
            print("推流线程结束")

    def stop(self):
        """停止视频处理和推流"""
        print("请求停止推流...")
        self._stop_event.set()
        
        # 如果子进程还在，尝试终止
        if self._process and self._process.poll() is None:
            print("终止 ffmpeg 子进程...")
            try:
                self._process.terminate()  # 发送 SIGTERM
                self._process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("子进程未响应，强制杀死")
                self._process.kill()
                
        # 等待线程结束
        threads = [self._capture_thread, self._process_thread, self._publish_thread]
        for thread in threads:
            if thread and thread.is_alive():
                thread.join(timeout=2)
                
        print("推流线程已结束")

    def is_running(self):
        """检查推流是否正在运行"""
        return (self._capture_thread and self._capture_thread.is_alive() and 
                self._process_thread and self._process_thread.is_alive())
                
    def set_config(self, config_dict: Dict[str, Any]):
        """更新配置参数"""
        for key, value in config_dict.items():
            if key in self.config:
                self.config[key] = value
        return self
