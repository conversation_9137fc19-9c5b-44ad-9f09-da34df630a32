

import asyncio
from concurrent.futures import Future, Process<PERSON>oolExecutor
from contextlib import contextmanager
import json
from multiprocessing import Manager
import os
import time
from typing import Any, Dict, List, Tuple
import cv2
from fastapi import APIRouter, Depends, HTTPException, Request, status
import numpy as np
from sqlmodel import SQLModel, Session
from torch import NoneType

from config import settings, SERVER_MEDIAS_BASE_PATH
from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.live_stream_publisher import LiveStreamPublisher
from onnx_sessions.object_detection.yolov11_onnx import get_yolov11_default_extend_json
from onnx_sessions.object_detection.yolov5_onnx import YOLOV5, SimpleTracker, detect_crowd_clusters, detect_roi_crossing_with_transition, draw_crowd_results, get_yolov5_default_extend_json, has_crossing_result, has_crowd_region, has_high_risk, run_live_reference, run_yolov5_obj_det_inference, valid_yolo_result
from orm.object_detection_orm import ObjDetCroGatEndpoint, ObjDetEndpoint, ObjDetLivePublishEndpoint, ObjDetModelLibraryEnum, ObjDetRegIntEndpoint, ObjDetTask, ObjDetTaskCreate, ObjDetTaskPublic, ObjDetTaskStep, ObjDetVectorEndpoint
from orm.model_orm import Model, ModelLibraryEnum, ModelStep, ModelTypeEnum
from orm.step_rom import StepStatusEnum, StepTypeEnum
from database.orm_database import get_session, get_session_need_close
from orm.task_orm import EndpointBase, EndpointTypeEnum, MediaTypeEnum
from orm.vector_task_orm import VectorCollectionEnum, VectorMedia
from routers.vector_router import get_vector_generator, insert_image_vector, run_vector
from service import model_service
from service.object_detection_service import ObjDetTaskService
from service.model_service import ModelService
from serve_utils import delete_folder, download_file_async, ensure_folder_exists, extract_frames_ffmpeg, guess_media_type, hook_notify_backoff, np_image_to_base64, get_sorted_frame_filenames_by_index, run_async_in_background
from service.process_pool_service import ProcessPoolService

OBJ_DET_TASK_TYPE = 'object_detection'
OBJ_DET_BASE_PATH = os.path.join(SERVER_MEDIAS_BASE_PATH, OBJ_DET_TASK_TYPE)

router = APIRouter()

@router.get("/object-detection/tasks/{task_id}", tags=["object-detection"], 
            response_model=ObjDetTaskPublic,
            response_model_exclude_none=True)
async def read_task(task_id: int, session: Session = Depends(get_session)):
    task_service = ObjDetTaskService(session)
    task_db = task_service.get_obj_det_task(task_id)
    if task_db:
        task_public = ObjDetTaskPublic.model_validate(task_db)
        if os.path.exists(task_db.result_path):
            task_public.load_obj_det_result(task_db.result_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@router.get("/object-detection/tasks", tags=["object-detection"], 
            response_model=list[ObjDetTaskPublic],
            response_model_exclude_none=True)
async def read_tasks(offset: int = 0, limit: int = 20, session: Session = Depends(get_session)):
    task_service = ObjDetTaskService(session)
    tasks = task_service.get_obj_det_tasks(offset, limit)
    task_publics = []
    for task in tasks:
        task_public = ObjDetTaskPublic.model_validate(task)
        if os.path.exists(task.result_path):
            task_public.load_obj_det_result(task.result_path)
        task_publics.append(task_public)
    return task_publics

@router.post("/object-detection/tasks", tags=["object-detection"], 
            response_model=ObjDetTaskPublic,
            status_code=status.HTTP_201_CREATED,
            response_model_exclude_none=True)
async def create_task(obj_det_task: ObjDetTaskCreate, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    # 检查endpoints是否与输入媒体相符
    if obj_det_task.media_type == MediaTypeEnum.UNKNOWN:
        obj_det_task.media_type = guess_media_type(obj_det_task.media_url)
    endpoint_step_types, unsupport_endpoint_types = get_endpoint_steps_and_unsupport_endpoints(obj_det_task.endpoints, obj_det_task.media_type)
    if len(unsupport_endpoint_types) > 0:
        unsupport_endpoint_types_str = ", ".join([str(endpoint_type.value) for endpoint_type in unsupport_endpoint_types])
        raise HTTPException(status_code=400, detail=f"Input media is {obj_det_task.media_type.value}, unsupport endpoint types: {unsupport_endpoint_types_str}")
    if obj_det_task.model_library == ObjDetModelLibraryEnum.CUSTOM:
        model_service = ModelService(session)
        model_select = model_service.get_model(obj_det_task.model_id)
    else:
        model_select = get_object_detection_default_model(obj_det_task.model_library)
    # TODO: 检查模型是否存在
    if model_select is None:
        raise HTTPException(status_code=404, detail="Model not found")

    task_service = ObjDetTaskService(session)
    task_db = ObjDetTask.model_validate(obj_det_task.model_dump(exclude={"endpoints"}))
    for ep_data in obj_det_task.endpoints:
        # 先构造 vector 和 live_publish 实例（如果有）
        vector_obj = ObjDetVectorEndpoint.model_validate(ep_data.vector) if ep_data.vector else None
        live_publish_obj = ObjDetLivePublishEndpoint.model_validate(ep_data.live_publish) if ep_data.live_publish else None
        cro_gat_obj = ObjDetCroGatEndpoint.model_validate(ep_data.crowd_gathering) if ep_data.crowd_gathering else None
        reg_int_obj = ObjDetRegIntEndpoint.model_validate(ep_data.regional_intrusion) if ep_data.regional_intrusion else None
        endpoint = ObjDetEndpoint(
            type=ep_data.type,
            vector=vector_obj,
            live_publish=live_publish_obj,
            crowd_gathering=cro_gat_obj,
            regional_intrusion=reg_int_obj,
        )
        task_db.endpoints.append(endpoint)
    print(f"Creating object-detection task {task_db}")
    task_db = task_service.add_obj_det_task(task_db)
    # 检查并创建嵌套文件夹
    folder_path = os.path.join(OBJ_DET_BASE_PATH, model_select.library + '_' + model_select.name + '_' 
                               + str(model_select.id) + '_' + str(task_db.id))
    if os.path.exists(folder_path):
        delete_folder(folder_path)
    ensure_folder_exists(folder_path)
    task_db.local_path = folder_path

    # download
    if obj_det_task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
        task_db.steps.append(ObjDetTaskStep.create_step(StepTypeEnum.download))
        task_db.steps.append(ObjDetTaskStep.create_step(StepTypeEnum.decode))
    # inference
    task_db.steps.append(ObjDetTaskStep.create_step(StepTypeEnum.inference))
    # endpoint
    for endpoint_step_type in endpoint_step_types:
        task_db.steps.append(ObjDetTaskStep.create_step(endpoint_step_type))
    # notify
    task_db.steps.append(ObjDetTaskStep.create_step(StepTypeEnum.notify))
    task_db.frame_folder_path = os.path.join(task_db.local_path, 'frames')
    task_db.result_path = os.path.join(task_db.local_path, 'result.txt')
    task_db = task_service.add_obj_det_task(task_db)
    process_pool.submit_task(run_start_infer_task, OBJ_DET_TASK_TYPE, task_db.id)

    return task_db

@router.delete("/object-detection/tasks/{task_id}", tags=["object-detection"], 
               response_model=ObjDetTaskPublic,
               response_model_exclude_none=True)
async def delete_task(task_id: int, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    task_service = ObjDetTaskService(session)
    # 使用进程池服务取消任务
    process_pool.cancel_task(OBJ_DET_TASK_TYPE, task_id)
    task_db = task_service.get_obj_det_task(task_id)
    if task_db:
        task_public = ObjDetTaskPublic.model_validate(task_db).model_copy(deep=True)
        if os.path.exists(task_db.result_path):
            task_public.load_obj_det_result(task_db.result_path)
        task_service.delete_obj_det_task(task_id)
        delete_folder(task_db.local_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@contextmanager
def task_step(task_service: ObjDetTaskService, task_id, step):
    try:
        task_service.update_obj_det_task_step_status(task_id, step, StepStatusEnum.in_progress)
        yield
        task_service.update_obj_det_task_step_status(task_id, step, StepStatusEnum.success)
    except Exception:
        task_service.update_obj_det_task_step_status(task_id, step, StepStatusEnum.failed)
        raise

def run_start_infer_task(task_type: str, task_id: int, cancel_flags: dict[int, bool]):
    try:
        with get_session_need_close() as session:
            model_service = ModelService(session)
            task_service = ObjDetTaskService(session)
            task = task_service.get_obj_det_task(task_id)
            if task.model_library == ObjDetModelLibraryEnum.CUSTOM:
                model = model_service.get_model(task.model_id)
            else:
                model = get_object_detection_default_model(task.model_library)
            if model is None:
                raise Exception("model not found")
            # download
            if task.get_step_id_by_type(StepTypeEnum.download):
                with task_step(task_service, task_id, StepTypeEnum.download):
                    media_path = asyncio.run(download_file_async(task.media_url, task.local_path))
                    if not media_path:
                        raise Exception("Download failed")
            # decode
            if task.get_step_id_by_type(StepTypeEnum.decode):
                with task_step(task_service, task_id, StepTypeEnum.decode):
                    result = extract_frames_ffmpeg(media_path, task.frame_folder_path)
                    if not result:
                        raise Exception("Decode failed")
            # inference
            publisher = None
            in_progress_step_type_list = []
            with task_step(task_service, task_id, StepTypeEnum.inference):
                class_list = []
                extend_dic = json.loads(model.extend)
                if "classes" in extend_dic:
                    class_list = extend_dic["classes"]
                if task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
                    infer_result_path = run_yolov5_obj_det_inference(model.model_path, class_list, task.frame_folder_path, task.result_path)
                elif task.media_type == MediaTypeEnum.STREAM:
                    model = YOLOV5(model.model_path, class_list)
                    publisher = LiveStreamPublisher(model, task.media_url)
                    publisher.start_capture()
                    # set infer_result_path not empty str
                    infer_result_path = task.media_url
                if not infer_result_path:
                    raise Exception("Inference failed")
                task.result_path = infer_result_path
                task_service.add_obj_det_task(task)
                # endpoints
                obj_det_collection = None
                obj_det_vector_gen = None
                vector_media = None
                frame_progress_var_wrapper = FrameProgressVarWrapper()
                for endpoint in task.endpoints:
                    if endpoint.type == EndpointTypeEnum.vector:
                        # vector
                        task_service.update_obj_det_task_step_status(task_id, StepTypeEnum.vector, StepStatusEnum.in_progress)
                        in_progress_step_type_list.append(StepTypeEnum.vector)
                        vector_endpoint = endpoint
                        obj_det_collection = VectorCollectionEnum.image_resnet50
                        obj_det_vector_gen = get_vector_generator(obj_det_collection)
                        vector_media = VectorMedia(
                            media_data_id=vector_endpoint.vector.media_data_id,
                            media_name=vector_endpoint.vector.media_name,
                            media_type=vector_endpoint.vector.media_type,
                            media_url=task.media_url,
                            frame_folder_path=task.frame_folder_path,
                            local_path=task.local_path
                        )
                    elif endpoint.type == EndpointTypeEnum.crowd_gathering:
                        # crowd gathering
                        task_service.update_obj_det_task_step_status(task_id, StepTypeEnum.crowd_gathering, StepStatusEnum.in_progress)
                        in_progress_step_type_list.append(StepTypeEnum.crowd_gathering)
                    elif endpoint.type == EndpointTypeEnum.regional_intrusion:
                        # regional intrusion
                        task_service.update_obj_det_task_step_status(task_id, StepTypeEnum.regional_intrusion, StepStatusEnum.in_progress)
                        in_progress_step_type_list.append(StepTypeEnum.regional_intrusion)
                        if endpoint.regional_intrusion.sensitivity < 0:
                            endpoint.regional_intrusion.sensitivity = 0
                        if endpoint.regional_intrusion.sensitivity > 1.0:
                            endpoint.regional_intrusion.sensitivity = 1.0
                    elif endpoint.type == EndpointTypeEnum.live_publish:
                        # live publish
                        task_service.update_obj_det_task_step_status(task_id, StepTypeEnum.live_publish, StepStatusEnum.in_progress)
                        in_progress_step_type_list.append(StepTypeEnum.live_publish)
                        target_url = endpoint.live_publish.target_url
                        if len(target_url) > 0:
                            publisher.start_publish(target_url)
            if publisher is None:
                # image or video
                task_public = ObjDetTaskPublic.model_validate(task)
                task_public.load_obj_det_result(task.result_path)
                image_list = get_sorted_frame_filenames_by_index(task.frame_folder_path)
                for image_name in image_list:
                    image_path = os.path.join(task.frame_folder_path, image_name)
                    image = cv2.imread(image_path)
                    yolo_result = task_public.get_yolo_result_by_image_name(image_name)
                    result_image = progress_frame_endpoints(task, image_name, image, yolo_result, 
                                                            obj_det_collection, obj_det_vector_gen, vector_media, # for vector
                                                            frame_progress_var_wrapper)
                # all steps done
                for step_type in in_progress_step_type_list:
                    task_service.update_obj_det_task_step_status(task_id, step_type, StepStatusEnum.success)
            else:
                # live stream
                def on_result_frame_callback(frame_index: int, frame_pts: int, 
                                                         output: Any, frame: np.ndarray) -> np.ndarray:
                    yolo_result: np.ndarray = output
                    image_name = f"frame_{frame_index:08d}_{frame_pts:08d}.jpg"
                    result_frame = progress_frame_endpoints(task, image_name, frame, yolo_result, 
                                                            obj_det_collection, obj_det_vector_gen, vector_media, # for vector
                                                            frame_progress_var_wrapper)
                    return result_frame
                publisher.set_result_frame_callback(on_result_frame_callback)
                # live notify
                notify_task_done(task_id)
                while publisher.is_running():
                    if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                        publisher.stop()
                        break
                    time.sleep(1)
                # exception stop running
                if not ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                    task_service.update_obj_det_task_step_status(task_id, StepTypeEnum.live_publish, StepStatusEnum.failed)
    except Exception as e:
        print(f"run_start_infer_task error: {e}")
        for step_type in in_progress_step_type_list:
            task_service.update_obj_det_task_step_status(task_id, step_type, StepStatusEnum.failed)
    finally:
        # 一定会被调用（无论失败、成功、提前 return、异常）
        print(f"[{task_id}] Task finished, notifying")
        # 直播任务被删除后也会通知，但此时已无法查到对应task，故通知会失效
        notify_task_done(task_id)

class FrameProgressVarWrapper:
    class CrowdGathering:
        last_alert_frame_index: int = 0
    class RegionalIntrusion:
        last_alert_frame_index: int = 0
        trackers: Dict[int, SimpleTracker] = {}
        last_target_centers: Dict[int, List[List[int]]] = {}
    
    crowd_gathering: CrowdGathering = CrowdGathering()
    regional_intrusion: RegionalIntrusion = RegionalIntrusion()
def progress_frame_endpoints(task: ObjDetTask, image_name: str, image: np.ndarray, yolo_result: np.ndarray,
                             collection: str, vector_gen: ICannONNX, media: VectorMedia, # for vector
                             var_wrapper: FrameProgressVarWrapper,
                            ):
    image_name = image_name.split('.')[0]  # 去掉扩展名
    parts = image_name.split('_')
    if len(parts) != 3:
        print(f"非法 image_name 格式: {image_name}")
        return image
    frame_index = int(parts[1])
    frame_pts = int(parts[2])
    if not valid_yolo_result(yolo_result):
        print(f"非法 yolo_result 格式: {yolo_result}")
        return image
    boxes = yolo_result[..., :4].astype(np.int32)
    scores = yolo_result[..., 4]
    classes = yolo_result[..., 5].astype(np.int32)
    
    for endpoint in task.endpoints:
        if endpoint.type == EndpointTypeEnum.vector:
            # vector
            for bbox, score, class_id in zip(boxes, scores, classes):
                bbox_int_list: List[int] = bbox.tolist()
                model_id = -1
                if task.model_library == ObjDetModelLibraryEnum.CUSTOM:
                    model_id = task.model_id
                image_vector_count, image_insert_count, vector_ids = insert_image_vector(collection, vector_gen, image, media,
                                                                            image_name, bbox=bbox_int_list, task_id=task.id, 
                                                                            model_id=model_id)
                if image_vector_count != image_insert_count:
                    print('Vector insert failed')
        elif endpoint.type == EndpointTypeEnum.crowd_gathering:
            person_bboxes = []
            for bbox, score, class_id in zip(boxes, scores, classes):
                if class_id == 0:
                    # person bbox
                    person_bboxes.append(bbox)
            # crowd_risk_pairs = get_crowd_risk_pairs(person_bboxes)
            crowd_info = detect_crowd_clusters(person_bboxes, 
                                                endpoint.crowd_gathering.person_space_coefficient, 
                                                endpoint.crowd_gathering.person_count_threshold,
                                                endpoint.crowd_gathering.roi_bbox)
            # if frame_index >= 0:
            #     # test
            #     frame = cv2.imread(task.get_image_path_by_frame_index_and_pts(frame_index, frame_pts))
            #     # out_frame = draw_crowd_risk_on_image(frame, crowd_risk_pairs)
            #     out_frame = draw_crowd_results(frame, crowd_info)
            #     cv2.imwrite(task.get_image_path_by_frame_index_and_pts(frame_index, frame_pts), out_frame)
            # # if has_high_risk(crowd_risk_pairs):
            if has_crowd_region(crowd_info):
                last_alert_index = var_wrapper.crowd_gathering.last_alert_frame_index
                if last_alert_index == 0 or \
                frame_index - last_alert_index >= endpoint.crowd_gathering.risk_frame_space:
                    var_wrapper.crowd_gathering.last_alert_frame_index = frame_index
                    image_base64 = np_image_to_base64(image)
                    if image_base64 is None:
                        image_base64 = ''
                    run_async_in_background(hook_notify_backoff(endpoint.crowd_gathering.web_hook, {
                        "task_id": task.id,
                        "endpoint_type": endpoint.type.value,
                        "frame_index": frame_index,
                        "frame_pts": frame_pts,
                        "crowd_result": crowd_info,
                        "frame_base64": image_base64
                    }))
                # draw crowd results
                image = draw_crowd_results(image, crowd_info)
        elif endpoint.type == EndpointTypeEnum.regional_intrusion:
            target_bboxes: Dict[int, List[List[int]]] = {}
            target_class_ids = endpoint.regional_intrusion.target_class_ids
            if len(target_class_ids) == 0:
                target_class_ids = [0]
            for class_id in target_class_ids:
                target_bboxes[class_id] = []
            for bbox, score, class_id in zip(boxes, scores, classes):
                if class_id in target_class_ids:
                    # target bbox
                    target_bboxes[class_id].append(bbox)
            cross_info, last_centers = detect_roi_crossing_with_transition(
                target_bboxes=target_bboxes,
                trackers=var_wrapper.regional_intrusion.trackers,
                last_centers=var_wrapper.regional_intrusion.last_target_centers,
                roi_bbox=endpoint.regional_intrusion.roi_bbox,
                direction=endpoint.regional_intrusion.direction,
                ratio=endpoint.regional_intrusion.sensitivity,
            )
            var_wrapper.regional_intrusion.last_target_centers = last_centers
            # if frame_index >= 0:
            #     # test
            #     frame = cv2.imread(task.get_image_path_by_frame_index_and_pts(frame_index, frame_pts))
            #     # out_frame = draw_crowd_risk_on_image(frame, crowd_risk_pairs)
            #     out_frame = draw_crowd_results(frame, crowd_info)
            #     cv2.imwrite(task.get_image_path_by_frame_index_and_pts(frame_index, frame_pts), out_frame)
            # # if has_high_risk(crowd_risk_pairs):
            if has_crossing_result(cross_info):
                last_alert_index = var_wrapper.regional_intrusion.last_alert_frame_index
                if last_alert_index == 0 or \
                frame_index - last_alert_index >= endpoint.regional_intrusion.risk_frame_space:
                    var_wrapper.regional_intrusion.last_alert_frame_index = frame_index
                    image_base64 = np_image_to_base64(image)
                    if image_base64 is None:
                        image_base64 = ''
                    run_async_in_background(hook_notify_backoff(endpoint.regional_intrusion.web_hook, {
                        "task_id": task.id,
                        "endpoint_type": endpoint.type.value,
                        "frame_index": frame_index,
                        "frame_pts": frame_pts,
                        "result": cross_info,
                        # "frame_base64": image_base64
                    }))
                # draw crowd results
                # image = draw_crowd_results(image, crowd_info)

    return image

def notify_task_done(task_id: int):
    with get_session_need_close() as session:
        task_service = ObjDetTaskService(session)
        task = task_service.get_obj_det_task(task_id)

        web_hook_url = task.web_hook or settings.web_hooks.on_object_detection_done
        try:
            with task_step(task_service, task_id, StepTypeEnum.notify):
                task_public = ObjDetTaskPublic.model_validate(task)
                task_public.load_obj_det_result(task.result_path)
                live_publish_endpoint = task.get_endpoint_by_type(EndpointTypeEnum.live_publish)
                if live_publish_endpoint:
                    task_public.inference_result_url = live_publish_endpoint.live_publish.target_url
                result = asyncio.run(hook_notify_backoff(web_hook_url, task_public.model_dump(exclude_none=True)))
                if not result:
                    raise Exception("Notify failed")
        except Exception as e:
            print(f"Notify error: {e}")

def get_endpoint_steps_and_unsupport_endpoints(endpoints: List[EndpointBase], media_type: MediaTypeEnum) \
    -> Tuple[List[StepTypeEnum], List[EndpointTypeEnum]]:
    endpoint_step_types = []
    unsupport_endpoint_types = []
    for endpoint in endpoints:
        if media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
            if endpoint.type == EndpointTypeEnum.vector:
                endpoint_step_types.append(StepTypeEnum.vector)
                continue
        elif media_type in [MediaTypeEnum.STREAM]:
            # if endpoint.type == EndpointTypeEnum.vector:
            #     endpoint_step_types.append(StepTypeEnum.vector)
            #     continue
            if endpoint.type == EndpointTypeEnum.live_publish:
                endpoint_step_types.append(StepTypeEnum.live_publish)
                continue
        if endpoint.type == EndpointTypeEnum.crowd_gathering:
            endpoint_step_types.append(StepTypeEnum.crowd_gathering)
            continue
        if endpoint.type == EndpointTypeEnum.regional_intrusion:
            endpoint_step_types.append(StepTypeEnum.regional_intrusion)
            continue
        unsupport_endpoint_types.append(endpoint.type)

    return endpoint_step_types, unsupport_endpoint_types

def get_object_detection_default_model(model_library: ObjDetModelLibraryEnum):
    if model_library == ObjDetModelLibraryEnum.YOLOV5:
        yolov5_model = Model(
            id=0,
            library=ModelLibraryEnum.yolov5,
            name='yolov5s',
            type=ModelTypeEnum.OBJECT_DETECTION,
            url='',
            web_hook='',
            extend=get_yolov5_default_extend_json(),
            model_path='model_zoo/yolov5s.onnx',
            local_path='',
        )
        return yolov5_model
    elif model_library == ObjDetModelLibraryEnum.YOLOV11:
        yolov11_model = Model(
            id=0,
            library=ModelLibraryEnum.yolov11,
            name='yolov11s',
            type=ModelTypeEnum.OBJECT_DETECTION,
            url='',
            web_hook='',
            extend=get_yolov11_default_extend_json(),
            model_path='model_zoo/yolov11s.onnx',
            local_path='',
        )
        return yolov11_model
    else:
        return None
