
from ast import mod
import asyncio
import base64
from concurrent.futures import Future, ProcessPoolExecutor
from contextlib import contextmanager
import json
from multiprocessing import Manager
import os
import time
from typing import Dict, List, Tuple
import uuid
import cv2
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
import numpy as np
from sqlmodel import Session

from config import SERVER_MEDIAS_BASE_PATH, settings
from database.milvus_database import get_milvus_client
from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.image_classification.resnet_onnx import ResNet50ONNX
from onnx_sessions.face_detection.insightface_detection_onnx import InsightFaceDetONNX
from onnx_sessions.face_detection.insightface_recognition_onnx import InsightFaceRecONNX, load_insightface_rec_result, run_insightface_rec_inference
from onnx_sessions.object_detection.yolov5_onnx import YOLOV5
from orm.inference_task_orm import FacRecFrameResult
from orm.media_orm import FUSED_VIRTUAL_MEDIA_DATA_ID, MediaExtend, MediaTypeEnum
from orm.step_rom import StepStatusEnum, StepTypeEnum
from database.orm_database import get_session, get_session_need_close
from orm.task_orm import DefaultModelLibraryEnum
from orm.vector_task_orm import VectorCollectionEnum, VectorDeleteMediasCreate, VectorDeleteMediasPublic, VectorDeleteMediasResult, VectorExtendInfo, VectorMedia, VectorMediaPublic, VectorMediaStep, VectorSearchMediaCreate, VectorSearchMediaPublic, VectorTask, VectorTaskCreate, VectorTaskPublic, VectorTypeEnum
from routers.models_router import get_object_detection_default_model
from service.milvus_service import MilvusService
from service.model_service import ModelService
from serve_utils import check_file_is_image, crop_and_save, delete_folder, download_file_async, ensure_folder_exists, extract_frame_copy, extract_frames_ffmpeg, hook_notify_backoff, is_valid_bbox, is_valid_kps, save_base64_image
from service.vector_task_service import VectorTaskService
from service.process_pool_service import ProcessPoolService
from service.web_hook_notify_service import notify_async
from utils.logger import get_logger

logger = get_logger()

VECTOR_TASK_TYPE = 'vector'
VECTOR_BASE_PATH = os.path.join(SERVER_MEDIAS_BASE_PATH, VECTOR_TASK_TYPE)

router = APIRouter()

milvus_service = MilvusService(get_milvus_client())

@router.get("/vector/tasks/{task_id}", tags=["vector"], 
            response_model=VectorTaskPublic)
async def read_task(task_id: int, session: Session = Depends(get_session)):
    task_service = VectorTaskService(session)
    task_db = task_service.get_vector_task(task_id)
    if task_db:
        return task_db
    raise HTTPException(status_code=404, detail="Task not found")

@router.get("/vector/tasks", tags=["vector"], 
            response_model=list[VectorTaskPublic])
async def read_tasks(offset: int = 0, limit: int = 20, session: Session = Depends(get_session)):
    task_service = VectorTaskService(session)
    tasks = task_service.get_vector_tasks(offset, limit)
    task_publics = []
    for task in tasks:
        task_public = VectorTaskPublic.model_validate(task)
        task_publics.append(task_public)
    return task_publics

@router.post("/vector/tasks", tags=["vector"], 
             response_model=VectorTaskPublic, status_code=status.HTTP_201_CREATED, 
             summary="媒体数据向量化")
async def create_task(vector_task: VectorTaskCreate, session: Session = Depends(get_session), request: Request = None):
    # disable insightface for now, TODO: fix
    if VectorCollectionEnum.image_insightface in vector_task.collections:
        raise HTTPException(status_code=400, detail="image_insightface not support yet")
    process_pool: ProcessPoolService = request.app.state.process_pool
    task_service = VectorTaskService(session)
    task_db = VectorTask.model_validate(vector_task)
    logger.info(f"Creating vector task {task_db}")
    task_db = task_service.add_vector_task(task_db)
    # 检查并创建嵌套文件夹
    folder_path = os.path.join(VECTOR_BASE_PATH, 'vector_task' + '_' + str(task_db.id))
    if os.path.exists(folder_path):
        delete_folder(folder_path)
    ensure_folder_exists(folder_path)
    task_db.local_path = folder_path

    for media in task_db.medias:
        folder_path = os.path.join(task_db.local_path, media.media_name + '_' + media.media_data_id + '_' + str(media.id))
        if os.path.exists(folder_path):
            # TODO: 是否复用既有文件夹内的内容
            delete_folder(folder_path)
        ensure_folder_exists(folder_path)
        media.local_path = folder_path
        download_step = VectorMediaStep.create_step(StepTypeEnum.download)
        decode_step = VectorMediaStep.create_step(StepTypeEnum.decode)
        vector_step = VectorMediaStep.create_step(StepTypeEnum.vector)
        notify_step = VectorMediaStep.create_step(StepTypeEnum.notify)
        media.steps = [download_step, decode_step, vector_step, notify_step]
        media.frame_folder_path = os.path.join(media.local_path, 'frames')
    
    task_db = task_service.add_vector_task(task_db)

    # 使用进程池服务提交任务
    process_pool.submit_task(run_start_vector_task, VECTOR_TASK_TYPE, task_db.id)

    return task_db

@router.delete("/vector/tasks/{task_id}", tags=["vector"], 
               response_model=VectorTaskPublic)
async def delete_task(task_id: int, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    task_service = VectorTaskService(session)
    # 使用进程池服务取消任务
    process_pool.cancel_task('vector', task_id)
    
    task_db = task_service.get_vector_task(task_id)
    if task_db:
        task_public = VectorTaskPublic.model_validate(task_db).model_copy(deep=True)
        task_service.delete_vector_task(task_id)
        delete_folder(task_db.local_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")


@router.post("/vector/search/medias", tags=["vector"], response_model=VectorSearchMediaPublic)
async def search_medias(search_media: VectorSearchMediaCreate, offset: int = 0, limit: int = Query(default=50, le=100)):
    msg_lines = []
    vector_gen = None
    face_fused_vectors = []
    face_fused_media_extends = []
    if search_media.query_media.type == VectorTypeEnum.FACE_DATASET:
        face_fused_vectors, face_fused_media_extends = get_fused_face_vectors()
    search_media_public = VectorSearchMediaPublic.model_validate(search_media)
    if search_media.collection == VectorCollectionEnum.image_insightface:
        vector_gen = get_vector_generator(search_media.collection, enable_detection=True, 
                                          vectors=face_fused_vectors, media_extends=face_fused_media_extends,
                                          similarity_threshold=0.2)
    else:
        vector_gen = get_vector_generator(search_media.collection) 
    if vector_gen is not None:
        search_vector = []
        query_media_path = None
        query_media_temp_folder = os.path.join(VECTOR_BASE_PATH, str(uuid.uuid4()))
        os.makedirs(query_media_temp_folder)
        if search_media.query_media.recognition_target_id:
            # use recognition_target_id to query fused vector
            if search_media.collection != VectorCollectionEnum.image_insightface:
                # TODO: support other collection
                raise HTTPException(status_code=400, detail="recognition_target_id currently only support for image_insightface")
            rec_filter = (
                f'media_data_id == "{FUSED_VIRTUAL_MEDIA_DATA_ID}" and '
                f'media_extend["recognition_target_id"] == "{search_media.query_media.recognition_target_id}"'
            )
            _, query_results = milvus_service.query_image_vector(
                [MediaTypeEnum.IMAGE],
                [],
                VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET,
                query_filter=rec_filter
            ) 
            if len(query_results) > 0:
                search_vector = query_results[0].vector
                msg_lines.append(f"Got match fused vector, media_extend: {query_results[0].media_extend}")
            else:
                msg_lines.append(f"Got no match fused vector, recognition_target_id: {search_media.query_media.recognition_target_id}")
        if not search_vector:
            # use image to query vector
            if search_media.query_media.base64 != '':
                query_media_path = save_base64_image(search_media.query_media.base64, query_media_temp_folder, 'query_media')
            elif search_media.query_media.url != '':
                query_media_path = await download_file_async(search_media.query_media.url, query_media_temp_folder)
        
            if query_media_path:
                msg_lines.append(f"Query media downloaded")
                image = cv2.imread(query_media_path)
                if search_media.query_media.type == VectorTypeEnum.FACE_DATASET and \
                    search_media.collection == VectorCollectionEnum.image_insightface:
                    msg_lines.append(f"Using image vectors to query fused vector")
                    fused_vector, face_rec_vector, face_rec_result = get_most_match_face_fused_vector(image, vector_gen)
                    logger.info(f"image face search use dataset, face_rec_result: {face_rec_result}")
                    if fused_vector is not None:
                        search_vector = fused_vector
                        msg_lines.append(
                            f"Got match fused vector, "
                            f"recognition_target_id {face_rec_result.class_id}, "
                            f"name: {face_rec_result.class_name}, score: {face_rec_result.rec_score}"
                        )
                    else:
                        msg_lines.append(f"Got no match fused vector")
                if not search_vector:
                    if is_valid_bbox(search_media.query_media.bbox, image.shape[:2]) or is_valid_kps(search_media.query_media.kps, image.shape[:2]):
                        msg_lines.append(f"Using bbox or kps to extract image vectors")
                        vector_tuples = vector_gen.extract_vectors(image, "", search_media.query_media.bbox, search_media.query_media.kps)
                    else:
                        msg_lines.append(f"Using full image to extract image vectors")
                        vector_tuples = vector_gen.extract_vectors(image)
                        if len(vector_tuples) == 0 and \
                            search_media.collection == VectorCollectionEnum.image_insightface:
                            # if image has no face, embed full image as face
                            vector_tuples = vector_gen.extract_vectors(image, bbox=[-2, -2, -2, -2])
                            search_media_public.message = "No face detected or face too big or face too small in the image, embed full image as face"
                            
                        if len(vector_tuples) > 0:
                            search_vector = vector_tuples[0][1]
        
        if search_vector:
            total_count, search_results = milvus_service.search_image_vector(
                media_types=search_media.media_types,
                media_data_ids=search_media.media_data_ids,
                collection=search_media.collection.value,
                vector=search_vector,
                group_by_field=search_media.group_by_field,
                group_size=search_media.group_size,
                query_filter=search_media.query_filter,
                threshold=search_media.query_media.threshold,
                offset=offset,
                limit=limit
            )
            search_media_public.total_count = total_count
            search_media_public.search_result = search_results
        else:
            msg_lines.append("No valid search vector")

        search_media_public.message = "; ".join(msg_lines)
    return search_media_public

@router.delete("/vector/medias", tags=["vector"], 
               response_model=VectorDeleteMediasPublic,
               summary="删除向量化的媒体数据")
async def delete_media(delete_medias: VectorDeleteMediasCreate, session: Session = Depends(get_session)):
    refuse_media_extends = []
    seen_target_ids = set()  # 用于去重
    if VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET in delete_medias.collections:
        _, query_results = milvus_service.query_image_vector(
            [MediaTypeEnum.IMAGE], 
            delete_medias.media_data_ids, 
            VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET
        )
        for result in query_results:
            media_extend = MediaExtend.model_validate_json(result.media_extend)
            target_id = media_extend.recognition_target_id
            if target_id and target_id not in seen_target_ids:
                refuse_media_extends.append(media_extend)
                seen_target_ids.add(target_id)

    delete_medias_public = VectorDeleteMediasPublic.model_validate(delete_medias)
    for collection in delete_medias.collections:
        flush = False
        if collection == VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET:
            flush = True
        delete_count = milvus_service.delete_image_vector([], delete_medias.media_data_ids, collection, flush=flush)
        logger.info(f"Delete in collection {collection} {delete_count} vectors for {delete_medias.media_data_ids}")
        delete_result = VectorDeleteMediasResult(collection=collection, delete_count=delete_count)
        delete_medias_public.delete_result.append(delete_result)
    
    for media_extend in refuse_media_extends:
        fuse_interface_datasete(-1, media_extend)

    return delete_medias_public


@contextmanager
def media_step_with_progress(task_service: VectorTaskService, media_id, step_type):
    """
    媒体步骤上下文管理器，用于管理媒体步骤的状态和进度
    
    Args:
        task_service: 任务服务
        media_id: 媒体ID
        step_type: 步骤类型
    
    Yields:
        update_progress: 更新进度的函数
    """
    def update_progress(progress: float, message: str = ""):
        """
        更新媒体步骤的进度
        
        Args:
            progress: 进度值，0.0-1.0
            message: 进度消息
        """
        task_service.update_vector_media_step_progress(media_id, step_type, progress, message)
    
    try:
        # 开始步骤
        task_service.update_vector_media_status(media_id, step_type, StepStatusEnum.in_progress)
        yield update_progress
        # 成功完成步骤
        task_service.update_vector_media_status(media_id, step_type, StepStatusEnum.success)
    except Exception as e:
        # 步骤失败
        logger.info(f"Media step {step_type} failed for media {media_id}: {e}")
        task_service.update_vector_media_status(media_id, step_type, StepStatusEnum.failed)
        raise

def run_start_vector_task(task_type: str, task_id: int, cancel_flags: dict[int, bool]):
    try:
        with get_session_need_close() as session:
            task_service = VectorTaskService(session)
            task = task_service.get_vector_task(task_id)
            vector_generator_dic = {}
            for collection in task.collections:
                if collection == VectorCollectionEnum.image_insightface or collection == VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET:
                    vector_generator_dic[collection] = get_vector_generator(collection, enable_detection=True)
                else:
                    vector_generator_dic[collection] = get_vector_generator(collection)
            
            # 创建一个列表来跟踪所有媒体的处理状态
            media_processing = []
            
            for media in task.medias:
                # 检查任务是否被取消
                if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                    logger.info(f"[{task_id}] Task was cancelled by user")
                    break
                
                # 添加媒体到处理列表
                media_processing.append(media.id)
                
                # download
                with media_step_with_progress(task_service, media.id, StepTypeEnum.download) as update_progress:
                    update_progress(0.1, "开始下载媒体...")
                    media_path = asyncio.run(download_file_async(
                        media.media_url, 
                        media.local_path,
                        progress_callback=lambda progress, message: update_progress(progress, message)
                    ))
                    if not media_path:
                        logger.info(f"{media.id} Download failed")
                        notify_media_done(task_id, media.id)
                        media_processing.remove(media.id)
                        continue
                
                # decode
                with media_step_with_progress(task_service, media.id, StepTypeEnum.decode) as update_progress:
                    update_progress(0.1, "开始解码媒体...")
                    if check_file_is_image(media_path):
                        result = extract_frame_copy(media_path, media.frame_folder_path)
                        update_progress(1.0, "图像解码完成")
                    else:
                        result = extract_frames_ffmpeg(
                            media_path, 
                            media.frame_folder_path,
                            progress_callback=lambda progress, message: update_progress(progress, message)
                        )
                    if not result:
                        logger.info(f"{media.id} Decode failed")
                        notify_media_done(task_id, media.id)
                        media_processing.remove(media.id)
                        continue
                
                # vector
                with media_step_with_progress(task_service, media.id, StepTypeEnum.vector) as update_progress:
                    update_progress(0.1, "开始向量化...")
                    vector_result, ids_dict = run_vector(task.id, media, vector_generator_dic, update_progress)
                    if not vector_result:
                        logger.info(f"{media.id} Vector failed")
                        notify_media_done(task_id, media.id)
                        media_processing.remove(media.id)
                        continue
                    
                    # for collection, ids in ids_dict.items():
                    #     task_service.add_vector_media_result(media.id, collection, ids)
                
                    if VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET in task.collections:
                        fuse_result = run_fuse_interface_datasete(task_id, media, update_progress)
                        if not fuse_result:
                            logger.info(f"{media.id} Insightface dataset fuse failed")
                            notify_media_done(task_id, media.id)
                            media_processing.remove(media.id)
                            continue
                # notify
                notify_media_done(task_id, media.id)
            
            # 等待所有媒体的通知步骤完成
            # 最多等待30秒，避免无限等待
            wait_start_time = time.time()
            max_wait_time = 30  # 最多等待30秒
            
            while media_processing and time.time() - wait_start_time < max_wait_time:
                with get_session_need_close() as check_session:
                    check_task_service = VectorTaskService(check_session)
                    # 检查每个媒体的通知状态
                    for media_id in list(media_processing):
                        media = check_task_service.get_vector_media(media_id)
                        if media:
                            notify_step = media.get_step_by_type(StepTypeEnum.notify)
                            if notify_step and (notify_step.status == StepStatusEnum.success or 
                                               notify_step.status == StepStatusEnum.failed):
                                media_processing.remove(media_id)
                                logger.info(f"Media {media_id} notification completed with status: {notify_step.status}")
                
                # 短暂休眠，避免频繁查询数据库
                time.sleep(0.5)
            
            if media_processing:
                logger.info(f"Warning: Some media notifications did not complete in time: {media_processing}")
                
    except Exception as e:
        logger.info(f"run_start_vector_task error: {e}")
    finally:
        # 一定会被调用（无论失败、成功、提前 return、异常）
        logger.info(f"[{task_id}] Task finished, notifying")
        # 直播任务被删除后也会通知，但此时已无法查到对应task，故通知会失效
        notify_task_done(task_id)

def notify_media_done(task_id: int, media_id: int):
    with get_session_need_close() as session:
        task_service = VectorTaskService(session)
        task = task_service.get_vector_task(task_id)
        if not task:
            logger.info(f"Task {task_id} not found, cannot notify media {media_id}")
            return
            
        media = task_service.get_vector_media(media_id)
        if not media:
            logger.info(f"Media {media_id} not found, cannot notify")
            return
            
        web_hook_url = task.web_hook or settings.web_hooks.on_vector_done
        
        # 通知回调函数
        def notification_callback(success: bool):
            with get_session_need_close() as callback_session:
                callback_task_service = VectorTaskService(callback_session)
                if success:
                    callback_task_service.update_vector_media_status(media_id, StepTypeEnum.notify, StepStatusEnum.success)
                else:
                    callback_task_service.update_vector_media_status(media_id, StepTypeEnum.notify, StepStatusEnum.failed)
                    logger.info(f"Notify media {media_id} failed")
        
        try:
            # 更新任务状态为通知中
            task_service.update_vector_media_status(media_id, StepTypeEnum.notify, StepStatusEnum.in_progress)
            
            # 使用异步通知服务
            task_public = VectorTaskPublic.model_validate(task)
            task_public.medias.clear()
            task_public.medias.append(media)
            notification_data = task_public.model_dump(exclude_none=True)
            notify_async(web_hook_url, notification_data, notification_callback)
            
        except Exception as e:
            logger.info(f"Prepare media notification error: {e}")
            task_service.update_vector_media_status(media_id, StepTypeEnum.notify, StepStatusEnum.failed)

def notify_task_done(task_id: int):
    with get_session_need_close() as session:
        task_service = VectorTaskService(session)
        task = task_service.get_vector_task(task_id)
        if not task:
            logger.info(f"Task {task_id} not found, cannot notify")
            return

        web_hook_url = task.web_hook or settings.web_hooks.on_vector_done
        
        # 通知回调函数
        def notification_callback(success: bool):
            logger.info(f"Task {task_id} notification {'succeeded' if success else 'failed'}")
        
        try:
            # 使用异步通知服务
            task_public = VectorTaskPublic.model_validate(task)
            notification_data = task_public.model_dump(exclude_none=True)
            notify_async(web_hook_url, notification_data, notification_callback)
            
        except Exception as e:
            logger.info(f"Prepare task notification error: {e}")

def get_vector_generator(collection: VectorCollectionEnum, model_id: int = -1, enable_detection: bool = False, 
                         vectors: List[List[float]]=None, 
                         media_extends: List[MediaExtend]=None, 
                         similarity_threshold: float=0.2) -> ICannONNX:
    if collection == VectorCollectionEnum.image_resnet50:
        obj_det_model = None
        if enable_detection:
            model = None
            if model_id < 0:
                model = get_object_detection_default_model(DefaultModelLibraryEnum.YOLOV5S)
            else:
                with get_session_need_close() as session:
                    model_service = ModelService(session)
                    model = model_service.get_model(model_id)
            if model is not None:
                class_list = []
                extend_dic = json.loads(model.extend)
                if "classes" in extend_dic:
                    class_list = extend_dic["classes"]
                obj_det_model = YOLOV5(model.model_path, class_list)
        # return ResNet50ONNX("model_zoo/resnet50-v2-7.onnx")
        return ResNet50ONNX("model_zoo/resnet50_2048_features.onnx", obj_det_model)
    elif collection == VectorCollectionEnum.image_vgg:
        return None
    elif collection == VectorCollectionEnum.image_insightface or collection == VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET:
        face_detection = None
        if enable_detection:
            face_detection = InsightFaceDetONNX("model_zoo/insightface_buffalo_l_det_10g.onnx")
        # return InsightFaceRecONNX("model_zoo/insightface_buffalo_l_w600k_r50.onnx", face_detection)
        return InsightFaceRecONNX("model_zoo/insightface_glint360k_r50.onnx", face_detection, 
                                  vectors=vectors, media_extends=media_extends, similarity_threshold=similarity_threshold)
    else:
        return None

def run_vector(task_id: int, media: VectorMedia, vector_gen_dic: Dict[str, ICannONNX], update_progress=None):
    vector_item_count = 0
    vector_insert_count = 0
    
    # 获取所有图像文件
    image_list = os.listdir(media.frame_folder_path)
    image_list.sort(key=lambda x: int(x.split('_')[1]))
    total_images = len(image_list)
    
    # 计算每个集合的进度比例
    collections = list(vector_gen_dic.keys())
    collection_count = len(collections)
    collection_progress_step = 1.0 / collection_count if collection_count > 0 else 1.0
    
    ids_dict = {}
    for i, (collection, vector_gen) in enumerate(vector_gen_dic.items()):
        if vector_gen is not None:
            # 计算当前集合的进度基准
            collection_progress_base = i * collection_progress_step
            
            if update_progress:
                update_progress(collection_progress_base, f"处理集合 {collection} ({i+1}/{collection_count})...")
            
            for j, image_name in enumerate(image_list):
                # 计算当前图像的进度
                image_progress = j / total_images * collection_progress_step
                if update_progress:
                    update_progress(
                        collection_progress_base + image_progress, 
                        f"处理集合 {collection} ({i+1}/{collection_count}): 图像 {j+1}/{total_images}"
                    )
                
                image_path = os.path.join(media.frame_folder_path, image_name)
                image = cv2.imread(image_path)
                bbox = [-1, -1, -1, -1]
                flush = False
                if collection == VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET:
                    # 只针对可以正常提取人脸的图片做向量化，如果直接对全图向量化，则会污染已知人脸的融合数据
                    # bbox = [-2, -2, -2, -2]
                    flush = True
                image_vector_count, image_insert_count, vector_ids = insert_image_vector(collection, vector_gen, image, media,
                                                                             image_name, bbox=bbox, task_id=task_id, flush=flush)
                vector_item_count += image_vector_count
                vector_insert_count += image_insert_count
                if collection not in ids_dict:
                    ids_dict[collection] = []
                ids_dict[collection].extend(vector_ids)
    
    if update_progress:
        update_progress(1.0, f"向量化完成: 处理 {vector_item_count} 项，成功插入 {vector_insert_count} 项")
    
    if vector_item_count == vector_insert_count:
        return True, ids_dict
    else:
        return False, ids_dict
    
def insert_image_vector(collection: str, vector_gen: ICannONNX, image: np.ndarray, media: VectorMedia,
                        image_name: str = '',
                        crop_path: str = '',
                        bbox: List[int] = [-1, -1, -1, -1],
                        kps: List[List[int]] = [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]],
                        score: float = -1.0,
                        task_id: int = -1,
                        model_id: int = -1,
                        flush: bool = False) -> Tuple[int, int, list[int]]:
    vector_tuples = vector_gen.extract_vectors(image, image_name, bbox, kps, score)
    if len(vector_tuples) == 0:
        logger.info(f"{image_name} {collection} 提取失败")
        return 0, 0, []
    vector_insert_count = 0
    vector_ids = []
    for vector_tuple in vector_tuples:
        vector_extend_info, vectors = vector_tuple
        vector_extend_info.vector_task_id = task_id
        vector_extend_info.model_id = model_id
        if bbox != [-1, -1, -1, -1] or kps != [[-1, -1], [-1, -1], [-1, -1], [-1, -1], [-1, -1]]:
            vector_extend_info.crop_path = crop_path
        insert_ret_dic = milvus_service.add_image_vector(collection, media, vector_extend_info, vectors, flush)
        vector_insert_count += insert_ret_dic['insert_count']
        vector_ids.extend(insert_ret_dic['ids'])

    return len(vector_tuples), vector_insert_count, vector_ids

def run_fuse_interface_datasete(task_id: int, media: VectorMedia, update_progress=None):
    try:
        media_extend = MediaExtend.model_validate_json(media.media_extend)
    except Exception as e:
        logger.warning(f"media_extend parse error: {media.media_extend}")
        return False
    return fuse_interface_datasete(task_id, media_extend, update_progress)

def fuse_interface_datasete(task_id: int, media_extend: MediaExtend, update_progress=None):
    if not media_extend.recognition_target_id:
        logger.warning(f"recognition_target_id is empty, skip.")
        return False

    # Step 1. 查询所有向量（排除已融合的虚拟 media_data_id）
    rec_filter = (
        f'media_data_id != "{FUSED_VIRTUAL_MEDIA_DATA_ID}" and '
        f'media_extend["recognition_target_id"] == "{media_extend.recognition_target_id}"'
    )

    _, query_results = milvus_service.query_image_vector(
        [MediaTypeEnum.IMAGE],
        [],
        VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET,
        query_filter=rec_filter
    )

    if len(query_results) == 0:
        logger.warning(f"recognition_target_id: {media_extend.recognition_target_id} 未查询到向量，删除融合")
        del_filter = (
            f'media_data_id == "{FUSED_VIRTUAL_MEDIA_DATA_ID}" and '
            f'media_extend["recognition_target_id"] == "{media_extend.recognition_target_id}"'
        )
        try:
            delete_count = milvus_service.delete_image_vector(
                [MediaTypeEnum.IMAGE],
                [],
                VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET,
                query_filter=del_filter
            )
            logger.info(f"删除融合向量数量: {delete_count}")
        except Exception as e:
            logger.warning(f"删除融合向量失败: {e}")
        return False

    # Step 2. 提取并过滤异常向量 + 无 score 的向量
    vectors, weights = [], []
    for v in query_results:
        score = getattr(v, 'score', None)

        # ✅ 跳过无 score 或无效 score 的向量
        if score is None or not isinstance(score, (int, float)) or score <= 0.6:
            logger.warning(f"跳过无效 score 向量: score={score}")
            continue

        try:
            vec = np.array(v.vector, dtype=np.float32)
            if vec.ndim != 1 or np.linalg.norm(vec) < 1e-6 or np.isnan(vec).any():
                logger.warning(f"跳过异常向量: {vec[:5]}...")
                continue
            vectors.append(vec)
            weights.append(score)
        except Exception as e:
            logger.warning(f"跳过无法解析的向量: {e}")
            continue

    if not vectors:
        logger.warning("无有效向量，跳过融合")
        return False

    vectors = np.stack(vectors)
    weights = np.array(weights, dtype=np.float32)
    weights_sum = weights.sum()
    if weights_sum <= 0:
        logger.warning("融合权重总和为0，跳过")
        return False
    weights /= weights_sum

    # Step 3. 方向过滤
    center = np.average(vectors, axis=0, weights=weights)
    center /= np.linalg.norm(center)

    cos_sim = np.dot(vectors, center)
    keep_mask = cos_sim > 0.3
    vectors = vectors[keep_mask]
    weights = weights[keep_mask]

    if len(vectors) < 1:
        logger.warning("全部向量偏离中心，跳过融合")
        return False

    weights /= weights.sum()
    fused_vector = np.average(vectors, axis=0, weights=weights)
    norm = np.linalg.norm(fused_vector)
    if norm < 1e-6:
        logger.warning("融合向量范数过小，跳过")
        return False
    fused_vector /= norm

    # Step 4. 插入融合向量
    fused_media = VectorMedia(
        media_data_id=FUSED_VIRTUAL_MEDIA_DATA_ID,
        media_name='',
        media_type=MediaTypeEnum.IMAGE,
        media_url='',
        media_extend=media_extend.model_dump_json(),
    )
    fused_extend_info = VectorExtendInfo(
        vector_task_id=task_id,
    )

    try:
        insert_ret_dic = milvus_service.add_image_vector(
            VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET,
            fused_media,
            fused_extend_info,
            fused_vector.tolist()
        )
    except Exception as e:
        logger.error(f"插入融合向量失败: {e}")
        return False

    ids = insert_ret_dic.get('ids', [])
    if insert_ret_dic.get('insert_count', 0) == 1 and len(ids) == 1:
        logger.info(
            f"成功插入融合向量: recognition_target_id={media_extend.recognition_target_id}, "
            f"样本数={len(vectors)}, 平均score={weights.mean():.4f}, 范数={norm:.4f}"
        )

        # Step 5. 删除旧融合向量
        del_filter = (
            f'media_data_id == "{FUSED_VIRTUAL_MEDIA_DATA_ID}" and '
            f'media_extend["recognition_target_id"] == "{media_extend.recognition_target_id}" and '
            f'id < {ids[0]}'
        )
        try:
            delete_count = milvus_service.delete_image_vector(
                [MediaTypeEnum.IMAGE],
                [],
                VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET,
                query_filter=del_filter
            )
            logger.info(f"删除旧融合向量数量: {delete_count}")
        except Exception as e:
            logger.warning(f"删除旧融合向量失败: {e}")
        return True
    else:
        logger.error(f"插入融合向量失败，返回结果: {insert_ret_dic}")
        return False


def get_fused_face_vectors(target_ids: list[str] = ["all"]):
    if len(target_ids) > 0 and "all" not in target_ids:
        target_id_list_str = ", ".join([f'"{x}"' for x in target_ids])
        rec_filter = f"media_data_id == \"{FUSED_VIRTUAL_MEDIA_DATA_ID}\" and media_extend[\"recognition_target_id\"] in [{target_id_list_str}]"
        _, query_results = milvus_service.query_image_vector([MediaTypeEnum.IMAGE], [],     
                                                    VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET, 
                                                    query_filter=rec_filter)
    else:
        # get all fused vector
        _, query_results = milvus_service.query_image_vector([MediaTypeEnum.IMAGE], [],     
                                                    VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET, 
                                                    query_filter=f"media_data_id == \"{FUSED_VIRTUAL_MEDIA_DATA_ID}\"")
    vectors: list[list[float]] = []
    media_extends: list[MediaExtend] = []
    for result in query_results:
        vectors.append(result.vector)
        media_extend = MediaExtend.model_validate_json(result.media_extend)
        media_extends.append(media_extend)

    return vectors, media_extends

def get_most_match_face_fused_vector(image: np.ndarray, fac_rec_model: InsightFaceRecONNX):
    fused_vector = None
    face_rec_vector = None
    face_rec_result = None

    vector_tuples = fac_rec_model.extract_vectors(image)
    
    if not vector_tuples:
        logger.error(f"未检测到人脸或提取向量失败")
        return fused_vector, face_rec_vector, face_rec_result
    
    max_rec_score = 0.0
    max_class_id = ""
    for vector_info, face_vector in vector_tuples:
        if vector_info.rec_score > max_rec_score:
            max_rec_score = vector_info.rec_score
            max_class_id = vector_info.class_id
            face_rec_vector = face_vector
            face_rec_result = FacRecFrameResult(
                frame_index=0,
                frame_pts=0,
                bbox=vector_info.bbox,
                kps=vector_info.kps,
                score=vector_info.score,
                download_url="",
                class_id=vector_info.class_id,
                class_name=vector_info.class_name,
                rec_score=vector_info.rec_score
            )
    if max_class_id:
        rec_filter = (
            f'media_data_id == "{FUSED_VIRTUAL_MEDIA_DATA_ID}" and '
            f'media_extend["recognition_target_id"] == "{max_class_id}"'
        )
        _, fused_results = milvus_service.query_image_vector(
            [MediaTypeEnum.IMAGE],
            [],
            VectorCollectionEnum.IMAGE_INSIGHTFACE_DATASET,
            query_filter=rec_filter
        )
        if len(fused_results) > 0:
            fused_vector = fused_results[0].vector

    return fused_vector, face_rec_vector, face_rec_result
