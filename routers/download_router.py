
import os
from urllib.parse import unquote, urlparse
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
from config import settings


router = APIRouter()

@router.get("/download/{file_path:path}", tags=["download"])
async def download_file(file_path: str):
    """下载文件"""
    if os.path.exists(file_path):
        file_name = os.path.basename(file_path)
        return FileResponse(file_path,
                            headers={"Content-Disposition": "inline"},
                            filename=file_name)
    else:
        raise HTTPException(status_code=404, detail="File not found")

def generate_download_url(file_path: str) -> str:
    """生成下载地址"""
    if not file_path:
        return ''
    ip = settings.api_server.external_ip
    port = settings.api_server.port
    return f'http://{ip}:{port}/download/{file_path}'

def restore_file_path(download_url: str) -> str:
    """从下载地址中还原出原始 file_path"""
    if not download_url:
        return ''
    
    parsed = urlparse(download_url)
    path = parsed.path
    # 预期是 /download/{file_path}
    if not path.startswith('/download/'):
        return ''
    # 去除前缀，并反转 URL 编码
    file_path = path[len('/download/'):]
    return unquote(file_path)
