from functools import partial
import uvicorn
import hashlib
import json
import os
import asyncio
from pathlib import Path
import shutil
import aiohttp
from fastapi.responses import FileResponse
import torchaudio
import torio
from fastapi import APIRouter, Depends, FastAPI, HTTPException, status
from pydantic import BaseModel
from typing import List, Callable
from enum import Enum
from urllib.parse import urlparse
from audio_denoiser.nsnet2_denoiser.enhance_onnx import NSnet2Enhancer
from audio_denoiser.DeepFilterNet3.deep_filter_net_onnx import DeepFilterNet3Enhancer
from audio_denoiser.FullSubNetPlus.full_sub_net_plus_onnx import FullSubNetPlusEnhancer
from serve_run import MediaBase, MediaInput, MediaOutput, ServeRun, ModelEnum, RunTypeEnum, ServeRunCreate, ServeStep, StepTypeEnum, StepStatusEnum, \
    ServeRunPublic
from config import settings, versions
import soundfile as sf
import backoff
from database.orm_database import create_db_and_tables, get_session, get_session_need_close
from sqlmodel import Session
from serve_run_service import ServeRunService
from contextlib import asynccontextmanager

# 日志记录器，用于追踪 backoff 的重试
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


router = APIRouter()

'''
1. 仅实现最核心的AI处理能力与部分输入资源的兼容处理（例如音频重采样），不支持处理后的文件再处理（例如转码），此部分应交由其他模块处理，尽量宽进严出；
2. 仅支持创建（开始），查询，删除；
3. 创建支持重置（重新生成）；
4. 支持状态变更WebHook，且支持地址列表；
'''

AUDIO_DENOISER_BASE_PATH = 'serve_medias/audio/denoiser'


@router.get("/audio/denoiser/output/{hash_id}/{file_name}", tags=["audio-denoiser"])
async def download_audio_denoiser_output(hash_id: str, file_name: str, session: Session = Depends(get_session)):
    serve_run_service = ServeRunService(session)
    serve_run = serve_run_service.get_serve_run_by_hash_id(hash_id)
    # print(f'serve_run:{serve_run}')
    if serve_run:
        file_path = serve_run.run_folder + '/output/' + file_name
        if os.path.exists(file_path):
            return FileResponse(file_path, 
                                media_type='audio/wav', 
                                headers={"Content-Disposition": "inline"},
                                filename=file_name)
        else:
            raise HTTPException(status_code=404, detail="File not found")
    raise HTTPException(status_code=404, detail="Run not found")

@router.get("/audio/denoiser/{hash_id}", tags=["audio-denoiser"], response_model=ServeRunPublic, response_model_exclude={"input_medias", "run_folder"})
async def get_serve_run(hash_id: str, session: Session = Depends(get_session)):
    serve_run_service = ServeRunService(session)
    serve_run = serve_run_service.get_serve_run_by_hash_id(hash_id)
    if serve_run:
        return serve_run
    raise HTTPException(status_code=404, detail="Run not found")

@router.delete("/audio/denoiser/{hash_id}", tags=["audio-denoiser"], response_model=ServeRunPublic, response_model_exclude={"input_medias", "run_folder"})
async def delete_serve_run(hash_id: str, session: Session = Depends(get_session)):
    serve_run_service = ServeRunService(session)
    serve_run = serve_run_service.get_serve_run_by_hash_id(hash_id)
    if serve_run and len(serve_run.run_folder) > 0:
        run_folder_path = Path(serve_run.run_folder)
        # 检查文件夹是否存在
        if run_folder_path.exists() and run_folder_path.is_dir():
            try:
                # 删除文件夹及其所有内容
                shutil.rmtree(run_folder_path)
                print(f"Successfully deleted folder: {run_folder_path}")
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Failed to delete folder: {e}")
        serve_run_service.delete_serve_run(serve_run.pri_id)
        # 返回 ServeRun 对象作为响应
        return serve_run
    raise HTTPException(status_code=404, detail="Run not found")

@router.post("/audio/denoiser", tags=["audio-denoiser"], response_model=ServeRunPublic, response_model_exclude={"input_medias", "run_folder"}, 
          status_code=status.HTTP_201_CREATED)
async def create_serve_run(serve_run: ServeRunCreate, session: Session = Depends(get_session)):
    serve_run_service = ServeRunService(session)
    hash_id = calculate_json_md5(serve_run.model_dump_json())
    db_serve_run = serve_run_service.get_serve_run_by_hash_id(hash_id)
    if db_serve_run:
        print(f"ServeRun with hash_id: {hash_id} already exists. Returning existing ServeRun.")
        return db_serve_run
    db_serve_run = ServeRun.model_validate(serve_run)
    print(f"Creating 0 ServeRun {db_serve_run}")
    # 检查并创建嵌套文件夹
    folder_path = os.path.join(AUDIO_DENOISER_BASE_PATH, hash_id + '_' + serve_run.id)
    ensure_folder_exists(folder_path)
    db_serve_run.hash_id = hash_id
    db_serve_run.run_folder = folder_path

    print(f"Creating ServeRun with hash_id: {hash_id} and id: {serve_run.id}")
    for input_media in db_serve_run.input_medias:
        transcodeStep = ServeStep.create_step(StepTypeEnum.transcode)
        inferenceStep = ServeStep.create_step(StepTypeEnum.inference)
        notifyStep = ServeStep.create_step(StepTypeEnum.notify)
        output_media = MediaOutput(id=input_media.id, url='', model=input_media.model,
                             steps=[transcodeStep, inferenceStep, notifyStep])
        db_serve_run.output_medias.append(output_media)
        # await serve_run.save_to_json()
        db_serve_run = serve_run_service.add_serve_run(db_serve_run)
        
        task = asyncio.create_task(start_serve_run(db_serve_run, input_media, folder_path))
        # 使用 partial 创建回调函数
        callback_with_args = partial(sync_wrapper_done_serve_run_callback, 
                                     serve_run=db_serve_run, output_media=output_media, folder_path=folder_path)
        task.add_done_callback(callback_with_args)

    # print(f"Creating 1 ServeRun {db_serve_run}")
    return db_serve_run

def calculate_json_md5(json_str: str):
    # 将 JSON 字符串编码为字节
    json_bytes = json_str.encode('utf-8')
    # 计算 MD5 哈希值
    md5_hash = hashlib.md5(json_bytes)
    # 返回十六进制表示的 MD5 值
    return md5_hash.hexdigest()

def ensure_folder_exists(folder: str):
    if not os.path.exists(folder):
        os.makedirs(folder)

def generate_file_name(media: MediaBase) -> str:
    parsed_url = urlparse(media.url)
    filename = os.path.basename(parsed_url.path)
    
    if media.id:
        filename = media.id + '_' + filename
    
    return filename

def generate_file_path(media: MediaBase, base_folder_path: str) -> str:
    """生成文件路径"""
    filename = generate_file_name(media)
    file_path = os.path.join(base_folder_path, filename)
    
    return file_path

def generate_output_download_url(hash_id: str, file_name: str) -> str:
    """生成下载地址"""
    ip = settings.api_server.external_ip
    port = settings.api_server.port
    return f'http://{ip}:{port}/audio/denoiser/output/{hash_id}/{file_name}'

async def start_serve_run(serve_run: ServeRun, input_media: MediaInput, folder_path: str):
    with get_session_need_close() as session:
        serve_run_service = ServeRunService(session)
        output_media = serve_run.get_output_media_by_id(input_media.id)
        file_path = generate_file_path(input_media, folder_path)
        serve_run_service.update_step_status(output_media.pri_id, StepTypeEnum.transcode, StepStatusEnum.in_progress)
        try:
            await download_file_async(input_media.url, file_path)
        except Exception as e:
            serve_run_service.update_step_status(output_media.pri_id, StepTypeEnum.transcode, StepStatusEnum.failed)
            return False
        print(f"File downloaded: {file_path}")
        wav_path = await preprocces_audio(input_media, file_path)
        print(f"File processed: {wav_path} (Media ID: {input_media.id})")
        serve_run_service.update_step_status(output_media.pri_id, StepTypeEnum.transcode, StepStatusEnum.success)
        if serve_run.type == RunTypeEnum.denoiser:
            output_folder = os.path.join(folder_path, 'output')
            ensure_folder_exists(output_folder)
            # 更新步骤状态为 'inference in_progress'
            serve_run_service.update_step_status(output_media.pri_id, StepTypeEnum.inference, StepStatusEnum.in_progress)
            # 音频增强处理 (异步执行)
            output_file_name = ''
            if input_media.model == ModelEnum.NSNet2:
                output_file_name = await run_in_executor_nsnet2(wav_path, output_folder)
            elif input_media.model == ModelEnum.DeepFilterNet3:
                output_file_name = await run_in_executor_deepfilternet3(wav_path, output_folder)
            elif input_media.model == ModelEnum.FullSubNetPlus:
                output_file_name = await run_in_executor_fullsubnetplus(wav_path, output_folder)

            # 更新 output_media 的 URL
            output_media.url = generate_output_download_url(serve_run.hash_id, output_file_name)
            serve_run_service.update_media_output(output_media)
            print(f'Writ done output to:{output_file_name}')
            return True

# 使用线程池异步化模型推理操作
async def run_in_executor_nsnet2(wav_path, output_folder):
    return await asyncio.to_thread(nsnet2_inference, wav_path, output_folder)

async def run_in_executor_deepfilternet3(wav_path, output_folder):
    return await asyncio.to_thread(deepfilternet3_inference, wav_path, output_folder)

async def run_in_executor_fullsubnetplus(wav_path, output_folder):
    return await asyncio.to_thread(fullsubnetplus_inference, wav_path, output_folder)

# 模型推理函数
def nsnet2_inference(wav_path, output_folder):
    enhancer = NSnet2Enhancer(fs=48000)
    sigIn, fs = sf.read(wav_path)
    if len(sigIn.shape) > 1:
        sigIn = sigIn[:, 0]
    outSig = enhancer(sigIn, fs)
    output_file_name = ModelEnum.NSNet2 + '_' + os.path.basename(wav_path)
    output_file = os.path.join(output_folder, output_file_name)
    sf.write(output_file, outSig, fs)
    return output_file_name

def deepfilternet3_inference(wav_path, output_folder):
    enhancer = DeepFilterNet3Enhancer()
    output_file_name = ModelEnum.DeepFilterNet3 + '_' + os.path.basename(wav_path)
    output_file = os.path.join(output_folder, output_file_name)
    enhancer(wav_path, output_file)
    return output_file_name

def fullsubnetplus_inference(wav_path, output_folder):
    enhancer = FullSubNetPlusEnhancer()
    output_file_name = ModelEnum.FullSubNetPlus + '_' + os.path.basename(wav_path)
    output_file = os.path.join(output_folder, output_file_name)
    enhancer(wav_path, output_file)
    return output_file_name

# 包装器函数，调度异步函数的执行
def sync_wrapper_done_serve_run_callback(future: asyncio.Future, serve_run: ServeRun, output_media: MediaOutput, folder_path: str):
    # 创建一个新的异步任务以调用异步回调
    asyncio.create_task(done_serve_run_callback(future, serve_run, output_media, folder_path))
    
async def done_serve_run_callback(future: asyncio.Future, serve_run: ServeRun, output_media: MediaOutput, folder_path: str):
    print(f"Serve run completed: {serve_run.id}, result={future}, media_id={output_media.id}")
    with get_session_need_close() as session:
        if future.done():
            serve_run_service = ServeRunService(session)
            if future.result() == True:
                serve_run_service.update_step_status(output_media.pri_id, StepTypeEnum.inference, StepStatusEnum.success)
            else:
                serve_run_service.update_step_status(output_media.pri_id, StepTypeEnum.inference, StepStatusEnum.failed)
            inference_done_count = 0
            notify_queued_count = 0
            for step in serve_run_service.get_steps_by_media_pri_id(output_media.pri_id):
                if step.type == StepTypeEnum.inference \
                    and (step.status == StepStatusEnum.success or step.status == StepStatusEnum.failed):
                    inference_done_count += 1
                if step.type == StepTypeEnum.notify \
                    and step.status == StepStatusEnum.queued:
                    notify_queued_count += 1
                
            
            # print(f"All done: {serve_run.model_dump_json()}")
            medias_count = len(serve_run.output_medias)
            if inference_done_count == medias_count and notify_queued_count == medias_count:
                for output_media in serve_run.output_medias:
                    serve_run_service.update_step_status(output_media.pri_id, StepTypeEnum.notify, StepStatusEnum.in_progress)
                asyncio.create_task(hook_inference_done(serve_run, output_media))

# 发送 webhook 请求的函数，添加指数退避重试机制
@backoff.on_exception(
    # 使用指数退避策略
    backoff.expo,
    # 针对 ClientError 进行重试
    aiohttp.ClientError,
    # base=2,
    # 最大重试次数为 5 次
    max_tries=settings.web_hooks.max_try_count,
    # 最大重试时间为 60 秒
    max_time=settings.web_hooks.max_try_time,
    # 防止重试高峰，可以使用抖动机制
    jitter=backoff.full_jitter,
    # jitter=None,
    logger=logger
)
async def send_webhook_request(serve_run: ServeRun):
    if settings.web_hooks.on_inference_done and serve_run:
        async with aiohttp.ClientSession() as session:
            async with session.post(settings.web_hooks.on_inference_done, json=serve_run.model_dump()) as response:
                if response.status == 200:
                    print(f"Webhook response: {response.status} {await response.text()}")
                    with get_session_need_close() as db_session:
                        serve_run_service = ServeRunService(db_session)
                        for output_media in serve_run.output_medias:
                            serve_run_service.update_step_status(output_media.pri_id, StepTypeEnum.notify, StepStatusEnum.success)
                else:
                    raise aiohttp.ClientError(f"Webhook failed with status: {response.status}")

async def hook_inference_done(serve_run: ServeRun, media: MediaBase):
    try:
        # 使用 backoff 装饰器处理重试机制
        await send_webhook_request(serve_run)
    except aiohttp.ClientError as e:
        print(f"Webhook failed after retries: {e}")
        with get_session_need_close() as db_session:
            serve_run_service = ServeRunService(db_session)
            # 如果重试次数用尽，标记所有 notify 步骤为失败
            for output_media in serve_run.output_medias:
                serve_run_service.update_step_status(output_media.pri_id, StepTypeEnum.notify, StepStatusEnum.failed)

async def download_file_async(url: str, file_path: str, callback: Callable[[str], None] = None):
    """异步从给定URL下载文件并保存到本地路径"""
    try:
        # 禁用总超时时间
        timeout = aiohttp.ClientTimeout(total=None)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url) as response:
                response.raise_for_status()  # 确认请求成功
                with open(file_path, 'wb') as f:
                    while True:
                        chunk = await response.content.read(8192)
                        if not chunk:
                            break
                        f.write(chunk)
                
                # 文件下载完成后调用回调函数
                if callback is not None:
                    await callback(file_path)
    except aiohttp.ClientError as e:
        print(f"Failed to download {url}: {e}")
        raise  # 重新抛出异常，让调用者捕捉
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        raise  # 抛出其他异常


async def preprocces_audio(media: MediaBase, file_path: str):
    """文件下载完成后执行的操作"""
    # 可以在这里执行其他操作，如重采样等
    # 转换为 .wav 格式
    if not file_path.endswith('.wav'):
        file_path_no_ext, ext = os.path.splitext(file_path)
        new_file_path = file_path_no_ext + '.wav'
        waveform, sample_rate = torchaudio.load(file_path)
        # waveform, sample_rate = torchaudio.load(uri=file_path, backend='ffmpeg')
        
        # 保存为 .wav 格式
        torchaudio.save(new_file_path, waveform, sample_rate)
        file_path = new_file_path
    
    # 重采样为 48000 Hz
    waveform, sample_rate = torchaudio.load(file_path)
    if sample_rate != 48000:
        waveform = torchaudio.transforms.Resample(orig_freq=sample_rate, new_freq=48000)(waveform)
        torchaudio.save(file_path, waveform, 48000)
    
    return file_path
