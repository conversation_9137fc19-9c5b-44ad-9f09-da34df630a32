

import asyncio
from concurrent.futures import Future, Process<PERSON>oolExecutor
from contextlib import contextmanager
import json
import os
import time
from typing import List, <PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlmodel import Session
from multiprocessing import Manager

from config import SERVER_MEDIAS_BASE_PATH, settings
from onnx_sessions.image_enhance.real_esrgan_onnx import run_img_enh_image_inference, run_live_reference, run_img_enh_video_inference
from onnx_sessions.live_stream_publisher import LiveStreamPublisher
from orm.image_enhance_orm import ImgEnhEndpoint, ImgEnhLivePublishEndpoint, ImgEnhTask, ImgEnhTaskCreate, ImgEnhTaskPublic, ImgEnhTaskStep, ImgEnhVectorEndpoint, LivePublishEndpoint
from orm.media_orm import MediaTypeEnum
from orm.model_orm import ModelStep
from orm.step_rom import StepStatusEnum, StepTypeEnum
from database.orm_database import get_session, get_session_need_close
from orm.task_orm import EndpointBase, EndpointTypeEnum
from routers.download_router import generate_download_url
from service import model_service
from service.image_enhance_service import ImgEnhTaskService
from service.model_service import ModelService
from serve_utils import delete_folder, download_file_async, ensure_folder_exists, extract_frames_ffmpeg, hook_notify_backoff, guess_media_type
from service.process_pool_service import ProcessPoolService

IMG_ENH_TASK_TYPE = 'image_enhance'
IMG_ENH_BASE_PATH = os.path.join(SERVER_MEDIAS_BASE_PATH, IMG_ENH_TASK_TYPE)

router = APIRouter()

@router.get("/image-enhance/tasks/{task_id}", tags=["image-enhance"], 
            response_model=ImgEnhTaskPublic,
            response_model_exclude_none=True)
async def read_task(task_id: int, session: Session = Depends(get_session)):
    task_service = ImgEnhTaskService(session)
    task_db = task_service.get_img_enh_task(task_id)
    if task_db:
        task_public = ImgEnhTaskPublic.model_validate(task_db)
        if os.path.exists(task_db.result_path):
            task_public.inference_result_url = generate_download_url(task_db.result_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@router.get("/image-enhance/tasks", tags=["image-enhance"], 
            response_model=list[ImgEnhTaskPublic],
            response_model_exclude_none=True)
async def read_tasks(offset: int = 0, limit: int = 20, session: Session = Depends(get_session)):
    task_service = ImgEnhTaskService(session)
    tasks = task_service.get_img_enh_tasks(offset, limit)
    task_publics = []
    for task in tasks:
        task_public = ImgEnhTaskPublic.model_validate(task)
        if os.path.exists(task.result_path):
            task_public.inference_result_url = generate_download_url(task.result_path)
        task_publics.append(task_public)
    return task_publics

@router.post("/image-enhance/tasks", tags=["image-enhance"], 
            response_model=ImgEnhTaskPublic,
            status_code=status.HTTP_201_CREATED, 
            response_model_exclude_none=True)
async def create_task(img_enh_task: ImgEnhTaskCreate, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    media_type = guess_media_type(img_enh_task.media_url)
    endpoint_step_types, unsupport_endpoint_types = get_endpoint_steps_and_unsupport_endpoints(img_enh_task.endpoints, media_type)
    if len(unsupport_endpoint_types) > 0:
        unsupport_endpoint_types_str = ", ".join([str(endpoint_type.value) for endpoint_type in unsupport_endpoint_types])
        raise HTTPException(status_code=400, detail=f"Input media is {media_type.value}, unsupport endpoint types: {unsupport_endpoint_types_str}")
    task_service = ImgEnhTaskService(session)
    task_db = ImgEnhTask.model_validate(img_enh_task.model_dump(exclude={"endpoints"}))
    task_db.media_type = media_type
    for ep_data in img_enh_task.endpoints:
        # 先构造 vector 和 live_publish 实例（如果有）
        vector_obj = ImgEnhVectorEndpoint.model_validate(ep_data.vector) if ep_data.vector else None
        live_publish_obj = ImgEnhLivePublishEndpoint.model_validate(ep_data.live_publish) if ep_data.live_publish else None
        endpoint = ImgEnhEndpoint(
            type=ep_data.type,
            vector=vector_obj,
            live_publish=live_publish_obj,
        )
        task_db.endpoints.append(endpoint)
    print(f"Creating image-enhance task {task_db}")
    task_db = task_service.add_img_enh_task(task_db)
    # 检查并创建嵌套文件夹
    folder_path = os.path.join(IMG_ENH_BASE_PATH, 'Real_ESRGAN' + '_' + str(task_db.id))
    if os.path.exists(folder_path):
        delete_folder(folder_path)
    ensure_folder_exists(folder_path)
    task_db.local_path = folder_path

    # download
    if media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
        task_db.steps.append(ImgEnhTaskStep.create_step(StepTypeEnum.download))
    # inference
    task_db.steps.append(ImgEnhTaskStep.create_step(StepTypeEnum.inference))
    # endpoint
    for endpoint_step_type in endpoint_step_types:
        task_db.steps.append(ImgEnhTaskStep.create_step(endpoint_step_type))
    # notify
    task_db.steps.append(ImgEnhTaskStep.create_step(StepTypeEnum.notify))
    # task_db.frame_folder_path = os.path.join(task_db.local_path, 'frames')
    # task_db.result_path = os.path.join(task_db.local_path, 'result.txt')
    task_db = task_service.add_img_enh_task(task_db)
    process_pool.submit_task(run_start_infer_task, IMG_ENH_TASK_TYPE, task_db.id)

    return task_db

@router.delete("/image-enhance/tasks/{task_id}", tags=["image-enhance"], 
               response_model=ImgEnhTaskPublic,
               response_model_exclude_none=True)
async def delete_task(task_id: int, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    task_service = ImgEnhTaskService(session)
    # 使用进程池服务取消任务
    process_pool.cancel_task(IMG_ENH_TASK_TYPE, task_id)
    task_db = task_service.get_img_enh_task(task_id)
    if task_db:
        task_public = ImgEnhTaskPublic.model_validate(task_db).model_copy(deep=True)
        if os.path.exists(task_db.result_path):
            task_public.inference_result_url = generate_download_url(task_db.result_path)
        task_service.delete_img_enh_task(task_id)
        delete_folder(task_db.local_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@contextmanager
def task_step(task_service: ImgEnhTaskService, task_id, step):
    try:
        task_service.update_img_enh_task_step_status(task_id, step, StepStatusEnum.in_progress)
        yield
        task_service.update_img_enh_task_step_status(task_id, step, StepStatusEnum.success)
    except Exception:
        task_service.update_img_enh_task_step_status(task_id, step, StepStatusEnum.failed)
        raise

def run_start_infer_task(task_type: str, task_id: int, cancel_flags: dict[int, bool]):
    try:
        with get_session_need_close() as session:
            task_service = ImgEnhTaskService(session)
            task = task_service.get_img_enh_task(task_id)
            # download
            if task.get_step_id_by_type(StepTypeEnum.download):
                with task_step(task_service, task_id, StepTypeEnum.download):
                    media_path = asyncio.run(download_file_async(task.media_url, task.local_path))
                    if not media_path:
                        raise Exception("Download failed")
            # inference
            with task_step(task_service, task_id, StepTypeEnum.inference):
                model_path = "model_zoo/RealESRGAN_x2.onnx"
                if task.media_type == MediaTypeEnum.IMAGE:
                    folder_path, file_name = os.path.split(media_path)
                    output_file_path = os.path.join(folder_path, 'image_enhance_' + file_name)
                    _, infer_result_path = run_img_enh_image_inference(model_path, media_path, output_file_path)
                elif task.media_type == MediaTypeEnum.VIDEO:
                    folder_path, file_name = os.path.split(media_path)
                    output_file_path = os.path.join(folder_path, 'video_enhance_' + file_name)
                    infer_result_path = run_img_enh_video_inference(model_path, media_path, output_file_path)
                elif task.media_type == MediaTypeEnum.STREAM:
                    target_rtmp_url = task.get_endpoint_by_type(EndpointTypeEnum.live_publish).live_publish.target_url
                    if target_rtmp_url:
                        with task_step(task_service, task_id, StepTypeEnum.live_publish):
                            infer_result_path, publisher = run_live_reference(model_path, task.media_url, target_rtmp_url)
                            if not infer_result_path:
                                raise Exception("Live inference failed")
                if not infer_result_path:
                    raise Exception("Inference failed")
                task.result_path = infer_result_path
                task_service.add_img_enh_task(task)
            # handle live cancel
            if publisher:
                # live notify
                notify_task_done(task_id)
                while publisher.is_running():
                    if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                        publisher.stop()
                        break
                    time.sleep(1)
                # exception stop running
                if not ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                    task_service.update_img_enh_task_step_status(task_id, StepTypeEnum.live_publish, StepStatusEnum.failed)
    except Exception as e:
        print(f"run_start_infer_task error: {e}")
    finally:
        # 一定会被调用（无论失败、成功、提前 return、异常）
        print(f"[{task_id}] Task finished, notifying")
        # 直播任务被删除后也会通知，但此时已无法查到对应task，故通知会失效
        notify_task_done(task_id)

def notify_task_done(task_id: int):
    with get_session_need_close() as session:
        task_service = ImgEnhTaskService(session)
        task = task_service.get_img_enh_task(task_id)

        web_hook_url = task.web_hook or settings.web_hooks.on_face_detection_done
        try:
            with task_step(task_service, task_id, StepTypeEnum.notify):
                task_public = ImgEnhTaskPublic.model_validate(task)
                task_public.inference_result_url = generate_download_url(task.result_path)
                live_publish_endpoint = task.get_endpoint_by_type(EndpointTypeEnum.live_publish)
                if live_publish_endpoint:
                    task_public.inference_result_url = live_publish_endpoint.live_publish.target_url
                result = asyncio.run(hook_notify_backoff(web_hook_url, task_public.model_dump(exclude_none=True)))
                if not result:
                    raise Exception("Notify failed")
        except Exception as e:
            print(f"Notify error: {e}")

def get_endpoint_steps_and_unsupport_endpoints(endpoints: List[EndpointBase], media_type: MediaTypeEnum) \
    -> Tuple[List[StepTypeEnum], List[EndpointTypeEnum]]:
    endpoint_step_types = []
    unsupport_endpoint_types = []
    for endpoint in endpoints:
        if media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
            # unsupport vector
            pass
            # if endpoint.type == EndpointTypeEnum.vector:
            #     endpoint_step_types.append(StepTypeEnum.vector)
            #     continue
        elif media_type in [MediaTypeEnum.STREAM]:
            if endpoint.type == EndpointTypeEnum.live_publish:
                endpoint_step_types.append(StepTypeEnum.live_publish)
                continue
        unsupport_endpoint_types.append(endpoint.type)

    return endpoint_step_types, unsupport_endpoint_types