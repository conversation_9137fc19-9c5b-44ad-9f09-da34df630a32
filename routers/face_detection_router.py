

import asyncio
from concurrent.futures import Future, ProcessPoolExecutor
from contextlib import contextmanager
import json
from multiprocessing import Manager
import os
import time
from typing import Any, List, Tuple
import cv2
from fastapi import APIRouter, Depends, HTTPException, Request, status
import numpy as np
from sqlmodel import Session

from config import SERVER_MEDIAS_BASE_PATH, settings
from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.face_detection.insightface_detection_onnx import Face, InsightFaceDetONNX, build_np_face_result, run_live_reference, valid_face_result
from onnx_sessions.live_stream_publisher import LiveStreamPublisher
from onnx_sessions.object_detection.yolov5_onnx import valid_yolo_result
from orm.face_detection_orm import FacDetEndpoint, FacDetLivePublishEndpoint, FacDetTask, FacDetTaskCreate, FacDetTaskPublic, FacDetTaskStep, FacDetVectorEndpoint
from orm.media_orm import Media<PERSON>ype<PERSON>num
from orm.model_orm import ModelStep
from orm.step_rom import Step<PERSON><PERSON>us<PERSON><PERSON>, StepTypeEnum
from database.orm_database import get_session, get_session_need_close
from orm.task_orm import EndpointBase, EndpointTypeEnum
from orm.vector_task_orm import VectorCollectionEnum, VectorMedia
from routers.inference_router import run_insightface_det_inference
from routers.vector_router import get_vector_generator, insert_image_vector, run_vector
from service import model_service
from service.face_detection_service import FacDetTaskService
from service.model_service import ModelService
from serve_utils import delete_folder, download_file_async, ensure_folder_exists, extract_frames_ffmpeg, get_sorted_frame_filenames_by_index, guess_media_type, hook_notify_backoff
from service.process_pool_service import ProcessPoolService

FAC_DET_TASK_TYPE = 'face_detection'
FAC_DET_BASE_PATH = os.path.join(SERVER_MEDIAS_BASE_PATH, FAC_DET_TASK_TYPE)

router = APIRouter()

@router.get("/face-detection/tasks/{task_id}", tags=["face-detection"], 
            response_model=FacDetTaskPublic,
            response_model_exclude_none=True)
async def read_task(task_id: int, session: Session = Depends(get_session)):
    task_service = FacDetTaskService(session)
    task_db = task_service.get_fac_det_task(task_id)
    if task_db:
        task_public = FacDetTaskPublic.model_validate(task_db)
        if os.path.exists(task_db.result_path):
            task_public.load_fac_det_result(task_db.result_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@router.get("/face-detection/tasks", tags=["face-detection"], 
            response_model=list[FacDetTaskPublic],
            response_model_exclude_none=True)
async def read_tasks(offset: int = 0, limit: int = 20, session: Session = Depends(get_session)):
    task_service = FacDetTaskService(session)
    tasks = task_service.get_fac_det_tasks(offset, limit)
    task_publics = []
    for task in tasks:
        task_public = FacDetTaskPublic.model_validate(task)
        if os.path.exists(task.result_path):
            task_public.load_fac_det_result(task.result_path)
        task_publics.append(task_public)
    return task_publics

@router.post("/face-detection/tasks", tags=["face-detection"], 
            response_model=FacDetTaskPublic,
            status_code=status.HTTP_201_CREATED,
            response_model_exclude_none=True)
async def create_task(fac_det_task: FacDetTaskCreate, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    # 检查endpoints是否与输入媒体相符
    if fac_det_task.media_type == MediaTypeEnum.UNKNOWN:
        fac_det_task.media_type = guess_media_type(fac_det_task.media_url)
    endpoint_step_types, unsupport_endpoint_types = get_endpoint_steps_and_unsupport_endpoints(fac_det_task.endpoints, fac_det_task.media_type)
    if len(unsupport_endpoint_types) > 0:
        unsupport_endpoint_types_str = ", ".join([str(endpoint_type.value) for endpoint_type in unsupport_endpoint_types])
        raise HTTPException(status_code=400, detail=f"Input media is {fac_det_task.media_type.value}, unsupport endpoint types: {unsupport_endpoint_types_str}")
    task_service = FacDetTaskService(session)
    task_db = FacDetTask.model_validate(fac_det_task.model_dump(exclude={"endpoints"}))
    for ep_data in fac_det_task.endpoints:
        # 先构造 vector 和 live_publish 实例（如果有）
        vector_obj = FacDetVectorEndpoint.model_validate(ep_data.vector) if ep_data.vector else None
        live_publish_obj = FacDetLivePublishEndpoint.model_validate(ep_data.live_publish) if ep_data.live_publish else None
        endpoint = FacDetEndpoint(
            type=ep_data.type,
            vector=vector_obj,
            live_publish=live_publish_obj,
        )
        task_db.endpoints.append(endpoint)
    print(f"Creating face-detection task {task_db}")
    task_db = task_service.add_fac_det_task(task_db)
    # 检查并创建嵌套文件夹
    folder_path = os.path.join(FAC_DET_BASE_PATH, 'insightface' + '_' + str(task_db.id))
    if os.path.exists(folder_path):
        delete_folder(folder_path)
    ensure_folder_exists(folder_path)
    task_db.local_path = folder_path

    # download
    if fac_det_task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
        task_db.steps.append(FacDetTaskStep.create_step(StepTypeEnum.download))
        task_db.steps.append(FacDetTaskStep.create_step(StepTypeEnum.decode))
    # inference
    task_db.steps.append(FacDetTaskStep.create_step(StepTypeEnum.inference))
    # endpoint
    for endpoint_step_type in endpoint_step_types:
        task_db.steps.append(FacDetTaskStep.create_step(endpoint_step_type))
    # notify
    task_db.steps.append(FacDetTaskStep.create_step(StepTypeEnum.notify))
    task_db.frame_folder_path = os.path.join(task_db.local_path, 'frames')
    task_db.result_path = os.path.join(task_db.local_path, 'result.txt')
    task_db = task_service.add_fac_det_task(task_db)
    
    # 使用ProcessPoolService提交任务
    process_pool.submit_task(run_start_infer_task, FAC_DET_TASK_TYPE, task_db.id)
    
    return task_db

@router.delete("/face-detection/tasks/{task_id}", tags=["face-detection"], 
               response_model=FacDetTaskPublic,
               response_model_exclude_none=True)
async def delete_task(task_id: int, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    task_service = FacDetTaskService(session)
    
    # 标记任务为取消状态
    process_pool.cancel_task(FAC_DET_TASK_TYPE, task_id)
    
    task_db = task_service.get_fac_det_task(task_id)
    if task_db:
        task_public = FacDetTaskPublic.model_validate(task_db).model_copy(deep=True)
        if os.path.exists(task_db.result_path):
            task_public.load_fac_det_result(task_db.result_path)
        task_service.delete_fac_det_task(task_id)
        delete_folder(task_db.local_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@contextmanager
def task_step(task_service: FacDetTaskService, task_id, step):
    try:
        task_service.update_fac_det_task_step_status(task_id, step, StepStatusEnum.in_progress)
        yield
        task_service.update_fac_det_task_step_status(task_id, step, StepStatusEnum.success)
    except Exception:
        task_service.update_fac_det_task_step_status(task_id, step, StepStatusEnum.failed)
        raise

def run_start_infer_task(task_type: str, task_id: int, cancel_flags: dict[int, bool]):
    try:
        with get_session_need_close() as session:
            task_service = FacDetTaskService(session)
            task = task_service.get_fac_det_task(task_id)
            # download
            if task.get_step_id_by_type(StepTypeEnum.download):
                with task_step(task_service, task_id, StepTypeEnum.download):
                    media_path = asyncio.run(download_file_async(task.media_url, task.local_path))
                    if not media_path:
                        raise Exception("Download failed")
            # decode
            if task.get_step_id_by_type(StepTypeEnum.decode):
                with task_step(task_service, task_id, StepTypeEnum.decode):
                    result = extract_frames_ffmpeg(media_path, task.frame_folder_path)
                    if not result:
                        raise Exception("Decode failed")
            # inference
            publisher = None
            in_progress_step_type_list = []
            with task_step(task_service, task_id, StepTypeEnum.inference):
                model_path = "model_zoo/insightface_buffalo_l_det_10g.onnx"
                if task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
                    infer_result_path = run_insightface_det_inference(model_path, task.frame_folder_path, task.result_path)
                elif task.media_type == MediaTypeEnum.STREAM:
                    model = InsightFaceDetONNX(model_path)
                    publisher = LiveStreamPublisher(model, task.media_url)
                    publisher.start_capture()
                    # set infer_result_path not empty str
                    infer_result_path = task.media_url
                if not infer_result_path:
                    raise Exception("Inference failed")
                task.result_path = infer_result_path
                task_service.add_fac_det_task(task)
                # endpoints
                fac_det_collection = None
                fac_det_vector_gen = None
                vector_media = None
                for endpoint in task.endpoints:
                    if endpoint.type == EndpointTypeEnum.vector:
                        # vector
                        task_service.update_fac_det_task_step_status(task_id, StepTypeEnum.vector, StepStatusEnum.in_progress)
                        in_progress_step_type_list.append(StepTypeEnum.vector)
                        vector_endpoint = endpoint
                        fac_det_collection = VectorCollectionEnum.image_insightface
                        fac_det_vector_gen = get_vector_generator(fac_det_collection)
                        vector_media = VectorMedia(
                            media_data_id=vector_endpoint.vector.media_data_id,
                            media_name=vector_endpoint.vector.media_name,
                            media_type=vector_endpoint.vector.media_type,
                            media_url=task.media_url,
                            frame_folder_path=task.frame_folder_path,
                            local_path=task.local_path
                        )
                    elif endpoint.type == EndpointTypeEnum.live_publish:
                        # live publish
                        task_service.update_fac_det_task_step_status(task_id, StepTypeEnum.live_publish, StepStatusEnum.in_progress)
                        in_progress_step_type_list.append(StepTypeEnum.live_publish)
                        target_url = endpoint.live_publish.target_url
                        if len(target_url) > 0:
                            publisher.start_publish(target_url)
            if publisher is None:
                # image or video
                task_public = FacDetTaskPublic.model_validate(task)
                task_public.load_fac_det_result(task.result_path)
                image_list = get_sorted_frame_filenames_by_index(task.frame_folder_path)
                for image_name in image_list:
                    image_path = os.path.join(task.frame_folder_path, image_name)
                    image = cv2.imread(image_path)
                    face_result = task_public.get_face_result_by_image_name(image_name)
                    result_image = progress_frame_endpoints(task, image_name, image, face_result, 
                                                            fac_det_collection, fac_det_vector_gen, vector_media, # for vector
                                                            )
                # all steps done
                for step_type in in_progress_step_type_list:
                    task_service.update_fac_det_task_step_status(task_id, step_type, StepStatusEnum.success)
            else:
                # live stream
                def on_result_frame_callback(frame_index: int, frame_pts: int, 
                                                         output: Any, frame: np.ndarray) -> np.ndarray:
                    face_list: List[Face] = output
                    face_result = build_np_face_result(face_list)
                    image_name = f"frame_{frame_index:08d}_{frame_pts:08d}.jpg"
                    result_frame = progress_frame_endpoints(task, image_name, frame, face_result, 
                                                                        fac_det_collection, fac_det_vector_gen, vector_media, # for vector
                                                                        )
                    return result_frame
                publisher.set_result_frame_callback(on_result_frame_callback)
                # live notify
                notify_task_done(task_id)
                while publisher.is_running():
                    if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                        publisher.stop()
                        break
                    time.sleep(1)
                # exception stop running
                if not ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                    task_service.update_fac_det_task_step_status(task_id, StepTypeEnum.live_publish, StepStatusEnum.failed)
    except Exception as e:
        print(f"run_start_infer_task error: {e}")
        for step_type in in_progress_step_type_list:
            task_service.update_fac_det_task_step_status(task_id, step_type, StepStatusEnum.failed)
    finally:
        # 一定会被调用（无论失败、成功、提前 return、异常）
        print(f"[{task_id}] Task finished, notifying")
        # 直播任务被删除后也会通知，但此时已无法查到对应task，故通知会失效
        notify_task_done(task_id)

def progress_frame_endpoints(task: FacDetTask, image_name: str, image: np.ndarray, face_result: np.ndarray,
                             collection: str, vector_gen: ICannONNX, media: VectorMedia, # for vector
                            ):
    image_name = image_name.split('.')[0]  # 去掉扩展名
    parts = image_name.split('_')
    if len(parts) != 3:
        print(f"非法 image_name 格式: {image_name}")
        return image
    frame_index = int(parts[1])
    frame_pts = int(parts[2])
    if not valid_face_result(face_result):
        print(f"非法 face_result 格式: {face_result}")
        return image
    boxes = face_result[..., :4].astype(np.int32)
    kpss = face_result[..., 4:14].astype(np.int32)
    scores = face_result[..., 14]
    
    for endpoint in task.endpoints:
        if endpoint.type == EndpointTypeEnum.vector:
            # vector
            for bbox, kps, score in zip(boxes, kpss, scores):
                bbox_int_list: List[int] = bbox.tolist()
                kps = np.array(kps, dtype=np.int32).reshape(5, 2)
                kps_int_list: List[List[int]] = kps.tolist()
                image_vector_count, image_insert_count, vector_ids = insert_image_vector(collection, vector_gen, image, media,
                                                                            image_name, bbox=bbox_int_list, kps=kps_int_list,
                                                                            task_id=task.id)
                if image_vector_count != image_insert_count:
                    print('Vector insert failed')
        elif endpoint.type == EndpointTypeEnum.crowd_gathering:
            pass

    return image

def notify_task_done(task_id: int):
    with get_session_need_close() as session:
        task_service = FacDetTaskService(session)
        task = task_service.get_fac_det_task(task_id)

        web_hook_url = task.web_hook or settings.web_hooks.on_face_detection_done
        try:
            with task_step(task_service, task_id, StepTypeEnum.notify):
                task_public = FacDetTaskPublic.model_validate(task)
                task_public.load_fac_det_result(task.result_path)
                live_publish_endpoint = task.get_endpoint_by_type(EndpointTypeEnum.live_publish)
                if live_publish_endpoint:
                    task_public.inference_result_url = live_publish_endpoint.live_publish.target_url
                result = asyncio.run(hook_notify_backoff(web_hook_url, task_public.model_dump(exclude_none=True)))
                if not result:
                    raise Exception("Notify failed")
        except Exception as e:
            print(f"Notify error: {e}")

def get_endpoint_steps_and_unsupport_endpoints(endpoints: List[EndpointBase], media_type: MediaTypeEnum) \
    -> Tuple[List[StepTypeEnum], List[EndpointTypeEnum]]:
    endpoint_step_types = []
    unsupport_endpoint_types = []
    for endpoint in endpoints:
        if media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
            if endpoint.type == EndpointTypeEnum.vector:
                endpoint_step_types.append(StepTypeEnum.vector)
                continue
        elif media_type in [MediaTypeEnum.STREAM]:
            if endpoint.type == EndpointTypeEnum.live_publish:
                endpoint_step_types.append(StepTypeEnum.live_publish)
                continue
        unsupport_endpoint_types.append(endpoint.type)

    return endpoint_step_types, unsupport_endpoint_types
