

import asyncio
from concurrent.futures import Future, ProcessPoolExecutor
from contextlib import contextmanager
import json
import os
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlmodel import Session
from ultralytics import YOL<PERSON>

from config import SERVER_MEDIAS_BASE_PATH, settings
from model_train.yolov5.train import main as yolov5_train_main
from model_train.yolov5.train import parse_opt as yolov5_train_parse_opt
from model_train.yolov5.export import main as yolov5_export_main
from model_train.yolov5.export import parse_opt as yolov5_export_parse_opt
from model_train.yolov5.utils.callbacks import Callbacks
from orm.step_rom import StepStatusEnum, StepTypeEnum
from orm.train_task_rom import TrainTask, TrainTaskCreate, TrainTaskPublic, TrainTaskStep
from database.orm_database import get_session, get_session_need_close
from routers.download_router import generate_download_url
from service.process_pool_service import ProcessPoolService
from service.train_task_service import TrainTaskService
from serve_utils import delete_folder, download_file_async, ensure_folder_exists, extract_frames_ffmpeg, generate_yolo_dataset_config_file, hook_notify_backoff
from service.web_hook_notify_service import notify_async
from utils.logger import get_logger

# 添加日志记录器
logger = get_logger()

MOD_TRA_TASK_TYPE = 'model_train'
MOD_TRA_BASE_PATH = os.path.join(SERVER_MEDIAS_BASE_PATH, MOD_TRA_TASK_TYPE)

router = APIRouter()

@router.get("/model-train/tasks/{task_id}", tags=["model-train"], response_model=TrainTaskPublic)
async def read_task(task_id: int, session: Session = Depends(get_session)):
    task_service = TrainTaskService(session)
    task_db = task_service.get_train_task(task_id)
    if task_db:
        task_public = TrainTaskPublic.model_validate(task_db)
        if os.path.exists(task_db.export_model_path):
            task_public.model_url = generate_download_url(task_db.export_model_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@router.get("/model-train/tasks", tags=["model-train"], response_model=list[TrainTaskPublic])
async def read_tasks(offset: int = 0, limit: int = 20, session: Session = Depends(get_session)):
    task_service = TrainTaskService(session)
    tasks = task_service.get_train_tasks(offset, limit)
    task_publics = []
    for task in tasks:
        task_public = TrainTaskPublic.model_validate(task)
        if os.path.exists(task.export_model_path):
            task_public.model_url = generate_download_url(task.export_model_path)
        task_publics.append(task_public)
    return task_publics

@router.post("/model-train/tasks", tags=["model-train"], response_model=TrainTaskPublic,
          status_code=status.HTTP_201_CREATED)
async def create_task(train_task: TrainTaskCreate, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    task_service = TrainTaskService(session)
    task_db = TrainTask.model_validate(train_task)
    print(f"Creating model-train task {task_db}")
    task_db = task_service.add_train_task(task_db)
    # 检查并创建嵌套文件夹
    folder_path = os.path.join(MOD_TRA_BASE_PATH, task_db.model_library + '_' + task_db.dataset_id + '_' 
                               + str(task_db.id))
    if os.path.exists(folder_path):
        delete_folder(folder_path)
    ensure_folder_exists(folder_path)
    task_db.local_path = folder_path

    download_step = TrainTaskStep.create_step(StepTypeEnum.download)
    decode_step = TrainTaskStep.create_step(StepTypeEnum.decode)
    train_step = TrainTaskStep.create_step(StepTypeEnum.train)
    notify_step = TrainTaskStep.create_step(StepTypeEnum.notify)
    task_db.steps = [download_step, decode_step, train_step, notify_step]
    task_db.config_path = os.path.join(task_db.local_path, 'config.yaml')
    task_db = task_service.add_train_task(task_db)

    process_pool.submit_task(run_start_train_task, MOD_TRA_TASK_TYPE, task_db.id)

    return task_db

@router.delete("/model-train/tasks/{task_id}", tags=["model-train"], response_model=TrainTaskPublic)
async def delete_task(task_id: int, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    task_service = TrainTaskService(session)
    # 使用进程池服务取消任务
    process_pool.cancel_task(MOD_TRA_TASK_TYPE, task_id)
    task_db = task_service.get_train_task(task_id)
    if task_db:
        task_public = TrainTaskPublic.model_validate(task_db)
        task_service.delete_train_task(task_id)
        delete_folder(task_db.local_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@contextmanager
def task_step(task_service: TrainTaskService, task_id, step_type):
    """
    任务步骤上下文管理器，用于管理任务步骤的状态和进度
    
    Args:
        task_service: 训练任务服务
        task_id: 任务ID
        step_type: 步骤类型
    
    Yields:
        update_progress: 更新进度的函数
    """
    def update_progress(progress: float, message: str = ""):
        """
        更新任务步骤的进度
        
        Args:
            progress: 进度值，0.0-1.0
            message: 进度消息
        """
        task_service.update_train_task_step_progress(task_id, step_type, progress, message)
    
    try:
        task_service.update_train_task_step_status(task_id, step_type, StepStatusEnum.in_progress)
        update_progress(0.0, f"开始{step_type.value}...")
        yield update_progress
        update_progress(1.0, f"{step_type.value}完成")
        task_service.update_train_task_step_status(task_id, step_type, StepStatusEnum.success)
    except Exception as e:
        task_service.update_train_task_step_status(task_id, step_type, StepStatusEnum.failed)
        update_progress(0.0, f"{step_type.value}失败: {str(e)}")
        raise

def run_start_train_task(task_type: str, task_id: int, cancel_flags: dict[int, bool]):
    try:
        with get_session_need_close() as session:
            task_service = TrainTaskService(session)
            task = task_service.get_train_task(task_id)
            
            # 检查任务是否被取消
            if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                logger.info(f"Task {task_id} cancelled before processing")
                return
                
            # download
            with task_step(task_service, task_id, StepTypeEnum.download) as update_progress:
                update_progress(0.1, "开始下载数据集...")
                dataset_path = asyncio.run(download_file_async(
                    task.dataset_url, 
                    task.local_path, 
                    progress_callback=lambda progress, message: update_progress(progress, message))
                )
                if not dataset_path:
                    raise Exception("Download failed")
                update_progress(0.9, "下载完成，正在处理...")
                task.dataset_path = dataset_path
                task_service.add_train_task(task)
                update_progress(1.0, "下载完成")
            
            # 检查任务是否被取消
            if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                logger.info(f"Task {task_id} cancelled after download")
                return
                
            # decode
            with task_step(task_service, task_id, StepTypeEnum.decode) as update_progress:
                update_progress(0.2, "解析数据集配置...")
                config_path = generate_yolo_dataset_config_file(task.dataset_path, task.config_path)
                if not config_path:
                    raise Exception("Decode failed")
                update_progress(1.0, "数据集配置解析完成")
            
            # 检查任务是否被取消
            if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                logger.info(f"Task {task_id} cancelled after decode")
                return
                
            # train
            with task_step(task_service, task_id, StepTypeEnum.train) as update_progress:
                update_progress(0.1, "准备训练环境...")
                onnx_path = train_yolov5_with_progress(task.local_path, config_path, update_progress)
                if not onnx_path:
                    raise Exception("Train failed")
                update_progress(0.95, "训练完成，保存模型...")
                task.export_model_path = onnx_path
                task_service.add_train_task(task)
                update_progress(1.0, "模型训练和导出完成")
    except Exception as e:
        logger.error(f"run_start_train_task error: {e}", exc_info=True)
    finally:
        # 一定会被调用（无论失败、成功、提前 return、异常）
        logger.info(f"[{task_id}] Task finished, notifying")
        notify_task_done(task_id)

def notify_task_done(task_id: int):
    with get_session_need_close() as session:
        task_service = TrainTaskService(session)
        task = task_service.get_train_task(task_id)
        if not task:
            logger.info(f"Task {task_id} not found, cannot notify")
            return

        web_hook_url = task.web_hook or settings.web_hooks.on_model_train_done
        
        # 通知回调函数
        def notification_callback(success: bool):
            with get_session_need_close() as callback_session:
                callback_task_service = TrainTaskService(callback_session)
                if success:
                    callback_task_service.update_train_task_step_status(task_id, StepTypeEnum.notify, StepStatusEnum.success)
                else:
                    callback_task_service.update_train_task_step_status(task_id, StepTypeEnum.notify, StepStatusEnum.failed)
                    logger.info(f"Notify model train task {task_id} failed")
        
        try:
            # 更新任务状态为通知中
            task_service.update_train_task_step_status(task_id, StepTypeEnum.notify, StepStatusEnum.in_progress)
            
            # 准备通知数据
            task_public = TrainTaskPublic.model_validate(task)
            if os.path.exists(task.export_model_path):
                task_public.model_url = generate_download_url(task.export_model_path)
            
            # 使用异步通知服务
            notification_data = task_public.model_dump(exclude_none=True)
            notify_async(web_hook_url, notification_data, notification_callback)
            
        except Exception as e:
            logger.info(f"Prepare notification error: {e}")
            task_service.update_train_task_step_status(task_id, StepTypeEnum.notify, StepStatusEnum.failed)

# for test
def train_yolo(project_path: str, dataset_config_path: str, weights_name: str = "yolov5s.pt"):
    weights_path = os.path.join("model_zoo", weights_name)
    # model = YOLO('yolov5su.pt')
    model = YOLO(weights_path)
    model.train(
        project=project_path, 
        data=dataset_config_path, 
        weights=weights_path, 
        epochs=3
    )
    return True


def train_yolov5_with_progress(project_path: str, dataset_config_path: str, progress_callback=None, weights_name: str = "yolov5s.pt"):
    """
    使用YOLOv5训练模型，并通过回调函数报告进度
    
    Args:
        project_path: 项目路径
        dataset_config_path: 数据集配置路径
        progress_callback: 进度回调函数，接收(进度, 消息)
        weights_name: 预训练权重名称
        
    Returns:
        str: 导出的ONNX模型路径
    """
    weights_path = os.path.join("model_zoo", weights_name)
    
    # 准备训练选项
    train_opt = yolov5_train_parse_opt(True)
    train_opt.project = project_path
    train_opt.data = dataset_config_path
    train_opt.weights = weights_path
    
    # 设置较少的训练轮次用于测试
    # train_opt.epochs = 3  # 取消注释以加快测试
    
    print(f"yolov5 train_opt: {train_opt}")
    
    # 存储训练结果的字典
    train_result = {"last": None, "best": None, "epoch": None, "results": None, "current_epoch": 0, "total_epochs": train_opt.epochs}
    
    # 训练结束回调
    def on_train_end(last, best, epoch, results):
        print(f"Training ended. Last checkpoint: {last}, Best checkpoint: {best}, Epoch: {epoch}, Results: {results}")
        train_result["last"] = last
        train_result["best"] = best
        train_result["epoch"] = epoch
        train_result["results"] = results
        if progress_callback:
            progress_callback(0.9, f"训练完成，共 {epoch} 轮，准备导出模型...")
    
    # 训练轮次开始回调 - 接收所有可能的参数
    def on_train_epoch_start(*args, **kwargs):
        train_result["current_epoch"] += 1
        if progress_callback:
            current = train_result["current_epoch"]
            total = train_result["total_epochs"]
            # 训练阶段占总进度的80%（0.1-0.9）
            progress = 0.1 + 0.8 * (current - 1) / total
            progress_callback(progress, f"正在训练第 {current}/{total} 轮")
    
    # 训练轮次结束回调 - 接收所有可能的参数
    def on_train_epoch_end(*args, **kwargs):
        if progress_callback:
            current = train_result["current_epoch"]
            total = train_result["total_epochs"]
            # 训练阶段占总进度的80%（0.1-0.9）
            progress = 0.1 + 0.8 * current / total
            progress_callback(progress, f"完成第 {current}/{total} 轮训练")
    
    # 注册回调
    callbacks = Callbacks()
    callbacks.register_action("on_train_start", "serve", lambda: progress_callback(0.1, "开始训练...") if progress_callback else None)
    callbacks.register_action("on_train_epoch_start", "serve", on_train_epoch_start)
    callbacks.register_action("on_train_epoch_end", "serve", on_train_epoch_end)
    callbacks.register_action("on_train_end", "serve", on_train_end)
    
    # 开始训练
    yolov5_train_main(train_opt, callbacks=callbacks)
    print("Training completed_1.")
    
    onnx_path = None
    if train_result["best"] is not None:
        if progress_callback:
            progress_callback(0.9, "训练完成，正在导出模型...")
        
        export_opt = yolov5_export_parse_opt(True)
        export_opt.weights = str(train_result["best"])
        export_opt.include = ("torchscript", "onnx")
        print(f"yolov5 export_opt: {export_opt}")
        yolov5_export_main(export_opt)
        onnx_path = str(train_result["best"].with_suffix(".onnx"))
        
        if progress_callback:
            progress_callback(0.95, f"模型导出完成: {onnx_path}")
    
    print("Training completed_2, onnx_path:", onnx_path)
    return onnx_path
