import asyncio
from concurrent.futures import Future, ProcessPoolExecutor
from contextlib import contextmanager
import json
from multiprocessing import Manager
import os
import time
from typing import Any, Dict, List, Tuple
import cv2
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
import numpy as np
from sqlmodel import SQLModel, Session
from torch import NoneType

from config import settings, SERVER_MEDIAS_BASE_PATH
from database.milvus_database import get_milvus_client
from onnx_sessions.cann_onnx_interface import ICannONNX
from onnx_sessions.face_detection.insightface_detection_onnx import get_face_result_by_image_name, load_insightface_det_result, run_insightface_det_inference, valid_face_result
from onnx_sessions.face_detection.insightface_recognition_onnx import InsightFaceRecONNX, load_insightface_rec_result, run_insightface_rec_inference
from onnx_sessions.image_enhance.real_esrgan_onnx import RealESRGAN_ONNX, run_img_enh_image_inference, run_img_enh_video_inference
from onnx_sessions.live_stream_publisher import LiveStreamPublisher
from onnx_sessions.live_stream_publisher_2 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LiveStreamPublisher2
from onnx_sessions.object_detection.yolov11_onnx import get_yolov11_default_extend_json
from onnx_sessions.object_detection.yolov5_onnx import YOLOV5, SimpleTracker, detect_crowd_clusters, detect_roi_crossing_with_transition, draw_crowd_results, get_yolo_result_by_image_name, get_yolov5_default_extend_json, has_crossing_result, has_crowd_region, has_high_risk, load_yolo_det_result, run_live_reference, run_yolov5_obj_det_inference, valid_yolo_result
from onnx_sessions.speech_recognition.paraformer_onnx import load_speech_rec_result, run_paraformer_asr_inference
from onnx_sessions.subtitle_recognition.duguang_ocr_det_onnx import DuGuangOcrDetOnnx, run_dg_ocr_det_inference
from onnx_sessions.subtitle_recognition.duguang_ocr_reg_onnx import load_text_reg_results, process_subtitle_recognition, run_dg_ocr_reg_inference
from orm.inference_task_orm import ImgEnhFrameResult, InferCroGatEndpoint, InferEndpoint, InferFacDetRun, InferFacRecRun, InferImgEngRun, InferLivePublishEndpoint, InferObjDetRun, InferRegIntEndpoint, InferRun, InferSpeRecRun, InferSubRegRun, InferTask, InferTaskCreate, InferTaskPublic, InferTaskStep, InferVectorEndpoint, InferEndpointCreate, InferRunCreate
from orm.media_orm import FUSED_VIRTUAL_MEDIA_DATA_ID, MediaExtend
from orm.model_orm import Model, ModelLibraryEnum, ModelStep, ModelTypeEnum
from orm.step_rom import StepStatusEnum, StepTypeEnum
from database.orm_database import get_session, get_session_need_close
from orm.task_orm import EndpointBase, EndpointTypeEnum, MediaTypeEnum, DefaultModelLibraryEnum
from orm.vector_task_orm import VectorCollectionEnum, VectorMedia
from routers.download_router import generate_download_url
from onnx_sessions.face_detection.insightface_detection_onnx import InsightFaceDetONNX
from routers.models_router import get_object_detection_default_model
from routers.vector_router import get_fused_face_vectors, get_vector_generator, insert_image_vector, run_vector
from service import model_service
from service.inference_task_service import InferTaskService
from service.milvus_service import MilvusService
from service.model_service import ModelService
from serve_utils import check_file_is_image, delete_folder, download_file_async, ensure_folder_exists, extract_audio_ffmpeg, extract_frame_copy, extract_frames_ffmpeg, guess_media_type, hook_notify_backoff, np_image_to_base64, get_sorted_frame_filenames_by_index, run_async_in_background, count_frames_in_directory
from service.process_pool_service import ProcessPoolService
from service.web_hook_notify_service import notify_async
from utils.logger import get_logger

logger = get_logger()

INFER_TASK_TYPE = 'inference'
INFER_BASE_PATH = os.path.join(SERVER_MEDIAS_BASE_PATH, INFER_TASK_TYPE)

router = APIRouter()

milvus_service = MilvusService(get_milvus_client())

@router.get("/inference/tasks/{task_id}", tags=["inference"], 
            response_model=InferTaskPublic,
            response_model_exclude_none=True)
async def read_task(task_id: int, 
                    res_offset: int = 0, res_limit: int = Query(default=0, le=10000),
                    session: Session = Depends(get_session)):
    task_service = InferTaskService(session)
    task_db = task_service.get_infer_task(task_id)
    if task_db:
        task_public = validate_infer_task_public(task_db, offset=res_offset, limit=res_limit)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@router.get("/inference/tasks", tags=["inference"], 
            response_model=list[InferTaskPublic],
            response_model_exclude_none=True)
async def read_tasks(offset: int = 0, limit: int = Query(default=20, le=100), 
                     res_offset: int = 0, res_limit: int = Query(default=0, le=10000),
                     session: Session = Depends(get_session)):
    task_service = InferTaskService(session)
    tasks = task_service.get_infer_tasks(offset, limit)
    task_publics = []
    for task in tasks:
        task_public = validate_infer_task_public(task, offset=res_offset, limit=res_limit)
        task_publics.append(task_public)
    return task_publics

@router.post("/inference/tasks", tags=["inference"], 
            response_model=InferTaskPublic,
            status_code=status.HTTP_201_CREATED,
            response_model_exclude_none=True)
async def create_task(infer_task: InferTaskCreate, request: Request, session: Session = Depends(get_session)):
    process_pool: ProcessPoolService = request.app.state.process_pool
    # 检查endpoints是否与输入媒体相符
    if infer_task.media_type == MediaTypeEnum.UNKNOWN:
        infer_task.media_type = guess_media_type(infer_task.media_url)
    run_step_types, endpoint_step_types, unsupport_endpoint_types = get_support_steps_and_unsupport_endpoints(
        infer_task.media_type,
        infer_task.runs,
        infer_task.endpoints
    )
    if len(unsupport_endpoint_types) > 0:
        unsupport_endpoint_types_str = ", ".join([str(endpoint_type.value) for endpoint_type in unsupport_endpoint_types])
        raise HTTPException(status_code=400, detail=f"Input media is {infer_task.media_type.value}, \
                                with runs {[run.type for run in infer_task.runs]}, \
                                unsupport endpoint types: {unsupport_endpoint_types_str}")

    task_service = InferTaskService(session)
    task_db = validate_infer_task_db(infer_task)
    logger.info(f"Creating inference task {task_db}")
    task_db = task_service.add_infer_task(task_db)
    # 检查并创建嵌套文件夹
    folder_path = os.path.join(INFER_BASE_PATH, 'inference_task_' + str(task_db.id))
    if os.path.exists(folder_path):
        delete_folder(folder_path)
    ensure_folder_exists(folder_path)
    task_db.local_path = folder_path

    # download
    if infer_task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO, MediaTypeEnum.AUDIO]:
        task_db.steps.append(InferTaskStep.create_step(StepTypeEnum.download))
        task_db.steps.append(InferTaskStep.create_step(StepTypeEnum.decode))
    # inference
    for run_step_type in run_step_types:
        task_db.steps.append(InferTaskStep.create_step(run_step_type))
    for run in task_db.runs:
        run.result_path = os.path.join(task_db.local_path, run.type.value + '_result.csv')
    # endpoint
    for endpoint_step_type in endpoint_step_types:
        task_db.steps.append(InferTaskStep.create_step(endpoint_step_type))
    # notify
    task_db.steps.append(InferTaskStep.create_step(StepTypeEnum.notify))
    task_db.frame_folder_path = os.path.join(task_db.local_path, 'frames')
    task_db.audio_folder_path = os.path.join(task_db.local_path, 'audios')
    task_db = task_service.add_infer_task(task_db)
    if task_db.id:
        process_pool.submit_task(run_start_infer_task, INFER_TASK_TYPE, task_db.id)

    return task_db

@router.delete("/inference/tasks/{task_id}", tags=["inference"], 
               response_model=InferTaskPublic,
               response_model_exclude_none=True)
async def delete_task(task_id: int, 
                      request: Request,
                      res_offset: int = 0, res_limit: int = Query(default=0, le=10000),
                      session: Session = Depends(get_session)):
    process_pool: ProcessPoolService = request.app.state.process_pool
    task_service = InferTaskService(session)
    # 使用进程池服务取消任务
    process_pool.cancel_task(INFER_TASK_TYPE, task_id)
    task_db = task_service.get_infer_task(task_id)
    if task_db:
        task_public = validate_infer_task_public(task_db, deep_copy=True, offset=res_offset, limit=res_limit)
        task_service.delete_infer_task(task_id)
        delete_folder(task_db.local_path)
        return task_public
    raise HTTPException(status_code=404, detail="Task not found")

@contextmanager
def task_step(task_service: InferTaskService, task_id, step_type, progress_callback=None):
    """
    任务步骤上下文管理器，支持进度更新
    
    :param task_service: 任务服务
    :param task_id: 任务ID
    :param step_type: 步骤类型
    :param progress_callback: 可选的进度回调函数，接收一个更新进度的函数作为参数
    """
    try:
        # 开始步骤
        task_service.update_infer_task_step_status(task_id, step_type, StepStatusEnum.in_progress)
        # 创建进度更新函数
        def update_progress(progress: float, message: str | None = None):
            task_service.update_infer_task_step_progress(task_id, step_type, progress, message or "")
        # 如果提供了回调，传入进度更新函数
        if progress_callback:
            progress_callback(update_progress)
        # 执行步骤
        yield update_progress
        # 步骤成功完成
        task_service.update_infer_task_step_status(task_id, step_type, StepStatusEnum.success, 1.0)
    except Exception as e:
        # 步骤失败
        task_service.update_infer_task_step_status(task_id, step_type, StepStatusEnum.failed, message=str(e))
        raise

def run_start_infer_task(task_type: str, task_id: int, cancel_flags: dict[int, bool]):
    try:
        in_progress_step_types = []
        with get_session_need_close() as session:
            task_service = InferTaskService(session)
            task = task_service.get_infer_task(task_id)
            # download
            if task.get_step_id_by_type(StepTypeEnum.download):
                with task_step(task_service, task_id, StepTypeEnum.download) as update_progress:
                    # 创建下载进度回调函数
                    async def download_with_progress():
                        return await download_file_async(
                            task.media_url, 
                            task.local_path,
                            progress_callback=lambda progress, message: update_progress(progress, message)
                        )
                    # 执行下载
                    update_progress(0.01, "准备下载...")
                    media_path = asyncio.run(download_with_progress())
                    if not media_path:
                        raise Exception("Download failed")
                    # 下载完成后更新进度
                    update_progress(1.0, "下载完成")
                    task.download_media_path = media_path
            # decode
            if task.get_step_id_by_type(StepTypeEnum.decode):
                with task_step(task_service, task_id, StepTypeEnum.decode) as update_progress:
                    # 初始进度更新
                    update_progress(0.01, "准备解码媒体...")
                    # 检查文件是否为图像
                    if task.media_type == MediaTypeEnum.IMAGE:
                        # 图像文件 - 直接复制到帧文件夹
                        update_progress(0.1, "处理图像文件...")
                        result = extract_frame_copy(media_path, task.frame_folder_path)
                        if result:
                            update_progress(1.0, "图像处理完成")
                        else:
                            update_progress(0, "图像处理失败")
                    elif task.media_type == MediaTypeEnum.VIDEO:
                        # 视频文件 - 使用FFmpeg提取帧
                        result = extract_frames_ffmpeg(
                            media_path, 
                            task.frame_folder_path,
                            progress_callback=lambda progress, message: update_progress(progress, message)
                        )
                        if task.get_run_by_type(ModelTypeEnum.SPEECH_RECOGNITION):
                            result = extract_audio_ffmpeg(
                                media_path, 
                                task.audio_folder_path,
                                progress_callback=lambda progress, message: update_progress(progress, message)
                            )
                    elif task.media_type == MediaTypeEnum.AUDIO:
                        # 音频文件 - 使用FFmpeg提取帧
                        result = extract_audio_ffmpeg(
                            media_path, 
                            task.audio_folder_path,
                            progress_callback=lambda progress, message: update_progress(progress, message)
                        )
                    if not result:
                        raise Exception("Decode failed")
            task_service.add_infer_task(task)
            # inference
            publisher = None
            infer_result_dict = {}
            fp_var_wrapper_dict = {}
            if task.media_type == MediaTypeEnum.STREAM:
                publisher = LiveStreamPublisher2()
            for run in task.runs:
                if run.type == ModelTypeEnum.OBJECT_DETECTION:
                    obj_ret_result = run_obj_det_inference(task_id, run, publisher)
                    infer_result_dict[run.type] = obj_ret_result
                    # prepare endpoints
                    fp_var_wrapper_dict[run.type] = FrameProgressVarWrapper()
                    prepare_task_endpoints(task_id, fp_var_wrapper_dict[run.type], run)
                elif run.type == ModelTypeEnum.FACE_DETECTION:
                    fac_det_result = run_fac_det_inference(task_id, run, publisher)
                    infer_result_dict[run.type] = fac_det_result
                    # prepare endpoints
                    fp_var_wrapper_dict[run.type] = FrameProgressVarWrapper()
                    prepare_task_endpoints(task_id, fp_var_wrapper_dict[run.type], run)
                elif run.type == ModelTypeEnum.FACE_RECOGNITION:
                    fac_rec_result = run_fac_rec_inference(task_id, run, publisher)
                    infer_result_dict[run.type] = fac_rec_result
                    # prepare endpoints
                    fp_var_wrapper_dict[run.type] = FrameProgressVarWrapper()
                    prepare_task_endpoints(task_id, fp_var_wrapper_dict[run.type], run)
                elif run.type == ModelTypeEnum.IMAGE_ENHANCE:
                    folder_path, file_name = os.path.split(task.download_media_path)
                    result_name = f"img_enh_{file_name}"
                    run.result_path = os.path.join(folder_path, result_name)
                    img_enh_result = run_img_enh_inference(task_id, run, publisher)
                    infer_result_dict[run.type] = img_enh_result
                elif run.type == ModelTypeEnum.SUBTITLE_RECOGNITION:
                    sub_rec_result = run_sub_rec_inference(task_id, run, publisher)
                    infer_result_dict[run.type] = sub_rec_result
                elif run.type == ModelTypeEnum.SPEECH_RECOGNITION:
                    spe_rec_result = run_spe_rec_inference(task_id, run, publisher)
                    infer_result_dict[run.type] = spe_rec_result
            task_service.add_infer_task(task)
            # in_progress endpoints
            in_progress_step_types = []
            for endpoint in task.endpoints:
                in_progress_step_types.append(endpoint.type.get_step_type())
            # frame process
            if publisher is None:
                if task.get_run_by_type(ModelTypeEnum.OBJECT_DETECTION) or \
                    task.get_run_by_type(ModelTypeEnum.FACE_DETECTION) or \
                    task.get_run_by_type(ModelTypeEnum.FACE_RECOGNITION) or \
                    task.get_run_by_type(ModelTypeEnum.IMAGE_ENHANCE) or \
                    task.get_run_by_type(ModelTypeEnum.SUBTITLE_RECOGNITION):
                    # image or video
                    image_list = get_sorted_frame_filenames_by_index(task.frame_folder_path)
                    total_images = len(image_list)
                    # 创建帧处理进度更新函数
                    def update_frame_progress(step_type, progress, message: str | None = None):
                        for step_id in task.get_step_ids_by_types(in_progress_step_types):
                            step = task_service.get_infer_task_step(step_id)
                            if step and step.type == step_type:
                                task_service.update_infer_task_step_progress(task_id, step_type, progress, message or "")
                    # 初始化进度消息
                    if total_images > 0:
                        for step_type in in_progress_step_types:
                            update_frame_progress(step_type, 0.1, f"开始处理 {total_images} 帧...")
                    # 处理每一帧
                    for i, image_name in enumerate(image_list):
                        # 计算当前进度 (10%-95%范围)
                        progress = 0.1 + (i / total_images) * 0.85 if total_images > 0 else 0.5
                        # 更新所有端点步骤的进度
                        for step_type in in_progress_step_types:
                            update_frame_progress(step_type, progress, f"处理第 {i+1}/{total_images} 帧 ({progress:.1%})")
                        
                        image_path = os.path.join(task.frame_folder_path, image_name)
                        image = cv2.imread(image_path)
                        for run in task.runs:
                            if run.type == ModelTypeEnum.OBJECT_DETECTION:
                                frame_results = infer_result_dict[run.type]
                                fp_var_wrapper = fp_var_wrapper_dict[run.type]
                                cur_frame_result, crop_path_list = get_yolo_result_by_image_name(image_name, frame_results)
                                result_image = process_frame_obj_det_endpoints(task, run, image_name, image, cur_frame_result, crop_path_list, 
                                                                            fp_var_wrapper)
                            elif run.type == ModelTypeEnum.FACE_DETECTION or run.type == ModelTypeEnum.FACE_RECOGNITION:
                                frame_results = infer_result_dict[run.type]
                                fp_var_wrapper = fp_var_wrapper_dict[run.type]
                                cur_frame_result, crop_path_list = get_face_result_by_image_name(image_name, frame_results)
                                result_image = progress_frame_fac_det_endpoints(task, run, image_name, image, cur_frame_result, crop_path_list, 
                                                                            fp_var_wrapper)
                            elif run.type == ModelTypeEnum.IMAGE_ENHANCE:
                                pass
                            elif run.type == ModelTypeEnum.SUBTITLE_RECOGNITION:
                                pass

                elif task.get_run_by_type(ModelTypeEnum.SPEECH_RECOGNITION):
                    pass
                
                # 完成处理，更新最终进度
                for step_type in in_progress_step_types:
                    update_frame_progress(step_type, 0.95, f"处理完成，共 {total_images} 帧")
                
                # all steps done
                for step_type in in_progress_step_types:
                    task_service.update_infer_task_step_status(task_id, step_type, StepStatusEnum.success, 1.0, "处理完成")
            else:
                # live stream
                publisher.start_capture(task.media_url)

                publish_wrapper = None
                for wrapper in fp_var_wrapper_dict.values():
                    if wrapper.live_publish.target_url:
                        publish_wrapper = wrapper
                        break
                
                if publish_wrapper and publish_wrapper.live_publish.target_url:
                    publisher.start_publish(publish_wrapper.live_publish.target_url)

                def on_result_frame_callback(frame_index: int, frame_pts: int, output: Any, frame: np.ndarray, 
                                             infer_runner: LiveInferRunner) -> np.ndarray:
                    fp_var_wrapper = fp_var_wrapper_dict.get(infer_runner.infer_run.type)
                    if not fp_var_wrapper:
                        return frame
                    
                    if infer_runner.infer_run.type == ModelTypeEnum.OBJECT_DETECTION:
                        yolo_result: np.ndarray = output
                        image_name = f"frame_{frame_index:08d}_{frame_pts:08d}.jpg"
                        result_frame = process_frame_obj_det_endpoints(task, infer_runner.infer_run, image_name, frame, yolo_result, [],
                                                                       fp_var_wrapper)
                        return result_frame
                    elif infer_runner.infer_run.type == ModelTypeEnum.FACE_DETECTION or infer_runner.infer_run.type == ModelTypeEnum.FACE_RECOGNITION:
                        face_result: np.ndarray = output
                        image_name = f"frame_{frame_index:08d}_{frame_pts:08d}.jpg"
                        result_frame = progress_frame_fac_det_endpoints(task, infer_runner.infer_run, image_name, frame, face_result, [],
                                                                       fp_var_wrapper)
                        return result_frame
                    elif infer_runner.infer_run.type == ModelTypeEnum.IMAGE_ENHANCE:
                        pass
                    elif infer_runner.infer_run.type == ModelTypeEnum.SUBTITLE_RECOGNITION:
                        pass
                    return frame
                
                publisher.set_result_frame_callback(on_result_frame_callback)
                # live notify
                notify_task_done(task_id)
                while publisher.is_running():
                    if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                        publisher.stop()
                        break
                    time.sleep(1)
                # exception stop running
                if not ProcessPoolService.is_task_cancelled_for_subprocess(task_type, task_id, cancel_flags):
                    task_service.update_infer_task_step_status(task_id, StepTypeEnum.live_publish, StepStatusEnum.failed)
    except Exception as e:
        logger.error(f"run_start_infer_task error: {e}")
        for step_type in in_progress_step_types:
            task_service.update_infer_task_step_status(task_id, step_type, StepStatusEnum.failed)
        import traceback
        print(f"[run_start_infer_task Task Error] {e}")
        traceback.print_exc()
        raise  # 必须 re-raise 以避免 silent fail
    finally:
        # 一定会被调用（无论失败、成功、提前 return、异常）
        logger.info(f"[{task_id}] Task finished, notifying")
        # 直播任务被删除后也会通知，但此时已无法查到对应task，故通知会失效
        notify_task_done(task_id)

def run_obj_det_inference(task_id: int, run: InferRun, publisher: LiveStreamPublisher2 | None):
    frame_results = []
    with get_session_need_close() as session:
        model_service = ModelService(session)
        task_service = InferTaskService(session)
        task = task_service.get_infer_task(task_id)
        model = None
        if run.object_detection:
            if run.object_detection.model_library == DefaultModelLibraryEnum.CUSTOM:
                model = model_service.get_model(run.object_detection.model_id)
            else:
                model = get_object_detection_default_model(run.object_detection.model_library)
        if model is None:
            raise Exception("model not found")
            
        with task_step(task_service, task_id, StepTypeEnum.object_detection) as update_progress:
            class_list = []
            extend_dic = json.loads(model.extend)
            if "classes" in extend_dic:
                class_list = extend_dic["classes"]
            update_progress(0.1, "加载模型中...")
            if task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
                # 运行推理，传入进度回调
                infer_result_path = run_yolov5_obj_det_inference(
                    model.model_path, 
                    class_list, 
                    task.frame_folder_path, 
                    run.result_path,
                    task.local_path,
                    progress_callback=lambda progress, message: update_progress(0.1 + progress * 0.8, message)
                )
                
                if not infer_result_path:
                    raise Exception(f"{run.type.value} failed")
                    
                update_progress(0.9, "加载结果中...")
                frame_results = load_yolo_det_result(run.result_path)
                update_progress(1.0, "推理完成")
                
            elif task.media_type == MediaTypeEnum.STREAM:
                if publisher:
                    update_progress(0.5, "准备直播推理...")
                    model = YOLOV5(model.model_path, class_list)
                    infer_runner = LiveInferRunner(model, run)
                    publisher.add_infer_runner(infer_runner)
                    update_progress(1.0, "直播推理准备完成")
            
    return frame_results

def run_fac_det_inference(task_id: int, run: InferRun, publisher: LiveStreamPublisher2 | None):
    frame_results = []
    with get_session_need_close() as session:
        task_service = InferTaskService(session)
        task = task_service.get_infer_task(task_id)
        with task_step(task_service, task.id, StepTypeEnum.face_detection) as update_progress:
            model_path = "model_zoo/insightface_buffalo_l_det_10g.onnx"
            if task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
                update_progress(0.05, "加载人脸检测模型...")
                # 运行人脸检测，传入进度回调
                infer_result_path = run_insightface_det_inference(
                    model_path, 
                    task.frame_folder_path, 
                    run.result_path, 
                    task.local_path,
                    progress_callback=lambda progress, message: update_progress(0.05 + progress * 0.85, message)
                )
                if not infer_result_path:
                    raise Exception(f"{run.type.value} failed")
                
                update_progress(0.9, "加载结果中...")
                frame_results = load_insightface_det_result(run.result_path)
                update_progress(1.0, "人脸检测完成")
            elif task.media_type == MediaTypeEnum.STREAM:
                if publisher:
                    update_progress(0.5, "准备直播人脸检测...")
                    model = InsightFaceDetONNX(model_path)
                    infer_runner = LiveInferRunner(model, run)
                    publisher.add_infer_runner(infer_runner)
                    update_progress(1.0, "直播人脸检测准备完成")
    return frame_results

def run_fac_rec_inference(task_id: int, run: InferRun, publisher: LiveStreamPublisher2 | None):
    frame_results = []
    with get_session_need_close() as session:
        task_service = InferTaskService(session)
        task = task_service.get_infer_task(task_id)
        with task_step(task_service, task.id, StepTypeEnum.face_recognition) as update_progress:
            vectors, media_extends = [], []
            if run.face_recognition:
                vectors, media_extends = get_fused_face_vectors(run.face_recognition.recognition_target_ids)
            det_model_path = "model_zoo/insightface_buffalo_l_det_10g.onnx"
            fac_det_model = InsightFaceDetONNX(det_model_path)
            # model_path = "model_zoo/insightface_buffalo_l_w600k_r50.onnx"
            model_path = "model_zoo/insightface_glint360k_r50.onnx"
            if task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
                update_progress(0.05, "加载人脸识别模型...")
                # 运行人脸检测，传入进度回调
                infer_result_path = run_insightface_rec_inference(
                    model_path, 
                    task.frame_folder_path, 
                    run.result_path, 
                    task.local_path,
                    fac_det_model, 
                    vectors, 
                    media_extends,
                    progress_callback=lambda progress, message: update_progress(0.05 + progress * 0.85, message),
                    similarity_threshold=0.2
                )
                if not infer_result_path:
                    raise Exception(f"{run.type.value} failed")
                
                update_progress(0.9, "加载结果中...")
                frame_results = load_insightface_rec_result(run.result_path)
                update_progress(1.0, "人脸识别完成")
            elif task.media_type == MediaTypeEnum.STREAM:
                if publisher:
                    update_progress(0.5, "准备直播人脸识别...")
                    model = InsightFaceRecONNX(model_path, fac_det_model, vectors=vectors, media_extends=media_extends)
                    infer_runner = LiveInferRunner(model, run)
                    publisher.add_infer_runner(infer_runner)
                    update_progress(1.0, "直播人脸识别准备完成")
    return frame_results

def run_img_enh_inference(task_id: int, run: InferRun, publisher: LiveStreamPublisher2 | None):
    frame_results = []
    with get_session_need_close() as session:
        task_service = InferTaskService(session)
        task = task_service.get_infer_task(task_id)
        with task_step(task_service, task.id, StepTypeEnum.image_enhance) as update_progress:
            model_path = "model_zoo/RealESRGAN_x2.onnx"
            if task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
                update_progress(0.05, "加载图像增强模型...")
                # 运行图像增强，传入进度回调
                if task.media_type == MediaTypeEnum.IMAGE:
                    infer_result = run_img_enh_image_inference(
                        model_path, 
                        task.download_media_path, 
                        run.result_path, 
                        progress_callback=lambda progress, message: update_progress(0.05 + progress * 0.85, message)
                    )
                    if isinstance(infer_result, np.ndarray):
                        # 如果返回的是图像数据，则保存并获取路径
                        cv2.imwrite(run.result_path, infer_result)
                        infer_result_path = run.result_path
                    else:
                        infer_result_path = infer_result
                elif task.media_type == MediaTypeEnum.VIDEO:
                    infer_result_path = run_img_enh_video_inference(
                        model_path, 
                        task.download_media_path, 
                        run.result_path, 
                        progress_callback=lambda progress, message: update_progress(0.05 + progress * 0.85, message)
                    )
                if not infer_result_path:
                    raise Exception(f"{run.type.value} failed")

                update_progress(1.0, "图像增强完成")
            elif task.media_type == MediaTypeEnum.STREAM:
                if publisher:
                    update_progress(0.5, "准备直播图像增强...")
                    model = RealESRGAN_ONNX(model_path)
                    infer_runner = LiveInferRunner(model, run)
                    publisher.add_infer_runner(infer_runner)
                    update_progress(1.0, "直播图像增强准备完成")
    return frame_results

def run_sub_rec_inference(task_id: int, run: InferRun, publisher: LiveStreamPublisher2 | None):
    frame_results = []
    with get_session_need_close() as session:
        task_service = InferTaskService(session)
        task = task_service.get_infer_task(task_id)
        with task_step(task_service, task.id, StepTypeEnum.subtitle_recognition) as update_progress:
            if task.media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
                update_progress(0.05, "加载OCR检测模型...")
                dg_det_model_path = "model_zoo/duguang_ocr_v2/detection/duguang_ocr_v2_detection_model_1024x1024.onnx"
                dg_reg_modle_floder = "model_zoo/duguang_ocr_v2/recognition"
                ocr_det_model = DuGuangOcrDetOnnx(dg_det_model_path)
                # 运行OCR识别，传入进度回调
                infer_result_path = run_dg_ocr_reg_inference(
                    dg_reg_modle_floder, 
                    task.frame_folder_path, 
                    run.result_path, 
                    ocr_det_model,
                    progress_callback=lambda progress, message: update_progress(0.05 + progress * 0.85, message)
                )
                if not infer_result_path:
                    raise Exception(f"{run.type.value} failed")
                
                update_progress(0.9, "处理识别结果...")
                if run.subtitle_recognition and run.subtitle_recognition.roi_bbox:
                    frame_results = process_subtitle_recognition(infer_result_path, run.subtitle_recognition.roi_bbox)
                else:
                    frame_results = process_subtitle_recognition(infer_result_path)
                update_progress(1.0, "字幕识别完成")
            elif task.media_type == MediaTypeEnum.STREAM:
                update_progress(0.5, "准备直播字幕识别...")
                # 这里可以添加直播字幕识别的代码
                update_progress(1.0, "直播字幕识别准备完成")
    return frame_results

def run_spe_rec_inference(task_id: int, run: InferRun, publisher: LiveStreamPublisher2 | None):
    frame_results = []
    with get_session_need_close() as session:
        task_service = InferTaskService(session)
        task = task_service.get_infer_task(task_id)
        with task_step(task_service, task.id, StepTypeEnum.speech_recognition) as update_progress:
            if task.media_type in [MediaTypeEnum.AUDIO, MediaTypeEnum.VIDEO]:
                update_progress(0.05, "加载语音识别模型...")
                paraformer_model_dir = "model_zoo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-onnx"
                audio_file_path = os.path.join(task.audio_folder_path, "audio.wav")
                # 运行语音识别，传入进度回调
                infer_result_path = run_paraformer_asr_inference(
                    paraformer_model_dir,
                    audio_file_path,
                    run.result_path,
                    progress_callback=lambda progress, message: update_progress(0.05 + progress * 0.85, message)
                )
                if not infer_result_path:
                    raise Exception(f"{run.type.value} failed")
                
                update_progress(0.9, "处理识别结果...")
                frame_results = load_speech_rec_result(infer_result_path)
                update_progress(1.0, "语音识别完成")
            elif task.media_type == MediaTypeEnum.STREAM:
                update_progress(0.5, "准备直播语音识别...")
                # 这里可以添加直播语音识别的代码
                update_progress(1.0, "直播语音识别准备完成")
    return frame_results
                        
class FrameProgressVarWrapper:
    class LivePublish:
        target_url: str = ''
    class Vector:
        collection: str = ''
        vector_gen: ICannONNX | None = None
        media: VectorMedia | None = None
    class CrowdGathering:
        last_alert_frame_index: int = 0
    class RegionalIntrusion:
        last_alert_frame_index: int = 0
        trackers: Dict[int, SimpleTracker] = {}
        last_target_centers: Dict[int, List[List[int]]] = {}
    
    live_publish: LivePublish = LivePublish()
    vector: Vector = Vector()
    crowd_gathering: CrowdGathering = CrowdGathering()
    regional_intrusion: RegionalIntrusion = RegionalIntrusion()

def prepare_task_endpoints(task_id: int, var_wrapper: FrameProgressVarWrapper, run: InferRun):
    with get_session_need_close() as session:
        task_service = InferTaskService(session)
        task = task_service.get_infer_task(task_id)
        for endpoint in task.endpoints:
            if endpoint.type == EndpointTypeEnum.vector:
                # vector
                task_service.update_infer_task_step_status(task_id, StepTypeEnum.vector, StepStatusEnum.in_progress)
                vector_endpoint = endpoint
                face_fused_vectors: List[List[float]] = []
                face_fused_media_extends: List[MediaExtend] = []
                if run.type == ModelTypeEnum.OBJECT_DETECTION:
                    var_wrapper.vector.collection = VectorCollectionEnum.image_resnet50
                elif run.type == ModelTypeEnum.FACE_DETECTION:
                    var_wrapper.vector.collection = VectorCollectionEnum.image_insightface
                elif run.type == ModelTypeEnum.FACE_RECOGNITION:
                    var_wrapper.vector.collection = VectorCollectionEnum.image_insightface
                    if run.face_recognition:
                        face_fused_vectors, face_fused_media_extends = get_fused_face_vectors(run.face_recognition.recognition_target_ids)

                var_wrapper.vector.vector_gen = get_vector_generator(var_wrapper.vector.collection, 
                                                                     vectors=face_fused_vectors, 
                                                                     media_extends=face_fused_media_extends,
                                                                     similarity_threshold=0.2)
                if vector_endpoint.vector:
                    var_wrapper.vector.media = VectorMedia(
                        media_data_id=vector_endpoint.vector.media_data_id,
                        media_name=vector_endpoint.vector.media_name,
                        media_type=vector_endpoint.vector.media_type,
                        media_extend=vector_endpoint.vector.media_extend,
                        media_url=task.media_url,
                        frame_folder_path=task.frame_folder_path,
                        local_path=task.local_path
                    )
            elif endpoint.type == EndpointTypeEnum.crowd_gathering:
                # crowd gathering
                task_service.update_infer_task_step_status(task_id, StepTypeEnum.crowd_gathering, StepStatusEnum.in_progress)
            elif endpoint.type == EndpointTypeEnum.regional_intrusion:
                # regional intrusion
                task_service.update_infer_task_step_status(task_id, StepTypeEnum.regional_intrusion, StepStatusEnum.in_progress)
                if endpoint.regional_intrusion:
                    if endpoint.regional_intrusion.sensitivity < 0:
                        endpoint.regional_intrusion.sensitivity = 0
                    if endpoint.regional_intrusion.sensitivity > 1.0:
                        endpoint.regional_intrusion.sensitivity = 1.0
            elif endpoint.type == EndpointTypeEnum.live_publish:
                # live publish
                task_service.update_infer_task_step_status(task_id, StepTypeEnum.live_publish, StepStatusEnum.in_progress)
                if endpoint.live_publish:
                    var_wrapper.live_publish.target_url = endpoint.live_publish.target_url
                
        return var_wrapper

def process_frame_obj_det_endpoints(task: InferTask, run: InferRun, image_name: str, image: np.ndarray, yolo_result: np.ndarray, crop_path_list: List[str], 
                                    var_wrapper: FrameProgressVarWrapper):
    image_name = image_name.split('.')[0]  # 去掉扩展名
    parts = image_name.split('_')
    if len(parts) != 3:
        logger.info(f"非法 image_name 格式: {image_name}")
        return image
    frame_index = int(parts[1])
    frame_pts = int(parts[2])
    if not valid_yolo_result(yolo_result):
        logger.info(f"非法 yolo_result 格式: {yolo_result}")
        return image
    boxes = yolo_result[..., :4].astype(np.int32)
    scores = yolo_result[..., 4]
    classes = yolo_result[..., 5].astype(np.int32)
    
    for endpoint in task.endpoints:
        if endpoint.type == EndpointTypeEnum.vector:
            # vector
            for bbox, score, class_id, crop_path in zip(boxes, scores, classes, crop_path_list):
                bbox_int_list: List[int] = bbox.tolist()
                model_id = -1
                if run.object_detection.model_library == DefaultModelLibraryEnum.CUSTOM:
                    model_id = run.object_detection.model_id
                image_vector_count, image_insert_count, vector_ids = insert_image_vector(var_wrapper.vector.collection, var_wrapper.vector.vector_gen, 
                                                                            image, var_wrapper.vector.media,
                                                                            image_name, crop_path=crop_path, bbox=bbox_int_list, task_id=task.id, 
                                                                            model_id=model_id, score=score)
                if image_vector_count != image_insert_count:
                    logger.info('Vector insert failed')
        elif endpoint.type == EndpointTypeEnum.crowd_gathering:
            person_bboxes = []
            for bbox, score, class_id in zip(boxes, scores, classes):
                if class_id == 0:
                    # person bbox
                    person_bboxes.append(bbox)
            # crowd_risk_pairs = get_crowd_risk_pairs(person_bboxes)
            crowd_info = detect_crowd_clusters(person_bboxes, 
                                                endpoint.crowd_gathering.person_space_coefficient, 
                                                endpoint.crowd_gathering.person_count_threshold,
                                                endpoint.crowd_gathering.roi_bbox)
            # if frame_index >= 0:
            #     # test
            #     frame = cv2.imread(task.get_image_path_by_frame_index_and_pts(frame_index, frame_pts))
            #     # out_frame = draw_crowd_risk_on_image(frame, crowd_risk_pairs)
            #     out_frame = draw_crowd_results(frame, crowd_info)
            #     cv2.imwrite(task.get_image_path_by_frame_index_and_pts(frame_index, frame_pts), out_frame)
            # # if has_high_risk(crowd_risk_pairs):
            if has_crowd_region(crowd_info):
                last_alert_index = var_wrapper.crowd_gathering.last_alert_frame_index
                if last_alert_index == 0 or \
                frame_index - last_alert_index >= endpoint.crowd_gathering.risk_frame_space:
                    var_wrapper.crowd_gathering.last_alert_frame_index = frame_index
                    image_base64 = np_image_to_base64(image)
                    if image_base64 is None:
                        image_base64 = ''
                    notify_async(endpoint.crowd_gathering.web_hook, {
                        "task_id": task.id,
                        "endpoint_type": endpoint.type.value,
                        "frame_index": frame_index,
                        "frame_pts": frame_pts,
                        "result": crowd_info,
                        "frame_base64": image_base64
                    })
                # draw crowd results
                image = draw_crowd_results(image, crowd_info)
        elif endpoint.type == EndpointTypeEnum.regional_intrusion:
            target_bboxes: Dict[int, List[List[int]]] = {}
            target_class_ids = endpoint.regional_intrusion.target_class_ids
            if len(target_class_ids) == 0:
                target_class_ids = [0]
            for class_id in target_class_ids:
                target_bboxes[class_id] = []
            for bbox, score, class_id in zip(boxes, scores, classes):
                if class_id in target_class_ids:
                    # target bbox
                    target_bboxes[class_id].append(bbox)
            cross_info, last_centers = detect_roi_crossing_with_transition(
                target_bboxes=target_bboxes,
                trackers=var_wrapper.regional_intrusion.trackers,
                last_centers=var_wrapper.regional_intrusion.last_target_centers,
                roi_bbox=endpoint.regional_intrusion.roi_bbox,
                direction=endpoint.regional_intrusion.direction,
                ratio=endpoint.regional_intrusion.sensitivity,
            )
            var_wrapper.regional_intrusion.last_target_centers = last_centers
            # if frame_index >= 0:
            #     # test
            #     frame = cv2.imread(task.get_image_path_by_frame_index_and_pts(frame_index, frame_pts))
            #     # out_frame = draw_crowd_risk_on_image(frame, crowd_risk_pairs)
            #     out_frame = draw_crowd_results(frame, crowd_info)
            #     cv2.imwrite(task.get_image_path_by_frame_index_and_pts(frame_index, frame_pts), out_frame)
            # # if has_high_risk(crowd_risk_pairs):
            if has_crossing_result(cross_info):
                last_alert_index = var_wrapper.regional_intrusion.last_alert_frame_index
                if last_alert_index == 0 or \
                frame_index - last_alert_index >= endpoint.regional_intrusion.risk_frame_space:
                    var_wrapper.regional_intrusion.last_alert_frame_index = frame_index
                    image_base64 = np_image_to_base64(image)
                    if image_base64 is None:
                        image_base64 = ''
                    notify_async(endpoint.regional_intrusion.web_hook, {
                        "task_id": task.id,
                        "endpoint_type": endpoint.type.value,
                        "frame_index": frame_index,
                        "frame_pts": frame_pts,
                        "result": cross_info,
                        # "frame_base64": image_base64
                    })
                # draw crowd results
                # image = draw_crowd_results(image, crowd_info)

    return image

def progress_frame_fac_det_endpoints(task: InferTask, run: InferRun, image_name: str, image: np.ndarray, face_result: np.ndarray, crop_path_list: List[str], 
                                    var_wrapper: FrameProgressVarWrapper):
    image_name = image_name.split('.')[0]  # 去掉扩展名
    parts = image_name.split('_')
    if len(parts) != 3:
        print(f"非法 image_name 格式: {image_name}")
        return image
    frame_index = int(parts[1])
    frame_pts = int(parts[2])
    if not valid_face_result(face_result):
        print(f"非法 face_result 格式: {face_result}")
        return image
    boxes = face_result[..., :4].astype(np.int32)
    kpss = face_result[..., 4:14].astype(np.int32)
    scores = face_result[..., 14]
    
    for endpoint in task.endpoints:
        if endpoint.type == EndpointTypeEnum.vector:
            # vector
            for bbox, kps, score, crop_path in zip(boxes, kpss, scores, crop_path_list):
                bbox_int_list: List[int] = bbox.tolist()
                kps = np.array(kps, dtype=np.int32).reshape(5, 2)
                kps_int_list: List[List[int]] = kps.tolist()
                image_vector_count, image_insert_count, vector_ids = insert_image_vector(var_wrapper.vector.collection, var_wrapper.vector.vector_gen, 
                                                                            image, var_wrapper.vector.media,
                                                                            image_name, crop_path=crop_path, bbox=bbox_int_list, kps=kps_int_list,
                                                                            task_id=task.id, score=score)
                if image_vector_count != image_insert_count:
                    print('Vector insert failed')
        elif endpoint.type == EndpointTypeEnum.crowd_gathering:
            pass

    return image

def notify_task_done(task_id: int):
    with get_session_need_close() as session:
        task_service = InferTaskService(session)
        task = task_service.get_infer_task(task_id)
        if not task:
            logger.info(f"Task {task_id} not found, cannot notify")
            return

        web_hook_url = task.web_hook or settings.web_hooks.on_inference_done
        
        # 通知回调函数
        def notification_callback(success: bool):
            with get_session_need_close() as callback_session:
                callback_task_service = InferTaskService(callback_session)
                if success:
                    callback_task_service.update_infer_task_step_status(task_id, StepTypeEnum.notify, StepStatusEnum.success)
                else:
                    callback_task_service.update_infer_task_step_status(task_id, StepTypeEnum.notify, StepStatusEnum.failed)
                    logger.info(f"Notify task {task_id} failed")
        
        try:
            # 更新任务状态为通知中
            task_service.update_infer_task_step_status(task_id, StepTypeEnum.notify, StepStatusEnum.in_progress)
            
            # 使用异步通知服务
            task_public = validate_infer_task_public(task)
            notification_data = task_public.model_dump(exclude_none=True)
            notify_async(web_hook_url, notification_data, notification_callback)
            
        except Exception as e:
            logger.info(f"Prepare notification error: {e}")
            task_service.update_infer_task_step_status(task_id, StepTypeEnum.notify, StepStatusEnum.failed)

def validate_infer_task_db(task_create: InferTaskCreate):
    task_db = InferTask.model_validate(task_create.model_dump(exclude={"runs", "endpoints"}))
    for run_data in task_create.runs:
        obj_det_run = InferObjDetRun.model_validate(run_data.object_detection) if run_data.object_detection else \
            InferObjDetRun() if run_data.type == ModelTypeEnum.OBJECT_DETECTION else None
        fac_det_run = InferFacDetRun.model_validate(run_data.face_detection) if run_data.face_detection else \
            InferFacDetRun() if run_data.type == ModelTypeEnum.FACE_DETECTION else None
        fac_rec_run = InferFacRecRun.model_validate(run_data.face_recognition) if run_data.face_recognition else \
            InferFacRecRun() if run_data.type == ModelTypeEnum.FACE_RECOGNITION else None
        img_enh_run = InferImgEngRun.model_validate(run_data.image_enhance) if run_data.image_enhance else \
            InferImgEngRun() if run_data.type == ModelTypeEnum.IMAGE_ENHANCE else None
        sub_reg_run = InferSubRegRun.model_validate(run_data.subtitle_recognition) if run_data.subtitle_recognition else \
            InferSubRegRun() if run_data.type == ModelTypeEnum.SUBTITLE_RECOGNITION else None
        spe_rec_run = InferSpeRecRun.model_validate(run_data.speech_recognition) if run_data.speech_recognition else \
            InferSpeRecRun() if run_data.type == ModelTypeEnum.SPEECH_RECOGNITION else None
        run = InferRun(
            type=run_data.type,
            object_detection=obj_det_run,
            face_detection=fac_det_run,
            face_recognition=fac_rec_run,
            image_enhance=img_enh_run,
            subtitle_recognition=sub_reg_run,
            speech_recognition=spe_rec_run,
        )
        task_db.runs.append(run)
    for ep_data in task_create.endpoints:
        # 先构造 vector 和 live_publish 实例（如果有）
        vector_obj = InferVectorEndpoint.model_validate(ep_data.vector) if ep_data.vector else None
        live_publish_obj = InferLivePublishEndpoint.model_validate(ep_data.live_publish) if ep_data.live_publish else None
        cro_gat_obj = InferCroGatEndpoint.model_validate(ep_data.crowd_gathering) if ep_data.crowd_gathering else None
        reg_int_obj = InferRegIntEndpoint.model_validate(ep_data.regional_intrusion) if ep_data.regional_intrusion else None
        endpoint = InferEndpoint(
            type=ep_data.type,
            vector=vector_obj,
            live_publish=live_publish_obj,
            crowd_gathering=cro_gat_obj,
            regional_intrusion=reg_int_obj,
        )
        task_db.endpoints.append(endpoint)
    return task_db

# 准备通知数据
def validate_infer_task_public(task_db: InferTask, deep_copy: bool = False, offset: int = 0, limit: int = 0):
    if deep_copy:
        task_public = InferTaskPublic.model_validate(task_db).model_copy(deep=True)
    else:
        task_public = InferTaskPublic.model_validate(task_db)

    for run in task_db.runs:
        # 跳过推理结果加载
        if limit <= 0:
            continue

        infer_results = []

        if run.type == ModelTypeEnum.OBJECT_DETECTION:
            infer_results = load_yolo_det_result(run.result_path)
        elif run.type == ModelTypeEnum.FACE_DETECTION:
            infer_results = load_insightface_det_result(run.result_path)
        elif run.type == ModelTypeEnum.FACE_RECOGNITION:
            infer_results = load_insightface_rec_result(run.result_path)
        elif run.type == ModelTypeEnum.IMAGE_ENHANCE:
            if os.path.exists(run.result_path):
                img_enh_result = ImgEnhFrameResult(
                    download_url=generate_download_url(run.result_path)
                )
                infer_results = [img_enh_result]
        elif run.type == ModelTypeEnum.SUBTITLE_RECOGNITION:
            infer_results = process_subtitle_recognition(run.result_path, run.subtitle_recognition.roi_bbox)
        elif run.type == ModelTypeEnum.SPEECH_RECOGNITION:
            infer_results = load_speech_rec_result(run.result_path)

        # 应用 offset 和 limit 截取
        start = offset
        end = offset + limit
        infer_results = infer_results[start:end]

        task_public.set_run_infer_result(run.type, infer_results)

    return task_public
        
def get_support_steps_and_unsupport_endpoints(
        media_type: MediaTypeEnum,
        runs: List[InferRunCreate],
        endpoints: List[InferEndpointCreate]
    ) -> Tuple[List[StepTypeEnum], List[StepTypeEnum], List[EndpointTypeEnum]]:
    run_types = [run.type for run in runs]
    run_step_types = []
    endpoint_step_types = []
    unsupport_endpoint_types = []
    for run_type in run_types:
        if run_type == ModelTypeEnum.OBJECT_DETECTION:
            run_step_types.append(StepTypeEnum.object_detection)
        elif run_type == ModelTypeEnum.FACE_DETECTION:
            run_step_types.append(StepTypeEnum.face_detection)
        elif run_type == ModelTypeEnum.FACE_RECOGNITION:
            run_step_types.append(StepTypeEnum.face_recognition)
        elif run_type == ModelTypeEnum.IMAGE_ENHANCE:
            run_step_types.append(StepTypeEnum.image_enhance)
        elif run_type == ModelTypeEnum.SUBTITLE_RECOGNITION:
            run_step_types.append(StepTypeEnum.subtitle_recognition)
        elif run_type == ModelTypeEnum.SPEECH_RECOGNITION:
            run_step_types.append(StepTypeEnum.speech_recognition)
    for endpoint in endpoints:
        if any(run_type in [ModelTypeEnum.OBJECT_DETECTION, ModelTypeEnum.FACE_DETECTION, ModelTypeEnum.FACE_RECOGNITION] for run_type in run_types):
            if media_type in [MediaTypeEnum.IMAGE, MediaTypeEnum.VIDEO]:
                if endpoint.type == EndpointTypeEnum.vector:
                    endpoint_step_types.append(StepTypeEnum.vector)
                    continue
            elif media_type in [MediaTypeEnum.STREAM]:
                if endpoint.type == EndpointTypeEnum.live_publish:
                    endpoint_step_types.append(StepTypeEnum.live_publish)
                    continue
            if endpoint.type == EndpointTypeEnum.crowd_gathering:
                endpoint_step_types.append(StepTypeEnum.crowd_gathering)
                continue
            if endpoint.type == EndpointTypeEnum.regional_intrusion:
                endpoint_step_types.append(StepTypeEnum.regional_intrusion)
                continue
        if any(run_type == ModelTypeEnum.IMAGE_ENHANCE for run_type in run_types):
            pass
        if any(run_type == ModelTypeEnum.SUBTITLE_RECOGNITION for run_type in run_types):
            pass

        unsupport_endpoint_types.append(endpoint.type)

    return run_step_types, endpoint_step_types, unsupport_endpoint_types


