
from ast import mod
import asyncio
from concurrent.futures import Future, ProcessPoolExecutor
import os
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlmodel import Session
from contextlib import contextmanager

from config import SERVER_MEDIAS_BASE_PATH, settings
from onnx_sessions.object_detection.yolov11_onnx import get_yolov11_default_extend_json
from onnx_sessions.object_detection.yolov5_onnx import get_yolov5_default_extend_json
from orm.model_orm import Model, ModelCreate, ModelLibraryEnum, ModelPublic, ModelStep, ModelTypeEnum
from orm.step_rom import StepStatusEnum, StepTypeEnum
from database.orm_database import get_session, get_session_need_close
from orm.task_orm import DefaultModelLibraryEnum
from service.model_service import ModelService
from serve_utils import delete_folder, download_file_async, ensure_folder_exists, hook_notify_backoff
from service.process_pool_service import ProcessPoolService
from service.web_hook_notify_service import notify_async
from utils.logger import get_logger

# 添加日志记录器
logger = get_logger()

MODELS_TASK_TYPE = 'models'
MODELS_BASE_PATH = os.path.join(SERVER_MEDIAS_BASE_PATH, MODELS_TASK_TYPE)

router = APIRouter()

@router.get("/models", tags=["models"], response_model=list[ModelPublic])
async def read_models(offset: int = 0, limit: int = Query(default=20, le=100), session: Session = Depends(get_session)):
    model_service = ModelService(session)
    models = model_service.get_models(offset, limit)
    return models

@router.get("/models/{model_id}", tags=["models"], response_model=ModelPublic, response_model_exclude={"input_medias", "local_path"})
async def read_model(model_id: int, session: Session = Depends(get_session)):
    model_service = ModelService(session)
    model_db = model_service.get_model(model_id)
    if model_db:
        return model_db
    raise HTTPException(status_code=404, detail="Model not found")

@router.post("/models", tags=["models"], response_model=ModelPublic, response_model_exclude={"input_medias", "local_path"}, 
          status_code=status.HTTP_201_CREATED, summary="上传模型")
async def create_model(model: ModelCreate, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    model_service = ModelService(session)
    model_db = Model.model_validate(model)
    print(f"Creating 0 Model {model_db}")
    model_db = model_service.add_model(model_db)
    # 检查并创建嵌套文件夹
    folder_path = os.path.join(MODELS_BASE_PATH, model_db.library + '_' + model_db.name + '_' + str(model_db.id))
    ensure_folder_exists(folder_path)
    model_db.local_path = folder_path

    downloadStep = ModelStep.create_step(StepTypeEnum.download)
    notifyStep = ModelStep.create_step(StepTypeEnum.notify)
    model_db.steps = [downloadStep, notifyStep]
    model_db = model_service.add_model(model_db)

    process_pool.submit_task(run_download_model, MODELS_TASK_TYPE, model_db.id)

    return model_db

@router.delete("/models/{model_id}", tags=["models"], response_model=ModelPublic, response_model_exclude={"input_medias", "local_path"})
async def delete_model(model_id: int, session: Session = Depends(get_session), request: Request = None):
    process_pool: ProcessPoolService = request.app.state.process_pool
    model_service = ModelService(session)
    # 使用进程池服务取消任务
    process_pool.cancel_task(MODELS_TASK_TYPE, model_id)
    model_db = model_service.get_model(model_id)
    if model_db:
        model_public = ModelPublic.model_validate(model_db)
        model_service.delete_model(model_id)
        delete_folder(model_db.local_path)
        return model_public
    raise HTTPException(status_code=404, detail="Model not found")

@contextmanager
def model_step(model_service: ModelService, model_id, step_type):
    """
    模型步骤上下文管理器，用于管理模型步骤的状态和进度
    
    Args:
        model_service: 模型服务
        model_id: 模型ID
        step_type: 步骤类型
    
    Yields:
        update_progress: 更新进度的函数
    """
    def update_progress(progress: float, message: str = ""):
        """
        更新模型步骤的进度
        
        Args:
            progress: 进度值，0.0-1.0
            message: 进度消息
        """
        model_service.update_model_step_progress(model_id, step_type, progress, message)
    
    try:
        # 开始步骤
        model_service.update_model_step_status(model_id, step_type, StepStatusEnum.in_progress)
        yield update_progress
        # 成功完成步骤
        model_service.update_model_step_status(model_id, step_type, StepStatusEnum.success)
    except Exception as e:
        # 步骤失败
        logger.error(f"Model step {step_type} failed for model {model_id}: {e}", exc_info=True)
        model_service.update_model_step_status(model_id, step_type, StepStatusEnum.failed)
        raise

def run_download_model(task_type: str, model_id: int, cancel_flags: dict[int, bool]):
    """
    下载模型的后台任务
    
    Args:
        task_type: 任务类型
        model_id: 模型ID
        cancel_flags: 取消标志字典
    """
    try:
        with get_session_need_close() as session:
            model_service = ModelService(session)
            model = model_service.get_model(model_id)
            
            # 检查任务是否被取消
            if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, model_id, cancel_flags):
                logger.info(f"Model {model_id} download cancelled before processing")
                return
            
            # 下载模型
            with model_step(model_service, model_id, StepTypeEnum.download) as update_progress:
                update_progress(0.1, "开始下载模型...")
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # 使用进度回调下载文件
                result = loop.run_until_complete(download_file_async(
                    model.url, 
                    model.local_path,
                    progress_callback=lambda progress, message: update_progress(progress, message)
                ))
                
                if not result:
                    raise Exception("模型下载失败")
                
                model.model_path = result
                model_service.add_model(model)
                update_progress(1.0, "模型下载完成")
            
            # 检查任务是否被取消
            if ProcessPoolService.is_task_cancelled_for_subprocess(task_type, model_id, cancel_flags):
                logger.info(f"Model {model_id} download cancelled after download")
                return
    except Exception as e:
        logger.error(f"run_download_model error: {e}", exc_info=True)
    finally:
        # 一定会被调用（无论失败、成功、提前 return、异常）
        logger.info(f"[{model_id}] Model download finished, notifying")
        notify_model_done(model_id)

def notify_model_done(model_id: int):
    """
    通知模型上传完成
    
    Args:
        model_id: 模型ID
    """
    with get_session_need_close() as session:
        model_service = ModelService(session)
        model = model_service.get_model(model_id)
        if not model:
            logger.info(f"Model {model_id} not found, cannot notify")
            return

        web_hook_url = model.web_hook or settings.web_hooks.on_model_download_done
        
        # 通知回调函数
        def notification_callback(success: bool):
            with get_session_need_close() as callback_session:
                callback_model_service = ModelService(callback_session)
                if success:
                    callback_model_service.update_model_step_status(model_id, StepTypeEnum.notify, StepStatusEnum.success)
                else:
                    callback_model_service.update_model_step_status(model_id, StepTypeEnum.notify, StepStatusEnum.failed)
                    logger.info(f"Notify model {model_id} failed")
        
        try:
            # 更新任务状态为通知中
            model_service.update_model_step_status(model_id, StepTypeEnum.notify, StepStatusEnum.in_progress)
            
            # 使用异步通知服务
            model_public = ModelPublic.model_validate(model)
            notification_data = model_public.model_dump(exclude_none=True)
            notify_async(web_hook_url, notification_data, notification_callback)
            
        except Exception as e:
            logger.info(f"Prepare model notification error: {e}")
            model_service.update_model_step_status(model_id, StepTypeEnum.notify, StepStatusEnum.failed)


def get_object_detection_default_model(model_library: DefaultModelLibraryEnum):
    if model_library == DefaultModelLibraryEnum.YOLOV5S:
        yolov5_model = Model(
            id=0,
            library=ModelLibraryEnum.yolov5,
            name='yolov5s',
            type=ModelTypeEnum.OBJECT_DETECTION,
            url='',
            web_hook='',
            extend=get_yolov5_default_extend_json(),
            model_path='model_zoo/yolov5s.onnx',
            local_path='',
        )
        return yolov5_model
    elif model_library == DefaultModelLibraryEnum.YOLOV11:
        yolov11_model = Model(
            id=0,
            library=ModelLibraryEnum.yolov11,
            name='yolov11s',
            type=ModelTypeEnum.OBJECT_DETECTION,
            url='',
            web_hook='',
            extend=get_yolov11_default_extend_json(),
            model_path='model_zoo/yolov11s.onnx',
            local_path='',
        )
        return yolov11_model
    else:
        return None 
