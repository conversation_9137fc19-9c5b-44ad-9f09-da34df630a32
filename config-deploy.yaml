debug: false

# 配置为服务器可供外部访问的IP与端口，供拼接生成文件的下载地址使用
api_server:
  port: 8000
  external_ip: "127.0.0.1"
  
web_hooks:
  # 回调失败时最大重试次数
  max_try_count: 20
  # 回调失败时最大重试时间，单位秒
  max_try_time: 300
  # 通过接口创建的音频降噪推理任务完成时回调
  on_inference_done: "http://127.0.0.1:8000/api/webhook"
  # 创建上传模型任务后，完成模型下载时默认的回调地址
  on_model_download_done: "http://127.0.0.1:8000/api/webhook"
  # 创建模型训练任务后，完成模型训练时默认的回调地址
  on_model_train_done: "http://127.0.0.1:8000/api/webhook"
  # 向量化任务完成后回调
  on_vector_done: "http://127.0.0.1:8000/api/webhook"
  # 创建目标识别推理任务后，完成推理时默认的回调地址
  on_object_detection_done: "http://127.0.0.1:8000/api/webhook"
  # 创建人脸识别推理任务后，完成推理时默认的回调地址
  on_face_detection_done: "http://127.0.0.1:8000/api/webhook"
