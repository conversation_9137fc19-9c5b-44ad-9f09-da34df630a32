#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复语音识别CSV结果的脚本
- 读取有问题的CSV文件
- 应用优化算法
- 生成修复后的CSV文件
"""

import os
import sys
import csv
import argparse
from typing import List, Dict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from onnx_sessions.speech_recognition.paraformer_onnx import load_speech_rec_result
from utils.logger import get_logger

logger = get_logger()


def analyze_csv_issues(csv_file: str):
    """
    分析CSV文件中的问题
    """
    print(f"=== 分析CSV文件: {csv_file} ===")
    
    try:
        results = load_speech_rec_result(csv_file)
        print(f"总记录数: {len(results)}")
        
        # 统计问题
        issues = {
            'short_sentences': [],      # 过短句子
            'long_durations': [],       # 过长时间段
            'duplicates': [],           # 重复文本
            'time_gaps': [],            # 时间间隔异常
            'invalid_times': []         # 无效时间戳
        }
        
        # 检查各种问题
        texts_seen = {}
        for i, result in enumerate(results):
            # 过短句子
            if len(result.text) <= 2:
                issues['short_sentences'].append((i+1, result.text, result.start_time))
            
            # 过长时间段
            duration = result.end_time - result.start_time
            if duration > 10:
                issues['long_durations'].append((i+1, result.text[:30], duration))
            
            # 重复文本
            if result.text in texts_seen:
                issues['duplicates'].append((i+1, result.text, texts_seen[result.text]))
            else:
                texts_seen[result.text] = i+1
            
            # 无效时间戳
            if result.start_time < 0 or result.end_time <= result.start_time:
                issues['invalid_times'].append((i+1, result.text, result.start_time, result.end_time))
        
        # 检查时间间隔
        sorted_results = sorted(results, key=lambda x: x.start_time)
        for i in range(len(sorted_results) - 1):
            current = sorted_results[i]
            next_result = sorted_results[i + 1]
            gap = next_result.start_time - current.end_time
            if gap > 5:  # 超过5秒的间隔
                issues['time_gaps'].append((current.text[:20], next_result.text[:20], gap))
        
        # 报告问题
        print(f"\n发现的问题:")
        print(f"  过短句子 (≤2字符): {len(issues['short_sentences'])} 条")
        print(f"  过长时间段 (>10秒): {len(issues['long_durations'])} 条")
        print(f"  重复文本: {len(issues['duplicates'])} 条")
        print(f"  大时间间隔 (>5秒): {len(issues['time_gaps'])} 处")
        print(f"  无效时间戳: {len(issues['invalid_times'])} 条")
        
        # 显示示例
        if issues['short_sentences']:
            print(f"\n过短句子示例:")
            for item in issues['short_sentences'][:3]:
                print(f"    行{item[0]}: '{item[1]}' ({item[2]:.3f}s)")
        
        if issues['duplicates']:
            print(f"\n重复文本示例:")
            for item in issues['duplicates'][:3]:
                print(f"    行{item[0]}: '{item[1]}' (首次出现在行{item[2]})")
        
        if issues['long_durations']:
            print(f"\n过长时间段示例:")
            for item in issues['long_durations'][:3]:
                print(f"    行{item[0]}: '{item[1]}...' ({item[2]:.1f}秒)")
        
        return results, issues
        
    except Exception as e:
        logger.error(f"分析CSV文件失败: {e}")
        return None, None


def optimize_results(results: List, issues: Dict) -> List[Dict]:
    """
    优化识别结果
    """
    print(f"\n=== 优化识别结果 ===")
    
    optimized = []
    
    # 按时间排序
    sorted_results = sorted(results, key=lambda x: x.start_time)
    
    i = 0
    while i < len(sorted_results):
        current = sorted_results[i]
        
        # 跳过过短的句子
        if len(current.text.strip()) <= 1:
            print(f"跳过过短句子: '{current.text}'")
            i += 1
            continue
        
        # 检查是否可以与下一个句子合并
        merged_text = current.text.strip()
        merged_start = current.start_time
        merged_end = current.end_time
        
        # 尝试合并相邻的短句子
        j = i + 1
        while j < len(sorted_results):
            next_result = sorted_results[j]
            
            # 如果下一个句子很短且时间接近，尝试合并
            if (len(next_result.text.strip()) <= 5 and 
                next_result.start_time - merged_end < 2.0 and
                len(merged_text) < 30):
                
                merged_text += next_result.text.strip()
                merged_end = next_result.end_time
                print(f"合并短句: '{current.text}' + '{next_result.text}' -> '{merged_text}'")
                j += 1
            else:
                break
        
        # 添加合并后的句子
        if len(merged_text) >= 2:  # 只保留有意义的句子
            optimized.append({
                "text": merged_text,
                "start_time": merged_start,
                "end_time": merged_end,
                "duration": merged_end - merged_start
            })
        
        i = j if j > i + 1 else i + 1
    
    # 去重
    seen_texts = set()
    final_optimized = []
    for item in optimized:
        if item["text"] not in seen_texts:
            seen_texts.add(item["text"])
            final_optimized.append(item)
        else:
            print(f"去除重复: '{item['text']}'")
    
    print(f"优化完成: {len(results)} -> {len(final_optimized)} 条记录")
    return final_optimized


def save_optimized_csv(optimized_results: List[Dict], output_file: str):
    """
    保存优化后的CSV文件
    """
    print(f"\n=== 保存优化结果到: {output_file} ===")
    
    os.makedirs(os.path.dirname(output_file) if os.path.dirname(output_file) else '.', exist_ok=True)
    
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # 写入表头
        writer.writerow(['sentence_id', 'text', 'start_time', 'end_time', 'duration'])
        
        # 写入优化后的数据
        for i, item in enumerate(optimized_results):
            writer.writerow([
                i + 1,
                item["text"],
                f"{item['start_time']:.3f}",
                f"{item['end_time']:.3f}",
                f"{item['duration']:.3f}"
            ])
    
    print(f"成功保存 {len(optimized_results)} 条优化记录")


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='修复语音识别CSV结果')
    parser.add_argument('input_csv', help='输入CSV文件路径')
    parser.add_argument('-o', '--output', help='输出CSV文件路径', 
                       default=None)
    parser.add_argument('--analyze-only', action='store_true', 
                       help='仅分析问题，不生成修复文件')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input_csv):
        print(f"错误: 输入文件不存在: {args.input_csv}")
        return 1
    
    # 设置输出文件
    if not args.output:
        base_name = os.path.splitext(args.input_csv)[0]
        args.output = f"{base_name}_optimized.csv"
    
    # 分析CSV文件
    results, issues = analyze_csv_issues(args.input_csv)
    if results is None:
        return 1
    
    if args.analyze_only:
        print("\n仅分析模式，不生成修复文件")
        return 0
    
    # 优化结果
    optimized_results = optimize_results(results, issues)
    
    # 保存优化后的文件
    save_optimized_csv(optimized_results, args.output)
    
    print(f"\n修复完成！")
    print(f"原文件: {args.input_csv} ({len(results)} 条记录)")
    print(f"修复文件: {args.output} ({len(optimized_results)} 条记录)")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
