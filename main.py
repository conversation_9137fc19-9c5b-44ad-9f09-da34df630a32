from config import settings, versions
from service.process_pool_service import ProcessPoolService, mark_as_main_process
from utils.logger import init_default_logger
# 初始化日志系统
init_default_logger({
    "level": settings.log.level,
    "console": settings.log.console,
    "file": settings.log.file,
    "file_path": settings.log.file_path,
    "max_bytes": settings.log.max_bytes,
    "backup_count": settings.log.backup_count,
    "json_format": settings.log.json_format
})

from functools import partial
import uvicorn
import hashlib
import json
import os
import asyncio
from pathlib import Path
import shutil
import aiohttp
from fastapi.responses import FileResponse
import torchaudio
import torio
from fastapi import Depends, FastAPI, HTTPException, status
from pydantic import BaseModel
from typing import List, Callable
from enum import Enum
from urllib.parse import urlparse
from audio_denoiser.nsnet2_denoiser.enhance_onnx import NSnet2Enhancer
from audio_denoiser.DeepFilterNet3.deep_filter_net_onnx import DeepFilterNet3Enhancer
from audio_denoiser.FullSubNetPlus.full_sub_net_plus_onnx import FullSubNetPlusEnhancer
from database.milvus_database import create_milvus_collections
from routers import download_router, inference_router, model_train_router, models_router, vector_router
from config import settings, versions
import soundfile as sf
import backoff
from database.orm_database import create_db_and_tables, get_session, get_session_need_close
from sqlmodel import Session
from contextlib import asynccontextmanager
from utils.logger import get_logger, init_default_logger
from middleware.logging_middleware import add_logging_middleware

# 初始化日志系统
# init_default_logger({
#     "level": settings.log.level,
#     "console": settings.log.console,
#     "file": settings.log.file,
#     "file_path": settings.log.file_path,
#     "max_bytes": settings.log.max_bytes,
#     "backup_count": settings.log.backup_count,
#     "json_format": settings.log.json_format
# })
# setup_log_listener()
logger = get_logger()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 记录应用启动日志
    logger.info("Application startup")
    mark_as_main_process()
    app.state.process_pool = ProcessPoolService()
    
    # 运行数据库迁移
    # from database.orm_database import run_migrations
    # run_migrations()
    
    # 创建数据库表（如果不存在）
    create_db_and_tables()
    create_milvus_collections()
    yield
    logger.info("Application shutdown")

app = FastAPI(lifespan=lifespan)

# 添加日志中间件
add_logging_middleware(app)

app.include_router(models_router.router)
# app.include_router(denoiser_router.router)
# app.include_router(object_detection_router.router)
# app.include_router(face_detection_router.router)
app.include_router(model_train_router.router)
app.include_router(download_router.router)
app.include_router(vector_router.router)
# app.include_router(image_enhance_router.router)
app.include_router(inference_router.router)

@app.get("/", tags=["general"])
def read_root():
    git_version = versions.git_version
    if len(git_version) > 7:
        git_version = git_version[:7]
    full_version = f'{versions.app_version}-{git_version}'
    return {
        "name": "ai-model-serve",
        "version": full_version
    }

# for webhook test
@app.post("/api/webhook", tags=["general"])
def webhook(data: dict):
    print(f'on webhook:{data}')
    return {"status": "ok"}


if __name__ == "__main__":
    backends = torchaudio.list_audio_backends()
    print(f'torchaudio available backends:{torchaudio.list_audio_backends()}')
    if 'ffmpeg' in backends:
        print(f'ffmpeg version:{torio.utils.ffmpeg_utils.get_versions()}')
    else:
        print('ffmpeg not available')
    ff_versions = torio.utils.ffmpeg_utils.get_versions()
    uvicorn.run(app, host="0.0.0.0", port=settings.api_server.port)
