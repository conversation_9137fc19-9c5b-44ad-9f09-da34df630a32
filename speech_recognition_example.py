#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音识别使用示例 - 展示修复后的功能
"""

import os
import sys
import json
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from onnx_sessions.speech_recognition.paraformer_onnx import run_paraformer_asr_inference


def example_usage():
    """
    展示修复后的语音识别功能使用方法
    """
    print("语音识别功能使用示例")
    print("=" * 50)
    
    # 示例配置
    model_dir = "/path/to/paraformer/model"  # 替换为实际的模型路径
    audio_file = "example_audio.wav"         # 替换为实际的音频文件
    output_csv = "asr_results.csv"           # 输出CSV文件路径
    
    print("配置信息:")
    print(f"  模型目录: {model_dir}")
    print(f"  音频文件: {audio_file}")
    print(f"  输出文件: {output_csv}")
    
    # 进度回调函数
    def progress_callback(progress, message):
        print(f"[{progress*100:.1f}%] {message}")
    
    print("\n开始语音识别...")
    
    # 注意：这里只是示例代码，实际运行需要有效的模型和音频文件
    try:
        # result_path = run_paraformer_asr_inference(
        #     model_dir=model_dir,
        #     audio_file=audio_file,
        #     output_csv=output_csv,
        #     device_id=-1,  # 使用CPU
        #     progress_callback=progress_callback
        # )
        # 
        # if result_path:
        #     print(f"\n识别完成！结果保存至: {result_path}")
        # else:
        #     print("\n识别失败！")
        
        # 模拟输出
        print("[10.0%] 正在加载语音识别模型...")
        print("[30.0%] 正在处理音频文件...")
        print("[60.0%] 正在处理识别结果...")
        print("[80.0%] 正在保存结果到CSV文件...")
        print("[100.0%] 语音识别完成，结果保存至 asr_results.csv")
        
    except Exception as e:
        print(f"识别过程中出错: {e}")


def show_improvements():
    """
    展示修复的改进点
    """
    print("\n" + "=" * 50)
    print("修复改进点说明")
    print("=" * 50)
    
    improvements = [
        {
            "问题": "分割音频后只能识别每段音频的第一句",
            "原因": "音频分块后，每个块的识别结果没有正确合并，时间偏移计算有误",
            "解决方案": [
                "1. 修改 inference_audio 方法，使用 adjust_timestamps 为每个块的结果添加正确的时间偏移",
                "2. 改进 process_results_to_sentences 方法，根据标点符号和时间间隔智能分割句子",
                "3. 确保每个音频块的所有句子都能被正确识别和处理"
            ]
        },
        {
            "问题": "推理结果中没有包含时间戳",
            "原因": "只有在 us_peaks 不为 None 时才生成时间戳，对于不支持的模型没有时间戳",
            "解决方案": [
                "1. 在主推理方法中，为没有时间戳的结果生成基本时间戳",
                "2. 基于音频长度和文本长度估算字符级时间戳",
                "3. 在 adjust_timestamps 方法中确保所有结果都有时间戳信息"
            ]
        }
    ]
    
    for i, improvement in enumerate(improvements, 1):
        print(f"\n{i}. {improvement['问题']}")
        print(f"   原因: {improvement['原因']}")
        print("   解决方案:")
        for solution in improvement['解决方案']:
            print(f"     {solution}")


def show_key_changes():
    """
    展示关键代码修改
    """
    print("\n" + "=" * 50)
    print("关键代码修改")
    print("=" * 50)
    
    changes = [
        {
            "文件": "paraformer_onnx.py",
            "方法": "inference_audio",
            "修改": "使用 adjust_timestamps 为每个音频块添加时间偏移，直接返回所有结果"
        },
        {
            "文件": "paraformer_onnx.py", 
            "方法": "adjust_timestamps (新增)",
            "修改": "为ASR结果调整时间戳偏移，为没有时间戳的结果生成基本时间戳"
        },
        {
            "文件": "paraformer_onnx.py",
            "方法": "__call__",
            "修改": "在没有 us_peaks 时也生成基本时间戳，基于音频长度估算"
        },
        {
            "文件": "paraformer_onnx.py",
            "方法": "process_results_to_sentences",
            "修改": "改进句子分割逻辑，根据标点符号和长度智能分割"
        }
    ]
    
    for i, change in enumerate(changes, 1):
        print(f"\n{i}. {change['文件']} - {change['方法']}")
        print(f"   修改内容: {change['修改']}")


def show_usage_tips():
    """
    展示使用建议
    """
    print("\n" + "=" * 50)
    print("使用建议")
    print("=" * 50)
    
    tips = [
        "1. 音频预处理: 确保音频文件为16kHz采样率的WAV格式",
        "2. 音频长度: 支持长音频文件，会自动分块处理（30秒块，5秒重叠）",
        "3. 时间戳精度: 生成的时间戳精确到0.01秒",
        "4. 句子分割: 自动根据标点符号和静音间隔分割句子",
        "5. 输出格式: CSV文件包含句子ID、文本、开始时间、结束时间、持续时间",
        "6. 设备选择: device_id=-1使用CPU，device_id>=0使用指定GPU",
        "7. 进度监控: 使用progress_callback参数监控处理进度"
    ]
    
    for tip in tips:
        print(f"  {tip}")


def show_output_format():
    """
    展示输出格式示例
    """
    print("\n" + "=" * 50)
    print("输出格式示例")
    print("=" * 50)
    
    print("CSV文件格式:")
    print("sentence_id,text,start_time,end_time,duration")
    print("1,这是第一句话,0.000,2.350,2.350")
    print("2,这是第二句话内容比较长,2.500,5.800,3.300")
    print("3,第三句话,6.000,7.200,1.200")
    
    print("\n程序返回的数据结构:")
    example_result = [
        {
            "preds": "这是第一句话",
            "timestamp": [
                ["这", 0.0, 0.1],
                ["是", 0.1, 0.2],
                ["第", 0.2, 0.3],
                ["一", 0.3, 0.4],
                ["句", 0.4, 0.5],
                ["话", 0.5, 0.6]
            ]
        }
    ]
    
    print(json.dumps(example_result, ensure_ascii=False, indent=2))


def main():
    """
    主函数
    """
    example_usage()
    show_improvements()
    show_key_changes()
    show_usage_tips()
    show_output_format()
    
    print("\n" + "=" * 50)
    print("修复完成！现在语音识别功能应该能够:")
    print("✓ 正确识别长音频中的所有句子")
    print("✓ 为所有识别结果生成时间戳")
    print("✓ 智能分割句子")
    print("✓ 提供准确的时间定位")


if __name__ == "__main__":
    main()
