# AI Model Serve

## 调试
### 配置开发环境

#### 安装Python环境
> 参考链接：https://blog.csdn.net/wanzheng_96/article/details/106883314

1. 安装依赖
```
yum install zlib-devel bzip2-devel openssl-devel ncurses-devel sqlite-devel readline-devel tk-devel gdbm-devel db4-devel libpcap-devel xz-devel libffi-devel
```

2. 编译安装Python
```
wget https://www.python.org/ftp/python/3.10.12/Python-3.10.12.tgz

tar -zxvf Python-3.10.12.tgz

cd Python-3.10.12

./configure --enable-shared --prefix=/usr/local/python3

make -j8 && make altinstall
```

3. 建立软链接
```
# 最好不要链接到/usr/bin/python或者python3，否则影响安装其他软件，这里我用python37
ln -s /usr/local/python3/bin/python3.10 /usr/bin/python310
ln -s /usr/local/python3/bin/pip3.10 /usr/bin/pip310
```

#### 创建虚拟环境
```
# 创建名为 deploy 的虚拟环境（使用Python 3.10.12测试）
python310 -m venv deploy

# 激活虚拟环境
source deploy/bin/activate

# 安装依赖包
pip install -r requirements.txt
```

### 启动服务
    
```
python main.py

或

uvicorn main:app --host=0.0.0.0 --reload

或

nohup bash run_main.sh > output.log 2>&1 &
```

### 导出离线API文档

```
python package/export_api_docs.py
```
会在package目录生成离线api文档

### 常见问题

1. soundfile未安装
```
yum install libsndfile
```

2. ffmpeg未安装
```
yum install ffmpeg
```

3. 如果调试运行时提示及如下错误
```
File "/usr/local/data/src/ai-model-serve/deploy/lib/python3.10/site-packages/albucore/functions.py", line 8, in <module>
    import simsimd as ss
ImportError: /usr/local/data/src/ai-model-serve/deploy/lib/python3.10/site-packages/simsimd./libgomp-98df74fd.so.1.0.0: cannot allocate memory in static TLS block
```

一般为libgomp在arm机器上的一个bug，可在终端输入如下命令：
```
export LD_PRELOAD=$LD_PRELOAD:/usr/local/data/src/ai-model-serve/deploy/lib/python3.10/site-packages/simsimd./libgomp-98df74fd.so.1.0.0
```

## 打包

### 补充说明
使用onnxruntime-cann后暂时无法使用pyinstaller打包，打包后运行cann相关推理会报错，可参考如下链接：

https://github.com/microsoft/onnxruntime/issues/20617

### 自动打包

```
python package/package.py
```

### 手动生成执行文件

```
pyinstaller main.spec
```

## 数据库管理

本项目使用 SQLModel 作为 ORM 框架，并使用 Alembic 进行数据库迁移管理。

### 数据库迁移

#### 初始化数据库

首次运行项目时，数据库会自动创建并应用所有迁移：

```bash
python main.py
```

#### 创建新的迁移

当您修改了数据模型后，需要创建新的迁移脚本：

```bash
cd migrations
alembic revision --autogenerate -m "描述您的更改"
```

#### 应用迁移

将数据库更新到最新版本：

```bash
cd migrations
alembic upgrade head
```

#### 查看迁移历史

```bash
cd migrations
alembic history
```

#### 回滚迁移

```bash
cd migrations
alembic downgrade <版本号>
```

更多详细信息，请参阅 `migrations/alembic/README` 文件。
