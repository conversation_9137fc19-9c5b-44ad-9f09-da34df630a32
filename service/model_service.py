from sqlmodel import Session, select
from datetime import datetime

from orm.model_orm import Model, ModelStep
from orm.step_rom import StepTypeEnum, StepStatusEnum


class ModelService:
    def __init__(self, session: Session):
        self.session = session

    def add_model(self, model: Model) -> Model:
        self.session.add(model)
        self.session.commit()
        self.session.refresh(model)
        return model
    
    def get_model(self, model_id: int) -> Model:
        return self.session.get(Model, model_id)
    
    def get_models(self, offset: int, limit: int) -> list[Model]:
        return self.session.exec(select(Model).offset(offset).limit(limit)).all()

    def delete_model(self, model_id: int) -> Model:
        model = self.get_model(model_id)
        if model:
            self.delete_model_step_by_model_id(model_id)
            self.session.delete(model)
            self.session.commit()
            return model
        return None

    def add_model_step(self, model_step: ModelStep) -> ModelStep:
        self.session.add(model_step)
        self.session.commit()
        self.session.refresh(model_step)
        return model_step
    
    def get_model_step(self, step_id: int):
        return self.session.get(ModelStep, step_id)
    
    def delete_model_step(self, step_id: int) -> ModelStep:
        step = self.get_model_step(step_id)
        if step:
            self.session.delete(step)
            self.session.commit()
            return step
        return None
    
    def delete_model_step_by_model_id(self, model_id: int):
        model = self.get_model(model_id)
        if model:
            for step in model.steps:
                self.session.delete(step)
            self.session.commit()
    
    def update_model_step_status(self, model_id: int, step_type: StepTypeEnum, status: StepStatusEnum):
        """
        更新模型步骤的状态
        
        Args:
            model_id: 模型ID
            step_type: 步骤类型
            status: 状态
        
        Returns:
            bool: 是否成功
        """
        model = self.get_model(model_id)
        if not model:
            return False
        
        for step in model.steps:
            if step.type == step_type:
                step.update_status(status)
                step.updated_at = datetime.now()
                self.session.add(step)
                self.session.commit()
                return True
        
        return False

    def update_model_step_progress(self, model_id: int, step_type: StepTypeEnum, progress: float, message: str = ""):
        """
        更新模型步骤的进度
        
        Args:
            model_id: 模型ID
            step_type: 步骤类型
            progress: 进度值，0.0-1.0
            message: 进度消息
        
        Returns:
            bool: 是否成功
        """
        model = self.get_model(model_id)
        if not model:
            return False
        
        for step in model.steps:
            if step.type == step_type:
                step.update_progress(progress, message)
                self.session.add(step)
                self.session.commit()
                return True
        
        return False
    
    
