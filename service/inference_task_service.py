
from sqlmodel import Session, select

from orm.inference_task_orm import InferTask, InferTaskStep
from orm.step_rom import Step<PERSON>tatus<PERSON>num, StepTypeEnum


class InferTaskService:
    def __init__(self, session: Session):
        self.session = session

    def add_infer_task(self, infer_task: InferTask) -> InferTask:
        self.session.add(infer_task)
        self.session.commit()
        self.session.refresh(infer_task)
        return infer_task
    
    def get_infer_task(self, task_id: int) -> InferTask:
        return self.session.get(InferTask, task_id)
    
    def get_infer_tasks(self, offset: int, limit: int) -> list[InferTask]:
        return self.session.exec(select(InferTask).offset(offset).limit(limit)).all()
    
    def delete_infer_task(self, task_id: int) -> InferTask:
        infer_task = self.get_infer_task(task_id)
        if infer_task:
            self.delete_infer_task_all_runs(task_id)
            self.delete_infer_task_all_endpoints(task_id)
            self.delete_infer_task_all_steps(task_id)
            self.session.delete(infer_task)
            self.session.commit()
            return infer_task
        return None
    

    def add_infer_task_step(self, task_step: InferTaskStep) -> InferTaskStep:
        self.session.add(task_step)
        self.session.commit()
        self.session.refresh(task_step)
        return task_step
    
    def get_infer_task_step(self, step_id: int):
        return self.session.get(InferTaskStep, step_id)
    
    def delete_infer_task_step(self, step_id: int) -> InferTaskStep:
        step = self.get_infer_task_step(step_id)
        if step:
            self.session.delete(step)
            self.session.commit()
            return step
        return None
    
    def update_infer_task_step_status(self, task_id: int, step_type: StepTypeEnum, status: str) -> InferTaskStep:
        task = self.get_infer_task(task_id)
        if task:
            step_id = task.get_step_id_by_type(step_type)
            if step_id:
                step = self.get_infer_task_step(step_id)
                if step:
                    step.update_status(status)
                    self.session.add(step)
                    self.session.commit()
                    self.session.refresh(step)
                    return step
        return None


    def delete_infer_task_all_steps(self, task_id: int):
        task = self.get_infer_task(task_id)
        if task:
            for step in task.steps:
                self.session.delete(step)
            self.session.commit()

    def delete_infer_task_all_runs(self, task_id: int):
        task = self.get_infer_task(task_id)
        if task:
            for run in task.runs:
                self.session.delete(run)
            self.session.commit()

    def delete_infer_task_all_endpoints(self, task_id: int):
        task = self.get_infer_task(task_id)
        if task:
            for endpoint in task.endpoints:
                self.session.delete(endpoint)
            self.session.commit()

    def update_infer_task_step_status(self, task_id: int, step_type: StepTypeEnum, 
                                      status: StepStatusEnum, progress: float = None, 
                                      message: str = None):
        """
        更新推理任务步骤状态
        
        :param task_id: 任务ID
        :param step_type: 步骤类型
        :param status: 新状态
        :param progress: 可选的进度值
        :param message: 可选的状态消息
        """
        task = self.get_infer_task(task_id)
        if task:
            for step in task.steps:
                if step.type == step_type:
                    step.update_status(status, progress, message)
                    self.session.add(step)
                    self.session.commit()
                    break
        return task

    def update_infer_task_step_progress(self, task_id: int, step_type: StepTypeEnum, 
                                       progress: float, message: str = None):
        """
        仅更新推理任务步骤进度
        
        :param task_id: 任务ID
        :param step_type: 步骤类型
        :param progress: 新的进度值 (0.0-1.0)
        :param message: 可选的状态消息
        """
        task = self.get_infer_task(task_id)
        if task:
            for step in task.steps:
                if step.type == step_type:
                    step.update_progress(progress, message)
                    self.session.add(step)
                    self.session.commit()
                    break
        return task