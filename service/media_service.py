from sqlmodel import Session, select

from orm.media_orm import Media, MediaStep
from orm.step_rom import StepTypeEnum


class MediaService:
    def __init__(self, session: Session):
        self.session = session

    def add_media(self, media: Media) -> Media:
        self.session.add(media)
        self.session.commit()
        self.session.refresh(media)
        return media
    
    def get_media(self, media_id: int) -> Media:
        return self.session.get(Media, media_id)
    
    def get_medias(self, offset: int, limit: int) -> list[Media]:
        return self.session.exec(select(Media).offset(offset).limit(limit)).all()
    
    def delete_media(self, media_id: int) -> Media:
        media = self.get_media(media_id)
        if media:
            self.delete_media_step_by_model_id(media_id)
            self.session.delete(media)
            self.session.commit()
            return media
        return None
    

    def add_media_step(self, task_step: MediaStep) -> MediaStep:
        self.session.add(task_step)
        self.session.commit()
        self.session.refresh(task_step)
        return task_step
    
    def get_media_step(self, step_id: int):
        return self.session.get(MediaStep, step_id)
    
    def delete_media_step(self, step_id: int) -> MediaStep:
        step = self.get_media_step(step_id)
        if step:
            self.session.delete(step)
            self.session.commit()
            return step
        return None
    
    def delete_media_step_by_model_id(self, media_id: int):
        task = self.get_media(media_id)
        if task:
            for step in task.steps:
                self.session.delete(step)
            self.session.commit()
    
    def update_media_step_status(self, media_id: int, step_type: StepTypeEnum, status: str) -> MediaStep:
        task = self.get_media(media_id)
        if task:
            step_id = task.get_step_id_by_type(step_type)
            if step_id:
                step = self.get_media_step(step_id)
                if step:
                    step.update_status(status)
                    self.session.add(step)
                    self.session.commit()
                    self.session.refresh(step)
                    return step
        return None
    