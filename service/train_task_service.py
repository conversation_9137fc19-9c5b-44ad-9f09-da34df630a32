
from sqlmodel import Session, select

from orm.step_rom import StepTypeEnum
from orm.train_task_rom import TrainTask, TrainTaskStep


class TrainTaskService:
    def __init__(self, session: Session):
        self.session = session

    def add_train_task(self, train_task: TrainTask) -> TrainTask:
        self.session.add(train_task)
        self.session.commit()
        self.session.refresh(train_task)
        return train_task
    
    def get_train_task(self, task_id: int) -> TrainTask:
        return self.session.get(TrainTask, task_id)
    
    def get_train_tasks(self, offset: int, limit: int) -> list[TrainTask]:
        return self.session.exec(select(TrainTask).offset(offset).limit(limit)).all()
    
    def delete_train_task(self, task_id: int) -> TrainTask:
        train_task = self.get_train_task(task_id)
        if train_task:
            self.delete_train_task_step_by_model_id(task_id)
            self.session.delete(train_task)
            self.session.commit()
            return train_task
        return None
    

    def add_train_task_step(self, task_step: TrainTaskStep) -> TrainTaskStep:
        self.session.add(task_step)
        self.session.commit()
        self.session.refresh(task_step)
        return task_step
    
    def get_train_task_step(self, step_id: int):
        return self.session.get(TrainTaskStep, step_id)
    
    def delete_train_task_step(self, step_id: int) -> TrainTaskStep:
        step = self.get_train_task_step(step_id)
        if step:
            self.session.delete(step)
            self.session.commit()
            return step
        return None
    
    def delete_train_task_step_by_model_id(self, task_id: int):
        task = self.get_train_task(task_id)
        if task:
            for step in task.steps:
                self.session.delete(step)
            self.session.commit()
    
    def update_train_task_step_status(self, task_id: int, step_type: StepTypeEnum, status: str) -> TrainTaskStep:
        task = self.get_train_task(task_id)
        if task:
            step_id = task.get_step_id_by_type(step_type)
            if step_id:
                step = self.get_train_task_step(step_id)
                if step:
                    step.update_status(status)
                    self.session.add(step)
                    self.session.commit()
                    self.session.refresh(step)
                    return step
        return None

    def update_train_task_step_progress(self, task_id: int, step_type: StepTypeEnum, progress: float, message: str = ""):
        """
        更新训练任务步骤的进度
        
        Args:
            task_id: 任务ID
            step_type: 步骤类型
            progress: 进度值，0.0-1.0
            message: 进度消息
        """
        task = self.get_train_task(task_id)
        if not task:
            return
        
        for step in task.steps:
            if step.type == step_type:
                step.update_progress(progress, message)
                self.session.add(step)
                self.session.commit()
                break
