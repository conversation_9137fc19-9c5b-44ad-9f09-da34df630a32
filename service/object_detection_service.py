
from sqlmodel import Session, select

from orm.object_detection_orm import ObjDetTask, ObjDetTaskStep
from orm.step_rom import StepTypeEnum


class ObjDetTaskService:
    def __init__(self, session: Session):
        self.session = session

    def add_obj_det_task(self, infer_task: ObjDetTask) -> ObjDetTask:
        self.session.add(infer_task)
        self.session.commit()
        self.session.refresh(infer_task)
        return infer_task
    
    def get_obj_det_task(self, task_id: int) -> ObjDetTask:
        return self.session.get(ObjDetTask, task_id)
    
    def get_obj_det_tasks(self, offset: int, limit: int) -> list[ObjDetTask]:
        return self.session.exec(select(ObjDetTask).offset(offset).limit(limit)).all()
    
    def delete_obj_det_task(self, task_id: int) -> ObjDetTask:
        infer_task = self.get_obj_det_task(task_id)
        if infer_task:
            self.delete_obj_det_task_step_by_model_id(task_id)
            self.session.delete(infer_task)
            self.session.commit()
            return infer_task
        return None
    

    def add_obj_det_task_step(self, task_step: ObjDetTaskStep) -> ObjDetTaskStep:
        self.session.add(task_step)
        self.session.commit()
        self.session.refresh(task_step)
        return task_step
    
    def get_obj_det_task_step(self, step_id: int):
        return self.session.get(ObjDetTaskStep, step_id)
    
    def delete_obj_det_task_step(self, step_id: int) -> ObjDetTaskStep:
        step = self.get_obj_det_task_step(step_id)
        if step:
            self.session.delete(step)
            self.session.commit()
            return step
        return None
    
    def delete_obj_det_task_step_by_model_id(self, task_id: int):
        task = self.get_obj_det_task(task_id)
        if task:
            for step in task.steps:
                self.session.delete(step)
            self.session.commit()
    
    def update_obj_det_task_step_status(self, task_id: int, step_type: StepTypeEnum, status: str) -> ObjDetTaskStep:
        task = self.get_obj_det_task(task_id)
        if task:
            step_id = task.get_step_id_by_type(step_type)
            if step_id:
                step = self.get_obj_det_task_step(step_id)
                if step:
                    step.update_status(status)
                    self.session.add(step)
                    self.session.commit()
                    self.session.refresh(step)
                    return step
        return None