from sqlmodel import Session, select

from orm.step_rom import StepTypeEnum
from orm.vector_task_orm import VectorMedia, VectorMediaStep, VectorTask


class VectorTaskService:
    def __init__(self, session: Session):
        self.session = session

    def add_vector_task(self, vector_task: VectorTask) -> VectorTask:
        self.session.add(vector_task)
        self.session.commit()
        self.session.refresh(vector_task)
        return vector_task
    
    def get_vector_task(self, task_id: int) -> VectorTask:
        return self.session.get(VectorTask, task_id)
    
    def get_vector_tasks(self, offset: int, limit: int) -> list[VectorTask]:
        return self.session.exec(select(VectorTask).offset(offset).limit(limit)).all()
    
    def delete_vector_task(self, task_id: int) -> VectorTask:
        vector_task = self.get_vector_task(task_id)
        if vector_task:
            # self.delete_vector_vector_media_by_model_id(task_id)
            self.session.delete(vector_task)
            self.session.commit()
            return vector_task
        return None
    

    def add_vector_media(self, vector_media: VectorMedia) -> VectorMedia:
        self.session.add(vector_media)
        self.session.commit()
        self.session.refresh(vector_media)
        return vector_media
    
    def get_vector_media(self, media_id: int):
        return self.session.get(VectorMedia, media_id)
    
    def delete_vector_media(self, media_id: int) -> VectorMedia:
        media = self.get_vector_media(media_id)
        if media:
            self.session.delete(media)
            self.session.commit()
            return media
        return None
    
    def delete_vector_medias_by_task_id(self, task_id: int):
        task = self.get_vector_task(task_id)
        if task:
            for media in task.medias:
                self.session.delete(media)
            self.session.commit()

    # def add_vector_media_result(self, media_id: int, collection: str, ids: list[int]):
    #     media = self.get_vector_media(media_id)
    #     if media:
    #         for result in media.vector_result:
    #             if result.collection == collection:
    #                 result.ids.extend(ids)
    #                 self.session.add(result)
    #                 self.session.commit()
    #                 self.session.refresh(result)
    #                 return
    #         result = VectorMediaResult(vector_media_id=media_id, collection=collection, ids=ids)
    #         self.session.add(result)
    #         self.session.commit()
    #         self.session.refresh(result)

    def get_media_step(self, step_id: int):
        return self.session.get(VectorMediaStep, step_id)
    
    def update_vector_media_status(self, media_id: int, step_type: StepTypeEnum, status: str) -> VectorMedia:
        media = self.get_vector_media(media_id)
        if media:
            step_id = media.get_step_id_by_type(step_type)
            if step_id:
                step = self.get_media_step(step_id)
                if step:
                    step.update_status(status)
                    self.session.add(step)
                    self.session.commit()
                    self.session.refresh(step)
                    return step
        return None

    def update_vector_media_step_progress(self, media_id: int, step_type: StepTypeEnum, progress: float, message: str = ""):
        """
        更新媒体步骤的进度
        
        Args:
            media_id: 媒体ID
            step_type: 步骤类型
            progress: 进度值，0.0-1.0
            message: 进度消息
        
        Returns:
            bool: 是否成功
        """
        media = self.get_vector_media(media_id)
        if not media:
            return False
        
        for step in media.steps:
            if step.type == step_type:
                step.update_progress(progress, message)
                self.session.add(step)
                self.session.commit()
                return True
        
        return False
