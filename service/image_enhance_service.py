
from sqlmodel import Session, select

from orm.image_enhance_orm import ImgEnhTask, ImgEnhTaskStep
from orm.step_rom import StepTypeEnum


class ImgEnhTaskService:
    def __init__(self, session: Session):
        self.session = session

    def add_img_enh_task(self, infer_task: ImgEnhTask) -> ImgEnhTask:
        self.session.add(infer_task)
        self.session.commit()
        self.session.refresh(infer_task)
        return infer_task
    
    def get_img_enh_task(self, task_id: int) -> ImgEnhTask:
        return self.session.get(ImgEnhTask, task_id)
    
    def get_img_enh_tasks(self, offset: int, limit: int) -> list[ImgEnhTask]:
        return self.session.exec(select(ImgEnhTask).offset(offset).limit(limit)).all()
    
    def delete_img_enh_task(self, task_id: int) -> ImgEnhTask:
        infer_task = self.get_img_enh_task(task_id)
        if infer_task:
            self.delete_img_enh_task_all_steps(task_id)
            self.delete_img_enh_task_all_endpoints(task_id)
            self.session.delete(infer_task)
            self.session.commit()
            return infer_task
        return None
    

    def add_img_enh_task_step(self, task_step: ImgEnhTaskStep) -> ImgEnhTaskStep:
        self.session.add(task_step)
        self.session.commit()
        self.session.refresh(task_step)
        return task_step
    
    def get_img_enh_task_step(self, step_id: int):
        return self.session.get(ImgEnhTaskStep, step_id)
    
    def delete_img_enh_task_step(self, step_id: int) -> ImgEnhTaskStep:
        step = self.get_img_enh_task_step(step_id)
        if step:
            self.session.delete(step)
            self.session.commit()
            return step
        return None
    
    def delete_img_enh_task_all_steps(self, task_id: int):
        task = self.get_img_enh_task(task_id)
        if task:
            for step in task.steps:
                self.session.delete(step)
            self.session.commit()

    def delete_img_enh_task_all_endpoints(self, task_id: int):
        task = self.get_img_enh_task(task_id)
        if task:
            for endpoint in task.endpoints:
                if endpoint.vector:
                    self.session.delete(endpoint.vector)
                if endpoint.live_publish:
                    self.session.delete(endpoint.live_publish)
                self.session.delete(endpoint)
            self.session.commit()
    
    def update_img_enh_task_step_status(self, task_id: int, step_type: StepTypeEnum, status: str) -> ImgEnhTaskStep:
        task = self.get_img_enh_task(task_id)
        if task:
            step_id = task.get_step_id_by_type(step_type)
            if step_id:
                step = self.get_img_enh_task_step(step_id)
                if step:
                    step.update_status(status)
                    self.session.add(step)
                    self.session.commit()
                    self.session.refresh(step)
                    return step
        return None