import queue
import threading
import asyncio
import time
from typing import Dict, Any, Optional, Callable

from serve_utils import hook_notify_backoff
from utils.logger import get_logger

logger = get_logger()

class NotificationService:
    """
    异步通知服务，避免通知阻塞当前进程
    使用单例模式确保全局只有一个通知服务实例
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(NotificationService, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self._queue = queue.Queue()
        self._running = True
        self._worker_thread = threading.Thread(target=self._worker, daemon=True)
        self._worker_thread.start()
        logger.info("NotificationService initialized")
    
    def _worker(self):
        """后台工作线程，处理通知队列"""
        while self._running:
            try:
                # 从队列获取通知任务，超时1秒
                task = self._queue.get(timeout=1)
                if task is None:
                    # 收到停止信号
                    break
                    
                web_hook_url, data, callback = task
                
                # 创建新线程执行异步通知，避免一个通知阻塞其他通知
                def notify_thread():
                    try:
                        result = asyncio.run(hook_notify_backoff(web_hook_url, data))
                        if callback:
                            callback(result)
                    except Exception as e:
                        logger.info(f"Notification error: {e}")
                        if callback:
                            callback(False)
                
                threading.Thread(target=notify_thread, daemon=True).start()
                
            except queue.Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.info(f"NotificationService worker error: {e}")
                time.sleep(1)  # 避免错误循环消耗CPU
    
    def enqueue_notification(self, web_hook_url: str, data: Dict[str, Any], 
                            callback: Optional[Callable[[bool], None]] = None):
        """
        将通知任务加入队列
        
        Args:
            web_hook_url: Webhook URL
            data: 要发送的数据
            callback: 可选的回调函数，通知完成后调用，参数为是否成功
        """
        self._queue.put((web_hook_url, data, callback))
    
    def shutdown(self):
        """关闭通知服务"""
        self._running = False
        self._queue.put(None)  # 发送停止信号
        if self._worker_thread.is_alive():
            self._worker_thread.join(timeout=5)
            
    @classmethod
    def get_instance(cls):
        """获取通知服务实例"""
        if cls._instance is None:
            return cls()
        return cls._instance

def notify_async(web_hook_url: str, data: Dict[str, Any], 
                callback: Optional[Callable[[bool], None]] = None):
    """
    异步发送通知，不阻塞当前进程
    
    Args:
        web_hook_url: Webhook URL
        data: 要发送的数据
        callback: 可选的回调函数，通知完成后调用，参数为是否成功
    """
    notification_service = NotificationService.get_instance()
    notification_service.enqueue_notification(web_hook_url, data, callback)

# 在应用退出时关闭通知服务
import atexit
atexit.register(lambda: NotificationService.get_instance().shutdown())