from concurrent.futures import Future, ProcessPoolExecutor
from functools import partial, wraps
from multiprocessing import Manager
import multiprocessing
import os
import queue
import setproctitle
from typing import Dict, Any

from config import settings
from utils.logger import get_logger, set_logger


def _processpool_initializer(parent_logger):
    print("_processpool_initializer start")
    set_logger(parent_logger)
    logger = get_logger()
    logger.info("subprocess logger initialized")

    print("_processpool_initializer end")

# def _processpool_initializer():
#     print("_processpool_initializer start")
#     # set_logger(parent_logger)
#     logger = get_logger()
#     logger.info("subprocess logger initialized")

#     print("_processpool_initializer end")

def subprocess_task_wrapper(func, task_type, task_id, cancel_flags, *args, **kwargs):
    """包装子进程任务，处理日志和取消逻辑"""
    logger = get_logger()
    logger.info(f"Starting {task_type} task {task_id}, current process name: {multiprocessing.current_process().name}")
    # logger.info(f"current process title: {setproctitle.getproctitle()}")
    sub_process_name = f"{task_type}_{task_id}"
    multiprocessing.current_process().name = sub_process_name
    setproctitle.setproctitle(sub_process_name)
    try:
        # 检查任务是否被取消
        if cancel_flags.get(f"{task_type}_{task_id}", False):
            logger.info(f"{task_type} task {task_id} was cancelled before execution")
            return None

        # 执行原始函数
        result = func(task_type, task_id, cancel_flags, *args, **kwargs)
        logger.info(f"Completed {task_type} task {task_id}")
        return result

    except Exception as e:
        logger.error(f"Error in {task_type} task {task_id}: {str(e)}")
        # 重新抛出异常，让进程池处理
        raise

_is_main_process = False

def mark_as_main_process():
    """在应用启动时调用此函数来标记主进程"""
    global _is_main_process
    _is_main_process = True

class ProcessPoolService:
    """统一管理进程池和任务的服务"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            # 使用全局标记判断
            global _is_main_process
            if _is_main_process or multiprocessing.current_process().name == 'MainProcess':
                cls._instance = super(ProcessPoolService, cls).__new__(cls)
                cls._instance._initialize()
            else:
                print(f"Skipping ProcessPoolService initialization in subprocess (PID: {os.getpid()})")
                # 在子进程中返回一个简化版本
                dummy = super(ProcessPoolService, cls).__new__(cls)
                dummy.is_subprocess = True
                return dummy
        return cls._instance
    
    def _initialize(self):
        """初始化进程池和任务管理器"""
        self.is_subprocess = False
        try:
            mp_context = multiprocessing.get_context("spawn")
            logger = get_logger()
            workers = self._get_optimal_workers()
            print(f"Initializing ProcessPoolExecutor with {workers} workers")
            self.executor = ProcessPoolExecutor(
                max_workers=workers, 
                initializer=_processpool_initializer,
                initargs=(logger,),
                mp_context=mp_context,
            )
            self.manager = Manager()
            self.cancel_flags = self.manager.dict()
            self.tasks: Dict[str, Dict[int, Future]] = {}
            print("ProcessPoolExecutor initialized successfully")
        except Exception as e:
            print(f"Error initializing ProcessPoolExecutor: {e}")
            import traceback
            traceback.print_exc()
            # 使用默认值作为备选
            self.executor = ProcessPoolExecutor(
                max_workers=2,
                initializer=_processpool_initializer,
                initargs=(logger,),
                mp_context=mp_context,
            )
            self.manager = Manager()
            self.cancel_flags = self.manager.dict()
            self.tasks = {}
    
    def _get_optimal_workers(self):
        """根据配置计算最优的工作进程数"""
        if settings.process_pool.use_cpu_count:
            # 使用CPU核心数，但至少留一个核心给其他任务
            return max(1, multiprocessing.cpu_count() - 1)
        else:
            # 使用配置的值
            return settings.process_pool.max_workers
    
    def get_executor(self):
        """获取进程池执行器"""
        if hasattr(self, 'is_subprocess') and self.is_subprocess:
            print("Warning: Attempting to access executor from subprocess")
            return None
        return self.executor
    
    def submit_task(self, func, task_type: str, task_id: int, *args, **kwargs):
        """提交任务到进程池，支持传递参数给任务函数"""
        if hasattr(self, 'is_subprocess') and self.is_subprocess:
            print(f"Warning: Cannot submit task {task_type}_{task_id} from subprocess")
            return None
        
        # 确保任务类型存在
        if task_type not in self.tasks:
            self.tasks[task_type] = {}
        
        # 设置取消标志
        self.cancel_flags[f"{task_type}_{task_id}"] = False
        
        # 添加调试信息
        print(f"Submitting task: {task_type}_{task_id}")
        print(f"Function to execute: {func.__name__}")
        
        # 直接提交，添加异常处理
        try:
            future = self.executor.submit(
                subprocess_task_wrapper,
                func, task_type, task_id, self.cancel_flags,
                *args, **kwargs
            )
            print(f"Task submitted successfully: {future}")
        except Exception as e:
            print(f"Error submitting task: {e}")
            import traceback
            traceback.print_exc()
            raise

        self.tasks[task_type][task_id] = future
        return future
    
    def cancel_task(self, task_type: str, task_id: int):
        """取消任务"""
        task_key = f"{task_type}_{task_id}"
        
        # 设置取消标志
        if task_key in self.cancel_flags:
            self.cancel_flags[task_key] = True
        
        # 取消Future
        if task_type in self.tasks and task_id in self.tasks[task_type]:
            self.tasks[task_type][task_id].cancel()
            return True
        
        return False
    
    def is_task_cancelled(self, task_type: str, task_id: int):
        """检查任务是否被取消"""
        task_key = f"{task_type}_{task_id}"
        return task_key in self.cancel_flags and self.cancel_flags[task_key]
    
    def cleanup_task(self, task_type: str, task_id: int):
        """清理任务资源"""
        if task_type in self.tasks and task_id in self.tasks[task_type]:
            del self.tasks[task_type][task_id]
        
        task_key = f"{task_type}_{task_id}"
        if task_key in self.cancel_flags:
            del self.cancel_flags[task_key]

    @classmethod
    def is_task_cancelled_for_subprocess(cls, task_type: str, task_id: int, cancel_flags: dict[int, bool]):
        """检查任务是否被取消，供子进程调用"""
        task_key = f"{task_type}_{task_id}"
        return task_key in cancel_flags and cancel_flags[task_key]

