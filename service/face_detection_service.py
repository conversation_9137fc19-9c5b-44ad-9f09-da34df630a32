
from sqlmodel import Session, select

from orm.face_detection_orm import FacDetTask, FacDetTaskStep
from orm.step_rom import StepTypeEnum


class FacDetTaskService:
    def __init__(self, session: Session):
        self.session = session

    def add_fac_det_task(self, infer_task: FacDetTask) -> FacDetTask:
        self.session.add(infer_task)
        self.session.commit()
        self.session.refresh(infer_task)
        return infer_task
    
    def get_fac_det_task(self, task_id: int) -> FacDetTask:
        return self.session.get(FacDetTask, task_id)
    
    def get_fac_det_tasks(self, offset: int, limit: int) -> list[FacDetTask]:
        return self.session.exec(select(FacDetTask).offset(offset).limit(limit)).all()
    
    def delete_fac_det_task(self, task_id: int) -> FacDetTask:
        infer_task = self.get_fac_det_task(task_id)
        if infer_task:
            self.delete_fac_det_task_step_by_model_id(task_id)
            self.session.delete(infer_task)
            self.session.commit()
            return infer_task
        return None
    

    def add_fac_det_task_step(self, task_step: FacDetTaskStep) -> FacDetTaskStep:
        self.session.add(task_step)
        self.session.commit()
        self.session.refresh(task_step)
        return task_step
    
    def get_fac_det_task_step(self, step_id: int):
        return self.session.get(FacDetTaskStep, step_id)
    
    def delete_fac_det_task_step(self, step_id: int) -> FacDetTaskStep:
        step = self.get_fac_det_task_step(step_id)
        if step:
            self.session.delete(step)
            self.session.commit()
            return step
        return None
    
    def delete_fac_det_task_step_by_model_id(self, task_id: int):
        task = self.get_fac_det_task(task_id)
        if task:
            for step in task.steps:
                self.session.delete(step)
            self.session.commit()
    
    def update_fac_det_task_step_status(self, task_id: int, step_type: StepTypeEnum, status: str) -> FacDetTaskStep:
        task = self.get_fac_det_task(task_id)
        if task:
            step_id = task.get_step_id_by_type(step_type)
            if step_id:
                step = self.get_fac_det_task_step(step_id)
                if step:
                    step.update_status(status)
                    self.session.add(step)
                    self.session.commit()
                    self.session.refresh(step)
                    return step
        return None