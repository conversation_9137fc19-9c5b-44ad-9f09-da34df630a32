import json
from typing import <PERSON><PERSON>
from exceptiongroup import catch
from pymilvus import MilvusClient

from orm.media_orm import MediaExtend, MediaTypeEnum
from orm.vector_task_orm import VectorCollectionEnum, VectorExtendInfo, VectorMedia, VectorQueryResult, VectorSearchGroupEnum, VectorSearchResult
from utils.logger import get_logger

logger = get_logger()

class MilvusService:
    def __init__(self, client: MilvusClient):
        self.client = client

    def add_image_vector(self, collection: str, media: VectorMedia, extend_info: VectorExtendInfo, vector: list, flush: bool = False):
        media_extend_dict = dict()
        if media.media_extend != "":
            try:
                media_extend_dict = json.loads(media.media_extend)
            except Exception as e:
                logger.debug(f"media_extend parse json error: {media.media_extend}")
        insert_result = self.client.insert(
            collection_name=collection,
            data={
                "media_data_id": media.media_data_id,
                "media_name": media.media_name,
                "media_type": media.media_type,
                "media_extend": media_extend_dict,
                "media_url": media.media_url,
                "extend_json": extend_info.model_dump(mode="json"),
                "vector": vector
            }
        )
        if flush:
            self.client.flush(collection_name=collection)            
        return insert_result

    def search_image_vector(self, media_types: list[MediaTypeEnum], media_data_ids: list[str], collection: str, vector: list, 
                            group_by_field: VectorSearchGroupEnum = VectorSearchGroupEnum.NONE, group_size: int = 1,
                            query_filter: str = '', threshold = 0.3, offset: int = 0, limit: int = 20) -> Tuple[int, list[VectorSearchResult]]:
        filter_parts = []
        if media_types:
            media_type_str = ", ".join([f'"{x.value}"' for x in media_types])
            filter_parts.append(f'media_type in [{media_type_str}]')
        if media_data_ids:
            ids_str = ", ".join([f'"{x}"' for x in media_data_ids])
            filter_parts.append(f'media_data_id in [{ids_str}]')
        if query_filter:
            filter_parts.append(f'({query_filter})')
        final_filter = " and ".join(filter_parts)

        if threshold > 1.0 or threshold < -1.0:
            threshold = 0.3

        search_params = {
            "metric_type": "COSINE",
            "range_filter": 1.0,
        }

        group_by_filed_str = ""
        if group_by_field != VectorSearchGroupEnum.NONE:
            group_by_filed_str = group_by_field.value
        else:
            # radius can not use with group_by_field
            search_params["radius"] = threshold

        total_count = -1
        # TODO: 启用分组查询后，每次查询时间过长，故暂时禁用结果总数统计的查询，降低接口返回时间
        if offset == 0 and group_by_field == VectorSearchGroupEnum.NONE:
            search_params["offset"] = 0
            total_results = self.client.search(
                collection_name=collection,
                data=[vector],
                filter=final_filter,
                limit=10000,
                group_by_field=group_by_filed_str,
                group_size=group_size,
                search_params=search_params
            )
            hits = total_results[0]
            filtered_hits = [hit for hit in hits if hit["distance"] >= threshold]
            total_count = len(filtered_hits)
            logger.info(f"search_image_vector: {total_count} results found")
        
        search_params["offset"] = offset
        results = self.client.search(
            collection_name=collection,
            data=[vector],
            filter=final_filter,
            limit=limit,
            group_by_field=group_by_filed_str,
            group_size=group_size,
            output_fields=["media_data_id", "media_name", "media_type", "media_extend", "media_url", "extend_json"],
            search_params=search_params
        )
        search_results = []
        for result in results:
            for hit in result:
                # if use group_by_field, radius will disable, so we need to filter by distance
                if group_by_field != VectorSearchGroupEnum.NONE:
                    if hit["distance"] < threshold:
                        continue
                extend_info_dic = hit["entity"]["extend_json"]
                try:
                    extend_info = VectorExtendInfo.model_validate(extend_info_dic)
                except Exception as e:
                    print(f"vector search result extend_json parse error: {extend_info_dic}")
                    continue
                media_extend_dic = hit["entity"]["media_extend"]
                try:
                    media_extend_str = json.dumps(media_extend_dic)
                except Exception as e:
                    print(f"vector search result media_extend parse error: {media_extend_dic}")
                    continue
                image_name = extend_info.frame_name.split('.')[0]  # 去掉扩展名
                search_results.append(VectorSearchResult(
                    distance=hit["distance"],
                    media_data_id=hit["entity"]["media_data_id"],
                    media_name=hit["entity"]["media_name"],
                    media_type=hit["entity"]["media_type"],
                    media_extend=media_extend_str,
                    media_url=hit["entity"]["media_url"],
                    vector_task_id=extend_info.vector_task_id,
                    frame_index=int(image_name.split('_')[1]),
                    frame_pts=int(image_name.split('_')[2]),
                    crop_path=extend_info.crop_path,
                    bbox=extend_info.bbox,
                    kps=extend_info.kps,
                    class_index=extend_info.class_index,
                    class_id=extend_info.class_id,
                    class_name=extend_info.class_name,
                    score=extend_info.score,
                    rec_score=extend_info.rec_score
                ))

        return total_count, search_results

    def query_image_vector(self, media_types: list[MediaTypeEnum], media_data_ids: list[str], collection: str, 
                            query_filter: str = '', limit: int = 100) -> Tuple[int, list[VectorQueryResult]]:
        filter_parts = []
        if media_types:
            media_type_str = ", ".join([f'"{x.value}"' for x in media_types])
            filter_parts.append(f'media_type in [{media_type_str}]')
        if media_data_ids:
            ids_str = ", ".join([f'"{x}"' for x in media_data_ids])
            filter_parts.append(f'media_data_id in [{ids_str}]')
        if query_filter:
            filter_parts.append(f'({query_filter})')
        final_filter = " and ".join(filter_parts)

        total_count = -1
        results = self.client.query(
            collection_name=collection,
            filter=final_filter,
            limit=limit,
            output_fields=["vector", "media_data_id", "media_name", "media_type", "media_extend", "media_url", "extend_json"],
        )
        query_results = []
        for result in results:
            extend_info_dict = result["extend_json"]
            try:
                extend_info = VectorExtendInfo.model_validate(extend_info_dict)
            except Exception as e:
                print(f"vector query result extend_json parse error: {extend_info_dict}")
                continue
                # extend_info = VectorExtendInfo()
            media_extend_dict = result["media_extend"]
            try:
                media_extend_str = json.dumps(media_extend_dict)
            except Exception as e:
                print(f"vector query result media_extend parse error: {media_extend_dict}")
                continue
            image_name = extend_info.frame_name.split('.')[0]  # 去掉扩展名
            if image_name == '':
                image_name = 'frame_00000000_00000000'
            query_results.append(VectorQueryResult(
                vector=result["vector"],
                # distance=hit["distance"],
                distance=0.0,
                media_data_id=result["media_data_id"],
                media_name=result["media_name"],
                media_type=result["media_type"],
                media_extend=media_extend_str,
                media_url=result["media_url"],
                vector_task_id=extend_info.vector_task_id,
                frame_index=int(image_name.split('_')[1]),
                frame_pts=int(image_name.split('_')[2]),
                crop_path=extend_info.crop_path,
                bbox=extend_info.bbox,
                kps=extend_info.kps,
                class_index=extend_info.class_index,
                class_id=extend_info.class_id,
                class_name=extend_info.class_name,
                score=extend_info.score,
                rec_score=extend_info.rec_score
            ))

        return total_count, query_results

    def delete_image_vector(self, media_types: list[MediaTypeEnum], media_data_ids: list[str], collection: str, 
                            query_filter: str = '', flush: bool = False) -> int:
        filter_parts = []
        if media_types:
            media_type_str = ", ".join([f'"{x.value}"' for x in media_types])
            filter_parts.append(f'media_type in [{media_type_str}]')
        if media_data_ids:
            ids_str = ", ".join([f'"{x}"' for x in media_data_ids])
            filter_parts.append(f'media_data_id in [{ids_str}]')
        if query_filter:
            filter_parts.append(f'({query_filter})')
        final_filter = " and ".join(filter_parts)

        total_count = -1
        result = self.client.delete(
            collection_name=collection,
            filter=final_filter,
        )
        total_count = result['delete_count']

        if flush:
            self.client.flush(collection_name=collection)

        return total_count