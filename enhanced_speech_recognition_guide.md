# 完善的语音识别系统使用指南

## 概述

经过完善的语音识别系统现在具备以下核心功能：

- ✅ **智能音频分块**：支持长音频文件，自动分块处理
- ✅ **重叠区域处理**：避免分块边界的识别丢失
- ✅ **时间戳精确对齐**：每个字符都有准确的时间戳
- ✅ **句子边界检测**：智能分割句子
- ✅ **结果去重合并**：处理重叠区域的重复内容

## 核心改进

### 1. 音频分块策略

```python
# 新的分块策略
chunk_size = 30 * 16000  # 30秒块
step_size = 25 * 16000   # 25秒步长，5秒重叠
overlap_size = chunk_size - step_size  # 5秒重叠区域
```

**优势：**
- 30秒块大小确保足够的上下文信息
- 5秒重叠避免句子在边界被截断
- 详细的块信息追踪（开始时间、结束时间、索引等）

### 2. 时间戳处理

每个识别结果现在包含：
```python
{
    "preds": "识别的文本",
    "timestamp": [
        [开始时间, 结束时间],  # 每个字符的时间戳
        # ...
    ],
    "raw_tokens": ["字", "符", "列", "表"],  # 字符内容在这里
    # 处理过程中的临时信息（最终输出会移除）
    "chunk_index": 块索引,
    "chunk_start_time": 块开始时间,
    "chunk_end_time": 块结束时间
}
```

**重要说明**：
- `timestamp` 只包含时间信息：`[[开始时间, 结束时间], ...]`
- `raw_tokens` 包含对应的字符内容：`["字", "符", ...]`
- 两个数组的长度相同，索引一一对应

### 3. 重叠区域处理

- **第一个块**：完整保留所有内容
- **后续块**：只保留非重叠区域的内容
- **智能去重**：基于时间戳和重叠标记自动去除重复

### 4. 句子边界检测

支持多种分割条件：
- **标点符号**：`。！？.!?；;`
- **长时间停顿**：超过1秒的静音间隔
- **长度限制**：超过100个字符自动分割

## 使用方法

### 基本使用

```python
from onnx_sessions.speech_recognition.paraformer_onnx import run_paraformer_asr_inference

# 基本调用
result_path = run_paraformer_asr_inference(
    model_dir="/path/to/paraformer/model",
    audio_file="long_audio.wav",
    output_csv="results.csv",
    device_id=-1,  # CPU推理
    progress_callback=lambda p, m: print(f"[{p*100:.1f}%] {m}")
)
```

### 高级使用

```python
from onnx_sessions.speech_recognition.paraformer_onnx import Paraformer

# 初始化模型
model = Paraformer(
    model_dir="/path/to/model",
    device_id=-1,
    quantize=True
)

# 直接推理
asr_results, waveform = model.inference("audio.wav")

# 处理为句子
sentences = model.process_results_to_sentences(asr_results)

# 保存结果
output_path = model.save_results_to_csv(sentences, "output.csv")
```

## 输出格式

### CSV文件格式
```csv
sentence_id,text,start_time,end_time,duration
1,这是第一句话,0.000,2.350,2.350
2,这是第二句话内容比较长,2.500,5.800,3.300
3,第三句话,6.000,7.200,1.200
```

### 程序返回格式
```python
[
    {
        "preds": "这是第一句话",
        "timestamp": [
            [0.0, 0.1],  # "这"的时间戳
            [0.1, 0.2],  # "是"的时间戳
            [0.2, 0.3],  # "第"的时间戳
            [0.3, 0.4],  # "一"的时间戳
            [0.4, 0.5],  # "句"的时间戳
            [0.5, 0.6],  # "话"的时间戳
        ],
        "raw_tokens": ["这", "是", "第", "一", "句", "话"],
        "start_time": 0.0,  # 句子开始时间
        "end_time": 0.6     # 句子结束时间
    }
]
```

**数据对应关系**：
- `raw_tokens[0]` = "这" 对应 `timestamp[0]` = [0.0, 0.1]
- `raw_tokens[1]` = "是" 对应 `timestamp[1]` = [0.1, 0.2]
- 以此类推...

## 性能优化建议

### 1. 音频预处理
- 确保音频为16kHz采样率
- 使用WAV格式（支持MP3自动转换）
- 音频质量越高，识别效果越好

### 2. 硬件配置
- **CPU推理**：`device_id=-1`，适合小批量处理
- **GPU推理**：`device_id=0`，适合大批量处理
- 内存建议：至少8GB，长音频需要更多内存

### 3. 参数调优
```python
# 可调整的参数
chunk_size = 30 * 16000    # 块大小，可根据内存调整
step_size = 25 * 16000     # 步长，影响重叠程度
quantize = True            # 量化模型，减少内存占用
```

## 故障排除

### 常见问题

1. **内存不足**
   - 减小chunk_size
   - 使用量化模型
   - 分批处理长音频

2. **识别效果差**
   - 检查音频质量
   - 确认采样率为16kHz
   - 尝试不同的模型

3. **时间戳不准确**
   - 检查音频是否有静音段
   - 确认模型支持时间戳输出
   - 验证音频文件完整性

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
model = Paraformer(model_dir="/path/to/model", device_id=-1)
```

## 最佳实践

1. **批量处理**：对多个文件使用相同的模型实例
2. **进度监控**：使用progress_callback跟踪处理进度
3. **结果验证**：检查输出CSV文件的完整性
4. **备份原始音频**：保留原始文件用于问题排查

## 技术细节

### 核心算法流程

1. **音频分块**：`audio_chunk_with_info()` - 生成带元信息的音频块
2. **块级推理**：`inference_chunk()` - 对每个块进行ASR推理
3. **时间戳调整**：`adjust_timestamps_with_chunk_info()` - 添加时间偏移和重叠标记
4. **结果合并**：`merge_overlapping_results()` - 智能合并重叠区域
5. **句子分割**：`detect_sentence_boundaries()` - 检测句子边界
6. **格式化输出**：`process_results_to_sentences()` - 转换为最终格式

### 数据流图

```
原始音频 → 音频分块 → 块级推理 → 时间戳调整 → 结果合并 → 句子分割 → 最终输出
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
  60秒WAV   3个30秒块   ASR结果   绝对时间戳  去重结果   句子列表   CSV文件
```

## 更新日志

### v2.0 (当前版本)
- ✅ 完善音频分块逻辑，支持详细块信息
- ✅ 改进时间戳处理，支持重叠区域标记
- ✅ 新增智能结果合并，避免重复内容
- ✅ 增强句子边界检测，支持多种分割条件
- ✅ 优化数据结构，保持完整性

### v1.0 (之前版本)
- 基础音频分块
- 简单时间戳调整
- 基本结果合并

---

**注意**：此系统已经过充分测试，能够处理长音频文件并提供准确的时间戳信息。如有问题，请参考故障排除部分或联系技术支持。
